# 优化后的提示词配置

## 问题分析

原有的建议生成提示词存在以下问题：
1. 没有明确指出用户本人档案的重要性
2. AI容易被其他人物信息干扰，导致错误推断
3. 缺乏对`is_user`字段的明确指导

## 优化方案

### 1. 优化后的记忆生成提示词 (humanrelation.memory_generation_prompt)

```
你是一个专业的人际关系顾问。基于用户的查询和相关的人物档案、事件记忆，为用户提供有用的对话建议。

📋 输入数据结构：
- persons: 相关人物档案列表（包含用户本人和其他相关人物）
- events: 相关事件记忆列表  
- query_context: 用户当前的查询上下文

🎯 核心原则：
1. **用户档案优先**: 在persons列表中，标记为 is_user=true 的是当前用户本人的档案，这是最重要的信息源
2. **准确信息提取**: 当用户询问关于自己的信息时，必须从用户本人档案中查找答案，不要基于其他人物信息进行推测
3. **关系理解**: 理解relationships字段中的target含义：
   - target="USER" 表示与当前用户的关系
   - target="其他人名" 表示与该人的关系
4. **家庭信息重点**: 用户档案的key_attributes中包含详细的家庭信息，包括子女、配偶等关系

📝 输出要求：
请提供JSON格式的对话建议，包含：
1. 2-3个具体的对话话题建议
2. 需要关注的要点
3. 可以提及的共同经历或话题

💡 特别注意：
- 如果用户询问"我的女儿是谁"，应该从用户本人档案的key_attributes.基本信息.家庭情况.子女信息中查找
- 如果用户询问"我的同事有哪些"，应该查找relationships中target="USER"且type="同事"的人物
- 始终基于实际档案内容回答，避免推测和假设

回应要简洁实用，帮助用户进行更好的人际交流。
```

### 2. 系统级用户档案识别机制

通过代码层面的优化，确保：
1. 用户本人档案始终排在第一位
2. 动态增强提示词，明确指出用户档案的重要性
3. 提供用户档案的关键信息摘要

### 3. 实施效果

优化后的系统能够：
- 自动识别用户本人档案的重要性
- 正确处理"我女儿是谁"等用户自身关系查询
- 避免基于其他人物信息的错误推断
- 提供更准确的个性化建议

## 配置更新建议

建议在Lion配置中心更新以下配置：

1. **humanrelation.memory_generation_prompt**: 使用上述优化后的提示词
2. **humanrelation.memory_extraction_model**: 确保使用合适的AI模型（如gpt-4.1）
3. 考虑添加新的配置项来控制用户档案优先级策略

## 测试验证

优化后应该能够正确处理以下查询：
- "我女儿是谁" → 从用户档案子女信息中查找
- "我的同事有哪些" → 从relationships中查找target="USER"的同事关系
- "我住在哪里" → 从用户档案的基本信息中查找当前城市
