# 天气AI模型配置 - 快速指南

## 🎯 功能说明

天气功能现在支持单独配置专用的AI模型，用于生成个性化天气提醒。

## ⚙️ 配置项

### 核心配置（Lion配置中心）

```
# 天气专用模型（可选）
humanrelation.weather.ai.model = gpt-4o-mini

# 天气生成参数（可选）
humanrelation.weather.ai.max_tokens = 800
humanrelation.weather.ai.temperature = 0.7
```

### 配置优先级

1. **天气专用配置** → 2. **通用AI配置** → 3. **默认值**

## 🚀 快速配置

### 推荐配置1：成本优化
```
humanrelation.weather.ai.model = gpt-4o-mini
humanrelation.weather.ai.temperature = 0.7
humanrelation.weather.ai.max_tokens = 600
```

### 推荐配置2：效果优先
```
humanrelation.weather.ai.model = gpt-4o
humanrelation.weather.ai.temperature = 0.8
humanrelation.weather.ai.max_tokens = 1000
```

### 推荐配置3：使用通用配置
```
# 不配置天气专用模型，使用通用AI配置
# humanrelation.ai.model = anthropic.claude-sonnet-4
```

## 📊 参数说明

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `model` | AI模型名称 | `gpt-4o-mini` (成本低) / `gpt-4o` (效果好) |
| `max_tokens` | 最大生成长度 | `600-1000` |
| `temperature` | 创造性程度 | `0.7-0.8` |

## ✅ 验证方法

配置后重启应用，查看日志：
```
调用天气专用AI生成提醒，模型: gpt-4o-mini, max_tokens: 800, temperature: 0.7
```

## 🔧 实现细节

代码位置：`app/service/ESmemory/es_event_service.py` 第1068-1076行

```python
# 天气专用模型配置，如果没有配置则使用通用AI模型
model_name = get_value(
    "humanrelation.weather.ai.model", 
    get_value("humanrelation.ai.model", "anthropic.claude-sonnet-4")
)

# 天气AI生成参数配置
max_tokens = int(get_value("humanrelation.weather.ai.max_tokens", "800"))
temperature = float(get_value("humanrelation.weather.ai.temperature", "0.7"))
```

## 📝 注意事项

1. **模型兼容性**：确保配置的模型在您的AI服务中可用
2. **成本控制**：不同模型成本差异较大
3. **重启生效**：配置修改后需要重启应用
4. **日志监控**：通过日志确认配置是否生效

---

**配置完成！** 🎉 天气功能将使用您指定的AI模型生成更个性化的天气提醒。
