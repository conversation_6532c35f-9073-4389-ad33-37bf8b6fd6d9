# 移除了金融数据库依赖 (tushare, yfinance, akshare, pytdx)
# 这些库导致 lxml 编译问题，且在人际关系管理系统中未使用

# Web框架和服务器
gevent
fastapi
uvicorn
websockets
pydantic

# 核心依赖
openai
loguru
requests
cat
python-cat

# 美团内部RPC和工具
octo-rpc==0.4.7

# 数据处理和解析
beautifulsoup4==4.12.2

# 搜索引擎客户端
opensearch-py  # 用于ES/OpenSearch连接
zebraproxyclient==0.0.12
sqlalchemy==2.0.37
pymysql==1.1.1 #MySQL数据库连接驱动
cryptography==43.0.3 #MySQL连接加密支持
DBUtils==3.1.0 #数据库连接池工具

# langchain & langgraph 全家桶
langgraph
langchain
langchain_openai
langchain_community
langchain_core
langchain_anthropic
langchain-google-community  # 保留：支持Google搜索扩展功能
# langchain-tavily  # 未使用Tavily搜索，已禁用
elasticsearch

# 美团内部依赖，S3plus
kms-client-sdk
mssapi-mt

# RAG检索相关依赖
scikit-learn  # 保留：master分支RAG优化需要
numpy  # 保留：其他库可能需要
jieba  # 保留：中文分词，体积小

# 性能优化相关依赖
psutil  # 系统信息获取，用于动态线程池调整

# -- PyTorch Configuration -- 已禁用以提升构建速度
# --extra-index-url https://download.pytorch.org/whl/cpu
# torch  # 已禁用：sentence-transformers依赖，现已注释


# 代码质量工具
black
isort
flake8
autoflake

