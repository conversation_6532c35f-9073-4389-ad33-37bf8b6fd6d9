# 线上数据迁移使用指南

## 📋 概述

`production_data_migration.py` 脚本用于将线上现有的旧格式人员数据迁移到新的标准化格式，支持双向关系处理功能。

**⚠️ 重要提醒**：这是线上数据迁移脚本，请务必先在测试环境验证后再在生产环境使用！

## 🚀 快速开始

### 第一步：检查数据状态
```bash
python3 production_data_migration.py --check-only
```

### 第二步：试运行（安全预览）
```bash
python3 production_data_migration.py
```

### 第三步：正式执行（确认无误后）
```bash
python3 production_data_migration.py --execute
```

### 第四步：如需回滚
```bash
python3 production_data_migration.py --rollback production_backup_YYYYMMDD_HHMMSS.json
```

## 📖 命令详解

| 命令 | 说明 | 安全性 |
|------|------|--------|
| `--check-only` | 仅检查数据格式分布，不执行任何迁移 | 🟢 完全安全 |
| 无参数（默认） | 试运行模式，模拟完整迁移流程但不修改数据 | 🟢 完全安全 |
| `--execute` | 正式执行迁移，会修改线上数据 | 🔴 需要确认 |
| `--rollback` | 回滚到迁移前状态 | 🟡 需要确认 |

## 🔍 执行示例

### 1. 数据检查
```bash
$ python3 production_data_migration.py --check-only

📊 数据检查结果:
   总人员记录: 150
   旧格式记录: 120  ← 需要迁移
   新格式记录: 30   ← 已是新格式
```

### 2. 试运行
```bash
$ python3 production_data_migration.py

🚀 开始线上数据迁移 (试运行)
============================================================
🔍 执行迁移前安全检查...
✅ 数据库连接正常
✅ 数据表检查通过
✅ 磁盘空间充足: 15.23GB
📊 找到 150 条人员记录

📦 处理批次 1/2 (100 条记录)
🔄 处理 1/150: 张三 (uuid-123)
🔄 处理 2/150: 李四 (uuid-456)
...

📈 迁移统计:
   总记录数: 150
   成功迁移: 120
   失败记录: 0
   关系标准化: 85
   属性标准化: 120

📝 迁移日志已保存: production_migration_log_20250717_143022.json
💾 备份数据已保存: production_backup_20250717_143022.json

💡 这是试运行模式，没有实际修改数据
   如需正式执行，请使用: python3 production_data_migration.py --execute
```

### 3. 正式执行
```bash
$ python3 production_data_migration.py --execute

⚠️ 警告：即将正式执行数据迁移！
   这将修改线上数据，请确保已经:
   1. 在测试环境验证过迁移脚本
   2. 通知相关人员
   3. 准备好回滚方案

请输入 'CONFIRM_PRODUCTION_MIGRATION' 确认继续: CONFIRM_PRODUCTION_MIGRATION

🚀 开始线上数据迁移 (正式执行)
... (执行过程同试运行)
✅ 数据迁移完成！
```

### 4. 回滚操作
```bash
$ python3 production_data_migration.py --rollback production_backup_20250717_143022.json

🔄 开始回滚迁移，使用备份文件: production_backup_20250717_143022.json
📊 找到 120 条备份记录
⚠️ 确认要回滚数据吗？输入 'CONFIRM_ROLLBACK' 确认: CONFIRM_ROLLBACK

✅ 回滚成功: uuid-123
✅ 回滚成功: uuid-456
...

📈 回滚统计: 成功 120, 失败 0
```

## 🛡️ 安全机制

### 自动安全检查
- ✅ 数据库连接测试
- ✅ 必要表结构检查
- ✅ 磁盘空间检查（至少1GB）
- ✅ 数据完整性验证

### 数据保护
- 📁 **自动备份**：每次迁移前自动备份原始数据
- 🔄 **一键回滚**：支持完全回滚到迁移前状态
- 📝 **详细日志**：记录每个操作的详细信息
- 🛡️ **试运行模式**：默认不修改数据，确保安全

### 确认机制
- 正式执行需要输入：`CONFIRM_PRODUCTION_MIGRATION`
- 回滚操作需要输入：`CONFIRM_ROLLBACK`

## 📁 生成文件

迁移过程会生成以下文件：

1. **迁移日志**：`production_migration_log_YYYYMMDD_HHMMSS.json`
   - 包含每个操作的详细记录
   - 统计信息和错误信息

2. **备份数据**：`production_backup_YYYYMMDD_HHMMSS.json`
   - 原始数据的完整备份
   - 用于回滚操作

## ⚡ 性能特点

- **批量处理**：每批处理100条记录，避免内存问题
- **失败监控**：失败率超过10%时会提示检查
- **进度显示**：实时显示处理进度
- **资源管理**：自动管理数据库连接和文件资源

## 🚨 注意事项

### 执行前准备
1. **测试验证**：在测试环境先验证脚本
2. **通知相关人员**：提前通知可能受影响的用户
3. **选择合适时间**：建议在业务低峰期执行
4. **确保备份**：确保有完整的数据库备份

### 执行环境
- 确保在应用根目录执行脚本
- 确保有足够的磁盘空间（至少1GB）
- 确保数据库连接正常
- 确保有相应的数据库操作权限

## 🆘 故障排除

### 常见问题

**Q: 脚本提示找不到app目录？**
A: 确保在应用根目录执行脚本，或者将脚本放在与app目录同级的位置。

**Q: 数据库连接失败？**
A: 检查数据库配置和网络连接，确保有相应的操作权限。

**Q: 迁移过程中断了怎么办？**
A: 如果是试运行模式，没有影响。如果是正式执行，可以使用备份文件回滚，然后重新执行。

**Q: 如何验证迁移是否成功？**
A: 使用 `--check-only` 参数检查数据格式分布，确认旧格式记录数为0。

### 紧急回滚
如果迁移后发现严重问题：
```bash
python3 production_data_migration.py --rollback production_backup_YYYYMMDD_HHMMSS.json
```

---

**📞 技术支持**：如遇到问题，请提供迁移日志文件和错误信息截图。
