#!/bin/bash
# 构建优化脚本

echo "🚀 开始构建优化..."

# 1. 清理Python缓存
echo "🧹 清理Python缓存..."
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyo" -delete

# 2. 清理备份文件
echo "🗑️ 清理备份文件..."
rm -f person_memory_backup_*.json
rm -f migration_log_*.json

# 3. 清理临时文件
echo "🧽 清理临时文件..."
rm -f *.log
rm -f *.tmp
rm -f .DS_Store
find . -name ".DS_Store" -delete

# 4. 检查大文件
echo "📊 检查项目大小..."
echo "总大小: $(du -sh . | cut -f1)"
echo "最大文件:"
find . -type f -size +1M -exec ls -lh {} \; | awk '{print $5 " " $9}' | sort -hr | head -10

# 5. 创建.dockerignore（如果不存在）
if [ ! -f .dockerignore ]; then
    echo "📝 创建.dockerignore..."
    cat > .dockerignore << EOF
# Python缓存
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 测试和覆盖率
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日志
*.log

# 数据库
*.db
*.sqlite3

# 备份文件
*.backup
*_backup_*.json
migration_log_*.json

# 大数据文件
data/
*.csv
*.json.gz

# Git
.git/
.gitignore

# 文档
*.md
docs/

# 测试文件
test_*.py
*_test.py
tests/

# 临时文件
*.tmp
*.temp
.DS_Store
EOF
fi

echo "✅ 构建优化完成！"
echo "💡 建议："
echo "   1. 使用多阶段Docker构建"
echo "   2. 配置Docker层缓存"
echo "   3. 使用更快的PyPI镜像源"
echo "   4. 考虑使用Alpine Linux基础镜像"
