# 小美人际关系AI对话流程说明

## 🎯 概述

本系统基于LangGraph框架构建了一个智能对话引擎，结合了记忆管理、意图分析、新闻检索等功能，为用户提供个性化的人际关系咨询服务。

## 📋 系统架构

```mermaid
graph TD
    A[用户输入] --> B[API接口层]
    B --> C[LangGraph工作流]
    C --> D[记忆处理节点]
    C --> E[AI模型节点]
    C --> F[工具调用节点]
    D --> G[MySQL人员数据]
    D --> H[ES事件数据]
    D --> I[新闻库]
    E --> J[OpenAI/其他AI模型]
    F --> K[各种工具集]
```

## 🔄 完整对话流程

### 1. API接口层

#### 1.1 流式接口 `/humanrelation/chat`

- **用途**: 实时流式对话，适用于在线聊天
- **响应**: Server-Sent Events (SSE) 流式响应
- **特点**: 实时性强，用户体验好

#### 1.2 非流式接口 `/humanrelation/chat_json`

- **用途**: 批量处理，返回完整响应和推荐问题
- **响应**: JSON格式完整响应
- **特点**: 包含AI响应和推荐问题列表

```json
{
  "response": "AI的完整回复内容",
  "status": "success",
  "recommended_questions": [
    "相关推荐问题1",
    "相关推荐问题2"
  ]
}
```

### 2. LangGraph工作流

#### 工作流节点结构

```python
# 节点定义
workflow.add_node("memory_processor", process_memory_and_context)  # 记忆处理
workflow.add_node("agent", call_model)                            # AI模型调用
workflow.add_node("tools", custom_tool_node)                      # 工具执行

# 流程定义
workflow.set_entry_point("memory_processor")
workflow.add_edge("memory_processor", "agent")
workflow.add_conditional_edges("agent", custom_tools_condition, {"tools": "tools", END: END})
workflow.add_edge("tools", "agent")
```

#### 流程图

```mermaid
graph LR
    START --> A[memory_processor]
    A --> B[agent]
    B --> C{需要工具调用?}
    C -->|是| D[tools]
    C -->|否| END
    D --> B
```

### 3. 记忆处理节点 (memory_processor)

这是整个对话流程的核心预处理节点，负责：

#### 3.1 记忆提取与意图识别

```python
# 核心流程
memory_result = memory_service.extract_and_process_memory(contextual_memory_input, user_id)

# 意图类型
- add_memory: 添加新的记忆信息
- query: 查询相关记忆
- merge_persons: 合并人物档案
- alias_relation: 处理别名关系
- rename_relation: 重命名关系
```

#### 3.2 查询意图处理 - 智能检索系统

当识别到查询意图时，系统会启动三层智能检索策略：

##### 📊 查询类型分析

```python
# AI驱动的意图分析
intent_result = self._analyze_query_intent(query_text)

# 三种查询类型
A类：直接属性查询
- 示例: "张三的电话是多少？"
- 策略: MySQL精确查询

B类：反向属性查询
- 示例: "我女儿是谁？"、"谁是程序员？"
- 策略: MySQL属性检索 + ES语义检索

C类：开放式联想查询
- 示例: "最近有什么重要的事情？"
- 策略: ES事件检索 + MySQL人物检索综合分析
```

##### 🔍 检索策略详解

**A类查询流程：**

1. 提取目标人物名称
2. 在MySQL人员表中精确查找
3. 返回具体属性信息

**B类查询流程：**

1. 提取搜索属性（如"女儿"、"程序员"）
2. MySQL中搜索relationships和key_attributes字段
3. ES中搜索人物档案记忆
4. 合并去重结果

**C类查询流程：**

1. ES中检索相关事件
2. 从事件中提取参与者
3. MySQL中查找参与者详细档案
4. 综合分析返回结果

#### 3.3 新闻检索增强 📰

```python
# RAG增强流程
news_result = _search_relevant_news(user_input)

# 检索结果包含
- title: 新闻标题
- content: 新闻内容摘要
- full_content: 完整内容
- similarity: 相关度分数
- tags: 标签列表
- ai_judgment: AI判断结果
```

#### 3.4 上下文构建

系统会构建包含以下信息的完整上下文：

```python
final_prompt = f"""system_prompt: {system_prompt}

chathistory:
{chat_history_str}

context:
记忆信息: {persons_and_events}
相关新闻: {news_summary}
建议回复: {suggestions}

ask: {user_input}"""
```

### 4. AI模型节点 (agent)

#### 4.1 模型配置

- 支持多种AI模型：OpenAI、Anthropic等
- 可配置温度、流式输出等参数
- 智能消息序列管理和长度控制

#### 4.2 智能调用策略

**首次调用：**

- 使用增强的memory_context
- 包含完整的上下文信息
- 支持prompt长度优化

**工具调用后的二次调用：**

- 保持完整消息历史
- 维护工具调用上下文
- 避免破坏工具调用链

#### 4.3 消息格式验证

- 自动修复消息序列格式
- 处理工具调用ID长度问题
- 确保所有tool_calls都有响应

### 5. 工具调用节点 (tools)

#### 5.1 可用工具集

| 工具类别 | 工具名称 | 功能描述 |
|---------|----------|----------|
| **信息查询** | `get_data_from_xhc` | 旅游、美食、景点信息查询 |
| **通讯工具** | `get_user_phone_tool` | 获取用户电话信息 |
| **天气服务** | `get_smart_weather_info` | 智能天气信息获取 |
| **提醒管理** | `add_reminder_tool` | 添加提醒 |
|           | `edit_reminder_tool` | 编辑提醒 |
|           | `delete_reminder_tool` | 删除提醒 |
|           | `list_reminders_tool` | 列出提醒 |
| **网络搜索** | `bing_search`/`google_search` | 网络信息搜索 |
| **计算工具** | `calculator_plus` | 数学计算 |

#### 5.2 工具调用流程

1. AI模型识别需要使用的工具
2. 生成工具调用请求
3. 工具节点执行具体操作
4. 返回执行结果
5. AI模型基于结果生成最终回复

### 6. 数据存储系统

#### 6.1 MySQL存储 (结构化数据)

```sql
-- 人员记忆表
person_memory:
- person_id: 人员唯一标识
- canonical_name: 正式姓名
- relationships: 人际关系JSON
- profile_summary: 个人简介
- key_attributes: 关键属性JSON
- intimacy_score: 亲密度分数
```

#### 6.2 Elasticsearch存储 (非结构化数据)

```json
// 事件索引结构
memory_event_store:
{
  "memory_type": "event",
  "event_id": "事件ID",
  "description_text": "事件描述",
  "participants": ["参与者列表"],
  "location": "地点",
  "topics": ["话题标签"],
  "sentiment": "情感分析结果"
}
```

#### 6.3 新闻知识库

- 标题、内容、标签信息
- 向量嵌入支持语义搜索
- AI判断和关键词生成

### 7. 对话状态管理

#### 7.1 检查点机制

- 基于MySQL的对话状态持久化
- 支持多轮对话上下文
- 异常状态自动检测与修复

#### 7.2 状态验证

```python
# 状态健康检查
- 检测未响应的tool_calls
- 验证消息序列完整性
- 自动清理错误状态
- 支持强制重置机制
```

## 🔧 配置管理

### Lion配置中心集成

所有关键配置通过Lion配置中心管理：

```python
# 主要配置项
- humanrelation.memory_extraction_model: 记忆提取模型
- humanrelation.query_intent_analysis_prompt: 查询意图分析提示词
- humanrelation.event_index_name: ES事件索引名称
- humanrelation.enable_recommendations: 是否启用推荐问题
- xiaomei.humanrelation.model_config: AI模型配置
```

## 📊 性能优化

### 1. 并发处理

- 异步流式响应
- 多线程后台任务
- 连接池管理

### 2. 缓存策略

- 检查点状态缓存
- 配置信息缓存
- 查询结果缓存

### 3. 资源管理

- 智能消息截断
- 内存使用监控
- 数据库连接优化

## 🚨 异常处理

### 1. 错误恢复机制

- AI调用失败自动重试
- 工具执行异常处理
- 数据库连接断开恢复

### 2. 状态修复

- 检查点状态验证
- 消息序列自动修复
- 强制重置问题会话

### 3. 日志监控

- 详细的执行日志
- 性能指标记录
- 异常告警机制

## 🎯 使用示例

### 典型对话场景

**场景1：信息查询**

```
用户: "我女儿是谁？"
系统: 意图分析 → B类查询 → MySQL关系查询 → 返回女儿信息
```

**场景2：事件回忆**

```
用户: "最近有什么重要的事？"
系统: 意图分析 → C类查询 → ES事件检索 → 整合回复
```

**场景3：工具调用**

```
用户: "明天天气怎么样？"
系统: 意图分析 → 调用天气工具 → 返回天气信息
```

**场景4：记忆添加**

```
用户: "我今天见了张三，他是我的新同事"
系统: 记忆提取 → 创建/更新人物档案 → 添加关系信息
```

## 🔄 流程总结

1. **输入处理**: API接收用户请求，初始化对话状态
2. **记忆处理**: 分析意图，检索相关记忆，获取新闻信息
3. **上下文构建**: 整合所有信息，构建完整上下文
4. **AI推理**: 基于上下文生成回复或工具调用
5. **工具执行**: 如需要，执行相应工具操作
6. **结果返回**: 生成最终回复，更新对话状态
7. **状态保存**: 持久化对话状态，准备下次交互

这个流程确保了系统能够：

- 🧠 智能理解用户意图
- 📚 有效利用历史记忆
- 🔍 获取最新相关信息
- 🛠️ 调用外部工具服务
- 💬 生成个性化回复
- 🔄 维护连续对话体验
