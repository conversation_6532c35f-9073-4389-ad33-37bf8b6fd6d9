#!/usr/bin/env python3
"""
修复李四的关系数据错误
问题：李四的关系中有 {"type": "女儿", "target": "李佳佳"}，这是错误的
应该改为：{"type": "父亲", "target": "李佳佳"}
"""

import os
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

import json

import service.mysql_person_service as person_service


def fix_lisi_relationships():
    """修复李四的关系数据"""
    try:
        # 李四的person_id
        lisi_person_id = "1dcc205f-e390-4796-a237-682429305aad"
        user_id = "zhangzheng51"

        print("🔍 开始修复李四的关系数据...")

        # 查询李四的当前数据
        persons = person_service.search_persons_by_name(user_id, "李四", limit=10)
        lisi_data = None

        for person in persons:
            if person.get("person_id") == lisi_person_id:
                lisi_data = person
                break

        if not lisi_data:
            print("❌ 未找到李四的数据")
            return False

        print(f"📋 李四当前的关系数据：")
        current_relationships = lisi_data.get("relationships", [])
        for i, rel in enumerate(current_relationships):
            print(f"  {i+1}. {rel}")

        # 修复关系数据
        fixed_relationships = []
        for rel in current_relationships:
            if rel.get("type") == "女儿" and rel.get("target") == "李佳佳":
                # 修复错误的关系
                fixed_rel = {"type": "父亲", "target": "李佳佳"}
                fixed_relationships.append(fixed_rel)
                print(f"🔧 修复关系：{rel} -> {fixed_rel}")
            elif rel.get("type") == "朋友" and rel.get("target") == "李四":
                # 删除自己和自己是朋友的错误关系
                print(f"🗑️ 删除错误关系：{rel}")
                continue
            else:
                # 保留正确的关系
                fixed_relationships.append(rel)
                print(f"✅ 保留关系：{rel}")

        print(f"\n📋 修复后的关系数据：")
        for i, rel in enumerate(fixed_relationships):
            print(f"  {i+1}. {rel}")

        # 确认修改
        confirm = input("\n❓ 确认要应用这些修改吗？(y/N): ")
        if confirm.lower() != "y":
            print("❌ 取消修改")
            return False

        # 更新数据库
        print("💾 正在更新数据库...")

        # 构建更新数据
        update_data = {"relationships": fixed_relationships}

        # 调用更新方法
        success = person_service.update_person(user_id=user_id, person_id=lisi_person_id, **update_data)

        if success:
            print("✅ 李四的关系数据修复成功！")

            # 验证修复结果
            print("\n🔍 验证修复结果...")
            updated_persons = person_service.search_persons_by_name(user_id, "李四", limit=10)
            for person in updated_persons:
                if person.get("person_id") == lisi_person_id:
                    print("📋 修复后的关系数据：")
                    for i, rel in enumerate(person.get("relationships", [])):
                        print(f"  {i+1}. {rel}")
                    break

            return True
        else:
            print("❌ 数据库更新失败")
            return False

    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        import traceback

        traceback.print_exc()
        return False


def verify_fix():
    """验证修复效果"""
    print("\n🧪 测试修复效果...")

    # 模拟查询"我女儿是谁"
    try:
        user_id = "zhangzheng51"

        # 查询所有人物的关系
        print("📋 当前所有人物的女儿关系：")

        # 查询用户本人的子女信息
        user_persons = person_service.search_persons_by_name(user_id, "张三", limit=10)
        for person in user_persons:
            if person.get("is_user"):
                key_attrs = person.get("key_attributes", {})
                basic_info = key_attrs.get("基本信息", {})
                family_info = basic_info.get("家庭情况", {})
                children = family_info.get("子女信息", [])
                if children:
                    print(f"✅ 用户的子女：{children}")

        # 查询所有与USER有女儿关系的人
        all_persons = person_service.get_all_persons(user_id, limit=50)
        daughters = []
        for person in all_persons:
            relationships = person.get("relationships", [])
            for rel in relationships:
                if rel.get("type") == "女儿" and rel.get("target") == "USER":
                    daughters.append(person.get("canonical_name"))

        if daughters:
            print(f"✅ 与用户有女儿关系的人：{daughters}")
        else:
            print("ℹ️ 没有找到与用户有女儿关系的人")

    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")


if __name__ == "__main__":
    print("🚀 开始修复李四的关系数据...")

    if fix_lisi_relationships():
        verify_fix()
        print("\n🎉 修复完成！现在AI应该能正确回答'我女儿是谁'的问题了。")
    else:
        print("\n❌ 修复失败，请检查错误信息。")
