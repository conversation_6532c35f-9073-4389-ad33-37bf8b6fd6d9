# OpenAI Tool Calls 格式错误修复方案

## 问题描述

在运行过程中遇到以下OpenAI API错误：

```
openai.BadRequestError: Error code: 400 - {'error': {'message': "An assistant message with 'tool_calls' must be followed by tool messages responding to each 'tool_call_id'. The following tool_call_ids did not have response messages: call_nsSdIgvJdnn8yysf6XJFZKCr", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}
```

## 问题根因分析

1. **LangGraph ToolNode异常处理缺陷**：当工具执行异常时，ToolNode可能没有为失败的tool_call生成对应的ToolMessage
2. **工具异常处理不规范**：工具在异常时返回错误字符串，但不等于自动生成ToolMessage
3. **网络和超时问题**：网络超时或连接失败导致工具执行中断，无法完成ToolMessage生成
4. **并发和资源竞争**：多线程执行时的资源竞争或死锁导致某些tool_call无法完成
5. **AI服务调用失败**：依赖AI服务的工具在AI调用失败时可能无法正常完成
6. **消息格式验证不足**：在发送给OpenAI API前缺少消息序列完整性验证

## 解决方案

### 1. 添加消息序列验证和修复功能

在 `app/agents/hr.py` 的 `call_model` 函数中添加了 `_validate_and_fix_message_sequence` 函数调用：

```python
# 【新增】验证和修复消息序列格式
messages_for_model = _validate_and_fix_message_sequence(messages_for_model)
```

### 2. 实现消息序列验证和修复逻辑

新增的 `_validate_and_fix_message_sequence` 函数具有以下功能：

- **检测未响应的tool_calls**：识别所有tool_call_ids和对应的tool_response_ids
- **精确修复策略**：
  - 混合情况：只移除未响应的tool_calls，保留有响应的
  - 全部未响应：移除所有tool_calls但保留消息内容，维持对话连续性
- **双重验证机制**：修复后进行最终验证，确保结果有效
- **失败安全机制**：修复失败时返回原始消息，避免系统崩溃
- **保持消息完整性**：确保修复后的消息序列符合OpenAI API要求

### 3. 增强工具节点验证

在 `custom_tool_node` 函数中添加了 `ToolMessage` 格式验证：

```python
# 验证ToolMessage格式
if isinstance(message, ToolMessage):
    if not hasattr(message, "tool_call_id") or not message.tool_call_id:
        logger.warning("发现格式异常的ToolMessage，缺少tool_call_id")
    else:
        logger.debug(f"验证ToolMessage格式正常: {message.tool_call_id}")
```

## 修复效果

1. **防止API错误**：自动检测和修复tool_calls格式问题，避免OpenAI API拒绝请求
2. **提高系统稳定性**：即使某些工具调用失败，系统仍能正常运行
3. **保持对话连续性**：即使所有tool_calls都失败，也保留消息内容，避免对话中断
4. **精确修复**：只移除有问题的tool_calls，保留正常的工具调用
5. **双重保障**：修复后验证确保结果有效，失败时安全回退
6. **更好的错误处理**：通过详细日志记录问题，便于调试和监控
7. **向后兼容**：修复功能不影响现有的正常工具调用流程

## 测试建议

1. **正常流程测试**：验证正常的工具调用仍然工作
2. **异常情况测试**：模拟tool_call没有响应的情况
3. **混合场景测试**：部分tool_calls有响应，部分没有响应
4. **性能测试**：确保验证逻辑不影响响应速度

## 监控建议

- 监控日志中的 "发现未响应的tool_calls" 警告
- 关注 "消息序列修复完成" 和修复前后的消息数量变化
- 监控 "最终验证失败" 错误，表示修复策略需要进一步优化
- 统计消息序列修复的频率，了解工具调用的稳定性
- 关注OpenAI API调用的成功率变化

## 文件修改列表

- `app/agents/hr.py`: 添加消息序列验证和修复逻辑
- `test_message_validation.py`: 测试脚本（用于验证修复功能）
- `TOOL_CALLS_FIX_SUMMARY.md`: 本文档
