#!/usr/bin/env python3
"""
网络连接诊断工具
用于分析POST请求连接延迟的原因
"""

import time
import socket
import requests
import subprocess
import json
from urllib.parse import urlparse

def test_dns_resolution(domain):
    """测试DNS解析时间"""
    print(f"🔍 测试DNS解析: {domain}")
    start_time = time.time()
    try:
        ip = socket.gethostbyname(domain)
        dns_time = time.time() - start_time
        print(f"✅ DNS解析成功: {domain} -> {ip} ({dns_time:.3f}s)")
        return dns_time, ip
    except Exception as e:
        dns_time = time.time() - start_time
        print(f"❌ DNS解析失败: {e} ({dns_time:.3f}s)")
        return dns_time, None

def test_tcp_connection(host, port):
    """测试TCP连接时间"""
    print(f"🔌 测试TCP连接: {host}:{port}")
    start_time = time.time()
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        tcp_time = time.time() - start_time
        sock.close()
        
        if result == 0:
            print(f"✅ TCP连接成功 ({tcp_time:.3f}s)")
        else:
            print(f"❌ TCP连接失败: 错误码 {result} ({tcp_time:.3f}s)")
        return tcp_time
    except Exception as e:
        tcp_time = time.time() - start_time
        print(f"❌ TCP连接异常: {e} ({tcp_time:.3f}s)")
        return tcp_time

def test_http_request(url, method='GET', data=None):
    """测试HTTP请求各阶段耗时"""
    print(f"🌐 测试HTTP请求: {method} {url}")
    
    session = requests.Session()
    
    # 记录各阶段时间
    times = {}
    
    def record_time(name):
        times[name] = time.time()
    
    try:
        record_time('start')
        
        if method.upper() == 'POST':
            response = session.post(url, json=data, timeout=30)
        else:
            response = session.get(url, timeout=30)
            
        record_time('end')
        
        total_time = times['end'] - times['start']
        
        print(f"✅ HTTP请求成功:")
        print(f"   状态码: {response.status_code}")
        print(f"   总耗时: {total_time:.3f}s")
        print(f"   响应大小: {len(response.content)} bytes")
        
        return total_time, response.status_code
        
    except requests.exceptions.Timeout:
        print(f"⏰ HTTP请求超时 (>30s)")
        return 30.0, 'TIMEOUT'
    except Exception as e:
        total_time = time.time() - times['start']
        print(f"❌ HTTP请求失败: {e} ({total_time:.3f}s)")
        return total_time, 'ERROR'

def test_curl_timing(url):
    """使用curl测试详细的连接时序"""
    print(f"📊 使用curl测试详细时序: {url}")
    
    curl_format = """
     time_namelookup:  %{time_namelookup}s
        time_connect:  %{time_connect}s
     time_appconnect:  %{time_appconnect}s
    time_pretransfer:  %{time_pretransfer}s
       time_redirect:  %{time_redirect}s
  time_starttransfer:  %{time_starttransfer}s
          time_total:  %{time_total}s
    """
    
    try:
        cmd = [
            'curl', '-w', curl_format,
            '-o', '/dev/null',
            '-s', url
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ curl测试结果:")
            print(result.stderr)
        else:
            print(f"❌ curl测试失败: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("⏰ curl测试超时")
    except FileNotFoundError:
        print("❌ curl命令未找到，请安装curl")
    except Exception as e:
        print(f"❌ curl测试异常: {e}")

def diagnose_connection(url):
    """综合诊断连接问题"""
    print("=" * 60)
    print(f"🔧 开始诊断连接问题: {url}")
    print("=" * 60)
    
    # 解析URL
    parsed = urlparse(url)
    domain = parsed.hostname
    port = parsed.port or (443 if parsed.scheme == 'https' else 80)
    
    print(f"📋 连接信息:")
    print(f"   域名: {domain}")
    print(f"   端口: {port}")
    print(f"   协议: {parsed.scheme}")
    print()
    
    # 1. DNS解析测试
    dns_time, ip = test_dns_resolution(domain)
    print()
    
    # 2. TCP连接测试
    if ip:
        tcp_time = test_tcp_connection(ip, port)
    else:
        tcp_time = test_tcp_connection(domain, port)
    print()
    
    # 3. HTTP请求测试
    http_time, status = test_http_request(url)
    print()
    
    # 4. curl详细测试
    test_curl_timing(url)
    print()
    
    # 5. 问题分析
    print("🎯 问题分析:")
    if dns_time > 1.0:
        print(f"⚠️  DNS解析较慢 ({dns_time:.3f}s) - 建议使用DNS缓存或更换DNS服务器")
    
    if tcp_time > 2.0:
        print(f"⚠️  TCP连接较慢 ({tcp_time:.3f}s) - 可能是网络延迟或服务器负载高")
    
    if http_time > 5.0:
        print(f"⚠️  HTTP请求较慢 ({http_time:.3f}s) - 可能是服务器处理慢或网络带宽不足")
    
    if dns_time < 0.1 and tcp_time < 1.0 and http_time > 3.0:
        print("⚠️  网络连接正常但HTTP响应慢 - 可能是后端服务器处理慢")
    
    print()
    print("💡 优化建议:")
    print("1. 使用连接池复用TCP连接")
    print("2. 启用HTTP Keep-Alive")
    print("3. 使用CDN加速")
    print("4. 优化后端服务器性能")
    print("5. 考虑使用HTTP/2")

if __name__ == "__main__":
    # 你的后端URL
    backend_url = "https://your-backend-domain.com/api/endpoint"
    
    # 如果你有具体的URL，请替换上面的URL
    print("请输入你的后端URL (或直接修改脚本中的backend_url变量):")
    user_url = input().strip()
    
    if user_url:
        backend_url = user_url
    
    diagnose_connection(backend_url)
