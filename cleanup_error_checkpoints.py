#!/usr/bin/env python3
"""
清理错误的检查点数据脚本
用于解决 tool_calls 未响应导致的持久化错误状态
"""

import json
import os
import sys

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

from my_mysql.sql_client import CLIENT, execute, select_many
from sqlalchemy import text
from utils.logger import logger


def cleanup_error_checkpoints():
    """清理包含错误状态的检查点"""
    try:
        # 直接使用 CLIENT 连接执行清理
        from my_mysql.sql_client import CLIENT

        logger.info("🔍 开始清理错误检查点...")

        with CLIENT.connect() as conn:
            # 1. 查找并清理包含特定 tool_call_id 的检查点
            problematic_tool_call_ids = [
                "call_nsSdIgvJdnn8yysf6XJFZKCr",  # 之前的问题ID
                "call_M94H8jvFkb7ad86VpLS44ZND",  # 新的问题ID
            ]

            # 查找所有问题检查点
            all_problematic_checkpoints = []

            for problematic_tool_call_id in problematic_tool_call_ids:
                find_sql = text("SELECT thread_id, user_id FROM chat_history WHERE checkpoint LIKE :pattern")
                result = conn.execute(find_sql.bindparams(pattern=f"%{problematic_tool_call_id}%"))
                checkpoints = result.fetchall()

                if checkpoints:
                    logger.info(f"发现 {len(checkpoints)} 个包含 {problematic_tool_call_id} 的检查点")
                    for thread_id, user_id in checkpoints:
                        logger.info(f"  - thread_id: {thread_id}, user_id: {user_id}")
                        all_problematic_checkpoints.append((thread_id, user_id))
                else:
                    logger.info(f"未发现包含 {problematic_tool_call_id} 的检查点")

            if all_problematic_checkpoints:
                # 去重
                unique_checkpoints = list(set(all_problematic_checkpoints))
                logger.info(f"🧹 开始清理 {len(unique_checkpoints)} 个唯一的问题检查点...")

                for thread_id, user_id in unique_checkpoints:
                    delete_sql = text("DELETE FROM chat_history WHERE thread_id = :thread_id AND user_id = :user_id")
                    conn.execute(delete_sql.bindparams(thread_id=thread_id, user_id=user_id))
                    logger.info(f"已清理 thread_id: {thread_id}, user_id: {user_id}")

                conn.commit()
                logger.info(f"✅ 成功清理了 {len(unique_checkpoints)} 个问题检查点")

                # 验证清理结果
                total_remaining = 0
                for problematic_tool_call_id in problematic_tool_call_ids:
                    verify_sql = text("SELECT COUNT(*) FROM chat_history WHERE checkpoint LIKE :pattern")
                    verify_result = conn.execute(verify_sql.bindparams(pattern=f"%{problematic_tool_call_id}%"))
                    remaining_count = verify_result.fetchone()[0]
                    total_remaining += remaining_count
                    logger.info(f"验证结果: 剩余 {problematic_tool_call_id} 检查点 {remaining_count} 个")

                logger.info(f"总计剩余问题检查点: {total_remaining} 个")
                return True
            else:
                logger.info("未发现任何问题检查点")
                return False

    except Exception as e:
        logger.error(f"清理检查点时发生错误: {e}")
        import traceback

        logger.error(traceback.format_exc())
        return False


def cleanup_error_checkpoints_background():
    """后台定期清理错误检查点"""
    import threading
    import time

    def cleanup_worker():
        while True:
            try:
                # 每小时检查一次
                time.sleep(3600)
                logger.info("🔍 后台检查错误检查点...")

                success = cleanup_error_checkpoints()
                if success:
                    logger.info("🧹 后台清理了一些错误检查点")

            except Exception as e:
                logger.error(f"后台清理检查点时发生错误: {e}")

    # 启动后台线程
    cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
    cleanup_thread.start()
    logger.info("✅ 后台错误检查点清理线程已启动")


def main():
    """主函数"""
    logger.info("🚀 开始清理错误检查点...")

    success = cleanup_error_checkpoints()

    if success:
        logger.info("✅ 清理完成！建议重启应用以确保生效。")
    else:
        logger.info("ℹ️ 没有发现需要清理的检查点，或清理过程中出现错误。")


if __name__ == "__main__":
    main()
