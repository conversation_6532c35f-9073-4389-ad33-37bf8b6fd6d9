    #!/usr/bin/env python3
"""
统计项目代码行数
"""

import os
import glob

def count_lines_in_project(project_path):
    """统计项目中各种文件的行数"""
    
    # 定义要统计的文件类型
    file_patterns = {
        'Python': ['**/*.py'],
        'JavaScript': ['**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx'],
        'HTML': ['**/*.html', '**/*.htm'],
        'CSS': ['**/*.css', '**/*.scss', '**/*.sass'],
        'Markdown': ['**/*.md', '**/*.markdown'],
        'JSON': ['**/*.json'],
        'YAML': ['**/*.yml', '**/*.yaml'],
        'SQL': ['**/*.sql'],
        'Shell': ['**/*.sh', '**/*.bash'],
        'Config': ['**/*.conf', '**/*.config', '**/*.ini', '**/*.cfg']
    }
    
    results = {}
    total_lines = 0
    total_files = 0
    
    print(f"正在统计项目代码行数: {project_path}")
    print("=" * 60)
    
    for file_type, patterns in file_patterns.items():
        type_lines = 0
        type_files = 0
        
        for pattern in patterns:
            files = glob.glob(os.path.join(project_path, pattern), recursive=True)
            
            for file_path in files:
                # 跳过一些不需要统计的目录
                if any(skip in file_path for skip in ['__pycache__', '.git', 'node_modules', '.venv', 'venv']):
                    continue
                
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = len(f.readlines())
                        type_lines += lines
                        type_files += 1
                except Exception as e:
                    print(f"无法读取文件 {file_path}: {e}")
        
        if type_files > 0:
            results[file_type] = {'files': type_files, 'lines': type_lines}
            total_lines += type_lines
            total_files += type_files
    
    # 打印结果
    print(f"{'文件类型':<12} {'文件数':<8} {'行数':<10} {'平均行数':<10}")
    print("-" * 50)
    
    for file_type, data in results.items():
        avg_lines = data['lines'] // data['files'] if data['files'] > 0 else 0
        print(f"{file_type:<12} {data['files']:<8} {data['lines']:<10} {avg_lines:<10}")
    
    print("-" * 50)
    print(f"{'总计':<12} {total_files:<8} {total_lines:<10}")
    print("=" * 60)
    
    return results, total_files, total_lines

if __name__ == "__main__":
    project_path = "/Users/<USER>/Downloads/project/xiaomei-humanrelation"
    count_lines_in_project(project_path)
