# 线上数据迁移指南

## 概述

这个脚本用于将线上现有的旧格式人员数据迁移到新的标准化格式，支持双向关系处理功能。

## 功能特点

- ✅ **安全的试运行模式** - 默认不修改数据，先预览迁移效果
- ✅ **完整的数据备份** - 自动备份原始数据，支持回滚
- ✅ **智能数据转换** - 自动标准化key_attributes和relationships
- ✅ **双向关系处理** - 支持父女、夫妻等关系的双向关联
- ✅ **详细的迁移日志** - 记录每个操作的详细信息
- ✅ **安全检查机制** - 迁移前检查数据库连接、磁盘空间等
- ✅ **回滚功能** - 支持一键回滚到迁移前状态

## 使用方法

### 1. 检查数据状态

首先检查当前数据的格式分布：

```bash
# 在线上服务器执行
cd /path/to/your/app
python3 production_data_migration.py --check-only
```

输出示例：
```
📊 数据检查结果:
   总人员记录: 150
   旧格式记录: 120
   新格式记录: 30
```

### 2. 试运行迁移

在正式执行前，先进行试运行：

```bash
# 试运行模式（默认）
python3 production_data_migration.py
```

这会：
- 执行安全检查
- 模拟迁移过程
- 生成迁移日志
- **不会修改任何数据**

### 3. 正式执行迁移

确认试运行结果无误后，执行正式迁移：

```bash
# 正式执行迁移
python3 production_data_migration.py --execute
```

系统会要求你输入确认码：
```
⚠️ 警告：即将正式执行数据迁移！
   这将修改线上数据，请确保已经:
   1. 在测试环境验证过迁移脚本
   2. 通知相关人员
   3. 准备好回滚方案

请输入 'CONFIRM_PRODUCTION_MIGRATION' 确认继续:
```

### 4. 回滚迁移（如需要）

如果迁移后发现问题，可以使用备份文件回滚：

```bash
# 回滚到迁移前状态
python3 production_data_migration.py --rollback production_backup_20250717_143022.json
```

## 迁移内容

### 数据结构转换

**旧格式 → 新格式**：

```json
// 旧格式
{
  "key_attributes": {
    "关系": "朋友",
    "年龄": "30",
    "职业": "工程师"
  },
  "relationships": ["朋友", "同事"]
}

// 新格式
{
  "key_attributes": {
    "基本信息": {
      "生日": "1994年",  // 年龄自动转换
      "职业信息": {
        "职位": "工程师"
      },
      "家庭情况": {
        "子女信息": [],
        "配偶姓名": ""
      }
      // ... 完整的标准化结构
    },
    "过往历史": "关系: 朋友"
  },
  "relationships": [
    {"type": "朋友", "target": "USER"},
    {"type": "同事", "target": "USER"}
  ]
}
```

### 特殊处理

1. **年龄转换**：`"年龄": "30"` → `"生日": "1994年"`
2. **关系标准化**：字符串数组 → 对象数组
3. **属性映射**：旧字段名 → 标准字段结构
4. **双向关系**：自动建立父女、夫妻等双向关联

## 安全机制

### 迁移前检查

- ✅ 数据库连接测试
- ✅ 必要表结构检查
- ✅ 磁盘空间检查
- ✅ 权限验证

### 数据保护

- 📁 **自动备份**：每次迁移前自动备份原始数据
- 🔄 **回滚支持**：支持一键回滚到迁移前状态
- 📝 **详细日志**：记录每个操作的详细信息
- 🛡️ **试运行模式**：默认不修改数据，确保安全

### 确认机制

- 正式执行需要输入确认码：`CONFIRM_PRODUCTION_MIGRATION`
- 回滚操作需要输入确认码：`CONFIRM_ROLLBACK`

## 输出文件

迁移过程会生成以下文件：

1. **迁移日志**：`production_migration_log_YYYYMMDD_HHMMSS.json`
   - 包含每个操作的详细记录
   - 统计信息和错误信息

2. **备份数据**：`production_backup_YYYYMMDD_HHMMSS.json`
   - 原始数据的完整备份
   - 用于回滚操作

## 注意事项

### 执行前准备

1. **测试验证**：在测试环境先验证脚本
2. **通知相关人员**：提前通知可能受影响的用户
3. **选择合适时间**：建议在业务低峰期执行
4. **备份确认**：确保有完整的数据库备份

### 执行过程

1. **监控日志**：密切关注迁移过程的日志输出
2. **性能影响**：迁移过程可能对数据库性能有影响
3. **中断处理**：如需中断，可以Ctrl+C停止（试运行模式安全）

### 执行后验证

1. **功能测试**：验证双向关系处理功能是否正常
2. **数据检查**：抽查部分数据确认格式正确
3. **性能监控**：观察系统性能是否正常

## 故障排除

### 常见问题

**Q: 迁移过程中断了怎么办？**
A: 如果是试运行模式，没有影响。如果是正式执行，可以使用备份文件回滚，然后重新执行。

**Q: 部分数据迁移失败怎么办？**
A: 查看迁移日志中的错误信息，修复问题后可以重新执行迁移（已成功的数据会被跳过）。

**Q: 如何验证迁移是否成功？**
A: 使用 `--check-only` 参数检查数据格式分布，确认旧格式记录数为0。

### 紧急回滚

如果迁移后发现严重问题：

```bash
# 立即回滚
python3 production_data_migration.py --rollback production_backup_YYYYMMDD_HHMMSS.json
```

## 技术支持

如遇到问题，请提供：
1. 迁移日志文件
2. 错误信息截图
3. 数据量统计信息

---

**重要提醒**：线上数据迁移是高风险操作，请务必在测试环境充分验证后再执行！
