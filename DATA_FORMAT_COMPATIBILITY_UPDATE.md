# 数据格式兼容性更新说明

## 更新背景

用户档案的 `key_attributes` 数据格式已经从扁平结构升级为标准化的嵌套结构。为了确保现有功能的正常运行，我们对相关代码进行了兼容性更新。

## 新旧数据格式对比

### 旧格式（扁平结构）
```json
{
  "key_attributes": {
    "工作地点": "深圳",
    "生活地点": "广州", 
    "手机号": "13987654321",
    "年龄": "30",
    "职业": "工程师"
  }
}
```

### 新格式（标准化嵌套结构）
```json
{
  "key_attributes": {
    "基本信息": {
      "家乡": "北京",
      "当前城市": "上海",
      "联系方式": {
        "电话": "13812345678",
        "邮箱": "<EMAIL>",
        "社交账号": {
          "微信": "test_wechat"
        }
      },
      "职业信息": {
        "公司": "某公司",
        "职位": "工程师",
        "行业": "IT"
      },
      "家庭情况": {
        "婚育状况": "",
        "子女信息": [],
        "配偶姓名": ""
      }
    },
    "过往历史": "关系: 朋友",
    "兴趣": "",
    "关心话题": "",
    "旅游历史": "",
    "餐饮偏好": "",
    "期望": ""
  }
}
```

## 更新的文件和功能

### 1. 天气工具地点提取 (`app/service/ESmemory/es_event_service.py`)

**更新内容：**
- 优先从新的标准化结构 `基本信息.当前城市` 和 `基本信息.家乡` 中提取地点信息
- 保持对旧格式扁平结构的兼容性
- 支持递归查找地点相关字段

**提取逻辑：**
1. **方法1（优先）**：从标准化结构提取
   - `基本信息.当前城市` → 当前城市
   - `基本信息.家乡` → 家乡
2. **方法2（兼容）**：递归查找包含地点关键词的字段
   - 关键词：地点、城市、居住地、工作地点、生活地点等

### 2. 手机号提取 (`app/tools/phonenumber.py`)

**更新内容：**
- 优先从新的标准化结构 `基本信息.联系方式.电话` 中提取手机号
- 保持对旧格式扁平结构的兼容性
- 支持递归查找手机号相关字段

**提取逻辑：**
1. **方法1（优先）**：从标准化结构提取
   - `基本信息.联系方式.电话` → 手机号
2. **方法2（兼容）**：递归查找包含手机号关键词的字段
   - 关键词：phone、手机号、mobile、电话、手机号码等

## 兼容性特点

### 1. 向后兼容
- 所有旧格式的数据仍能正常工作
- 不会破坏现有功能

### 2. 向前兼容
- 新格式数据能够被正确识别和处理
- 优先使用标准化结构中的数据

### 3. 混合格式支持
- 同时支持新旧格式混合存在的情况
- 优先使用新格式，回退到旧格式

## 测试验证

已通过以下测试场景验证兼容性：

### 地点提取测试
- ✅ 新格式：正确提取 `当前城市: 上海, 家乡: 北京`
- ✅ 旧格式：正确提取 `工作地点: 深圳, 生活地点: 广州`
- ✅ 混合格式：正确提取 `当前城市: 杭州, 家乡: 南京, 工作地点: 苏州`

### 手机号提取测试
- ✅ 新格式：正确提取 `13812345678`（从标准化结构）
- ✅ 旧格式：正确提取 `13987654321`（从扁平结构）
- ✅ 混合格式：正确提取 `13666666666`（从旧格式字段）

## 注意事项

1. **数据迁移**：建议逐步将旧格式数据迁移到新的标准化结构
2. **性能考虑**：递归查找会有轻微的性能开销，但在可接受范围内
3. **日志记录**：所有提取过程都有详细的日志记录，便于调试和监控
4. **错误处理**：包含完善的异常处理，确保系统稳定性

## 后续计划

1. 监控新旧格式数据的使用情况
2. 逐步迁移所有旧格式数据到标准化结构
3. 在确认所有数据迁移完成后，可以考虑移除旧格式兼容代码
