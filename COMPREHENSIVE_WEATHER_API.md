# 综合天气服务接口文档

## 🌤️ 功能概述

综合天气服务是一个智能的多地点天气查询接口，能够根据用户的社交关系和事件安排，提供全面的天气信息和个性化提醒。

## 🎯 核心特性

### 1. 多维度地点收集
- **用户常用地址**：从用户档案中提取工作地点、居住地点等
- **事件地址**：即将发生的事件地点（未来7天）
- **亲属地址**：直属亲属（父母、配偶、子女）的地址
- **老板地址**：上司、领导等重要人员的地址

### 2. 智能天气查询
- **体感温度优先**：优先显示体感温度，更贴近实际感受
- **并发查询**：同时获取多个地点的天气信息
- **错误容错**：单个地点失败不影响其他地点查询

### 3. AI个性化提醒
- **温和语气**：像朋友一样的关怀提醒
- **关系感知**：结合社交关系给出针对性建议
- **事件关联**：考虑即将发生的事件提供建议
- **智能降级**：AI失败时提供基础天气提醒

## 📡 接口定义

### 请求
```http
GET /humanrelation/comprehensive_weather?user_id={user_id}
```

### 响应格式
```json
{
  "result": "success",
  "user_id": "user123",
  "timestamp": "2025-01-23T18:00:00",
  "weather_summary": {
    "total_locations": 5,
    "successful_queries": 4,
    "failed_queries": 1
  },
  "locations": [
    {
      "name": "上海",
      "type": "user_location",
      "source": "用户工作地点",
      "person_name": "张三",
      "relationship": "本人"
    },
    {
      "name": "北京",
      "type": "person_location", 
      "source": "父亲地址",
      "person_name": "张父",
      "relationship": "父亲"
    },
    {
      "name": "深圳",
      "type": "event_location",
      "source": "即将发生的事件",
      "event_description": "2025-01-25出差深圳",
      "event_time": "2025-01-25",
      "participants": ["我", "USER"]
    }
  ],
  "weather_data": [
    {
      "location": {
        "name": "上海",
        "type": "user_location",
        "source": "用户工作地点"
      },
      "weather": {
        "temperature": "15",
        "feelsLike": "12",
        "weather": "多云",
        "humidity": "65",
        "windDirection": "东北风",
        "windPower": "3级",
        "aqi": "85",
        "uvIndex": "5"
      },
      "success": true,
      "adcode": "310000"
    }
  ],
  "ai_reminder": "张三，今天上海多云，体感12°C有点凉，建议穿薄外套。你父亲那边北京晴天但较冷，记得提醒他注意保暖。明天要出差深圳，那边温度适宜，但湿度较高，记得带把伞以防突然下雨。"
}
```

## ⚙️ 配置要求

### Lion配置中心配置

#### 1. AI提醒提示词
**配置项**：`humanrelation.comprehensive_weather_reminder_prompt`

**建议配置**：
```
你是一个贴心的天气助手。请根据用户的个人信息、社交关系和多地点天气数据，生成一条温和、个性化的天气提醒。

要求：
1. 语气温和亲切，像朋友一样关怀
2. 优先使用体感温度，更贴近实际感受  
3. 结合用户的社交关系（家人、老板等）给出贴心建议
4. 关注即将发生的事件，提供针对性建议
5. 内容简洁实用，不超过200字
6. 避免过于正式的表达，使用日常对话的语气

输入信息包括：
- user_profile: 用户基本信息
- weather_summary: 各地点天气摘要  
- location_relationships: 地点与人员关系信息
- current_time: 当前时间

请生成一条温暖贴心的天气提醒。
```

#### 2. 其他必需配置
- `humanrelation.event_index_name`: 事件索引名称（默认：memory_event_store）
- `humanrelation.memory_extraction_model`: AI模型（默认：gpt-4o-mini）

## 🔍 地点收集逻辑

### 1. 用户地址提取
从用户档案的 `key_attributes` 中查找：
- 地点、工作地点、生活地点、居住地点
- 常住地址、工作地址、家庭地址
- 现居地、所在地

### 2. 事件地址提取
- 查询未来7天的事件记录
- 从事件的 `location` 字段提取地址
- 从 `description_text` 中提取时间信息

### 3. 亲属地址提取
搜索以下关系的人员：
- 直系亲属：父亲、母亲、妻子、丈夫、儿子、女儿
- 口语化称呼：爸爸、妈妈、老婆、老公

### 4. 老板地址提取
搜索以下关系的人员：
- 职场关系：老板、上司、领导、主管
- 职位称呼：经理、总监、CEO、CTO

## 📊 使用示例

### 基础调用
```bash
curl -X GET "http://localhost:8080/humanrelation/comprehensive_weather?user_id=user123"
```

### 响应示例
```json
{
  "result": "success",
  "user_id": "user123", 
  "timestamp": "2025-01-23T18:00:00",
  "weather_summary": {
    "total_locations": 3,
    "successful_queries": 3,
    "failed_queries": 0
  },
  "ai_reminder": "今天上海多云，体感12°C，建议穿薄外套。你妈妈那边杭州有小雨，记得提醒她带伞。明天要去北京开会，那边晴天但风大，注意保暖防风。"
}
```

## 🎨 AI提醒特色

### 1. 个性化称呼
- 使用用户的昵称或别名
- 自然的称呼方式

### 2. 关系感知
- "你妈妈那边..."
- "老板在的地方..."
- "明天要去..."

### 3. 体感温度优先
- "体感12°C有点凉"
- "感觉会比较闷热"

### 4. 事件关联
- "明天要出差，那边..."
- "开会的地方..."
- "活动当天..."

## 🔧 技术特点

### 1. 智能去重
- 相同地点名称自动去重
- 保留最相关的地点信息

### 2. 并发查询
- 多个地点同时查询天气
- 提高响应速度

### 3. 错误容错
- 单个地点失败不影响整体
- 提供详细的错误信息

### 4. 降级机制
- AI失败时提供基础提醒
- 确保用户始终能获得天气信息

## 🚀 扩展建议

### 1. 时间范围配置
- 支持配置事件查询的天数范围
- 支持历史事件的地点分析

### 2. 地点权重
- 根据访问频率调整地点优先级
- 智能推荐最重要的地点

### 3. 推送集成
- 定时推送天气提醒
- 重要天气变化主动通知

### 4. 多语言支持
- 支持不同语言的天气提醒
- 本地化的表达方式

这个综合天气服务接口为用户提供了全方位的天气关怀，不仅关注用户本人，还考虑到了用户的社交圈和日程安排，真正做到了智能化和人性化的天气服务。
