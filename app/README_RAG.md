# RAG功能使用指南

## 🎯 功能概述

本系统实现了基于RAG（检索增强生成）的智能对话功能，能够：
- 从新闻库中检索相关信息
- 结合个人记忆提供个性化建议
- 增强AI回答的准确性和时效性

## 🚀 启动方式

### 方式1：推荐启动方式
```bash
cd app
python run.py
```

### 方式2：直接启动
```bash
cd app
python app.py
```

## 🧪 测试RAG功能

```bash
cd app
python test_rag.py
```

## 📡 API接口

### 1. 流式聊天接口
```bash
POST /humanrelation/chat
Content-Type: application/json

{
    "content": "如何处理职场人际关系",
    "conversation_id": "conv_123",
    "user_id": "user_456"
}
```

### 2. 新闻搜索接口
```bash
GET /humanrelation/search_news?keyword=职场关系&size=5
```

### 3. 测试新闻增强功能
```bash
POST /humanrelation/test_news_enhancement
Content-Type: application/json

{
    "content": "如何处理职场人际关系",
    "conversation_id": "conv_123",
    "user_id": "user_456"
}
```

## 🔧 核心组件

### 1. RAG检索器 (`rag_retriever.py`)
- **向量嵌入**: 使用SentenceTransformer进行文本向量化
- **语义搜索**: 结合向量相似度和关键词匹配
- **智能排序**: 综合多种分数进行结果排序

### 2. 新闻增强 (`agents/hr.py`)
- **新闻检索**: 根据用户查询检索相关新闻
- **上下文构建**: 将新闻信息融入对话上下文
- **提示增强**: 构建包含最新资讯的系统提示

### 3. 记忆服务 (`service/enhanced_memory_service.py`)
- **记忆提取**: 从对话中提取人员和事件信息
- **记忆检索**: 根据查询检索相关记忆
- **个性化建议**: 基于个人记忆提供建议

## 📊 工作流程

1. **用户提问** → 系统接收用户输入
2. **记忆处理** → 提取和检索个人记忆
3. **新闻检索** → 从新闻库搜索相关信息
4. **上下文构建** → 整合记忆和新闻信息
5. **AI生成** → 基于增强上下文生成回答

## 🎨 特色功能

### 智能查询扩展
- 自动为短查询添加相关词汇
- 根据查询类型扩展关键词

### 多模态检索
- 向量相似度搜索
- 关键词匹配搜索
- 混合排序算法

### 个性化增强
- 结合个人记忆
- 引用最新资讯
- 提供针对性建议

## 🔍 监控和调试

### 健康检查
```bash
GET /monitor/alive
```

### 线程状态
```bash
GET /humanrelation/reminder_thread_status
```

### 日志查看
系统会输出详细的检索和增强日志，包括：
- 新闻检索结果
- 相似度分数
- 上下文构建过程

## 🛠️ 配置说明

### 模型配置
- 主模型: `paraphrase-multilingual-MiniLM-L12-v2`
- 备用模型: `sentence-transformers/all-MiniLM-L6-v2`
- 相似度阈值: 0.2（可调整）

### 检索配置
- 默认返回数量: 3条新闻
- 最大候选数量: 10条
- 内容截取长度: 500字符

## 🚨 注意事项

1. **依赖安装**: 确保所有依赖包已正确安装
2. **ES连接**: 确保Elasticsearch服务正常运行
3. **模型下载**: 首次运行会自动下载嵌入模型
4. **内存使用**: 嵌入模型会占用一定内存

## 🎉 效果展示

使用RAG功能后，AI回答会：
- 引用最新相关资讯
- 结合个人记忆信息
- 提供更准确和实用的建议
- 保持对话的连贯性和个性化
