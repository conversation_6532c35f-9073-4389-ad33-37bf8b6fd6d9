import base64
import requests
from opensearchpy import OpenSearch
from langchain_core.tools import tool
from configs.config import APP_KEY, CURRENT_ENV, ES_ACCESS_KEY
from configs.lion_config import get_value
from utils.logger import logger


def generate_token(appkey, accesskey):
    """生成Basic Auth的TOKEN"""
    s = f"{appkey}:{accesskey}"
    return base64.b64encode(s.encode()).decode()


def call_openapi(appkey, accesskey, cluster_name):
    """调用OpenAPI获取集群节点信息"""
    token = generate_token(appkey, accesskey)
    headers = {
        "Authorization": f"Basic {token}"
    }
    url = f"{openapi_url}/clusters/{cluster_name}/nodes"
    response = requests.get(url, headers=headers)
    response.raise_for_status()  # 如果请求失败则抛出异常

    data = response.json()
    if data["code"] != 0:
        raise Exception(f"Error fetching nodes: {data['message']}")

    # 提取节点的HTTP地址
    nodes = [{"host": node['ip'], "port": node['httpPort'], "scheme": "http"} for node in data["data"]]
    return nodes


# 从配置中心获取配置参数
cluster_name = "ai_eaglenode-ai-resources_default"
openapi_url = "http://eagleweb.sankuai.com/openapi/"

# 获取节点列表并初始化OpenSearch客户端
dianping_client = None

def init_xhc_client():
    """初始化XHC数据库客户端"""
    global dianping_client

    try:

        # 调用OpenAPI获取节点信息
        nodes = call_openapi(APP_KEY, ES_ACCESS_KEY, cluster_name)

        # 初始化OpenSearch客户端
        dianping_client = OpenSearch(
            hosts=nodes,
            http_auth=(APP_KEY, ES_ACCESS_KEY),
            use_ssl=False,
            verify_certs=False,
            sniff_on_start=True,  
            sniff_on_connection_fail=True,  
            sniffer_timeout=60
        )

        # 测试连接
        cluster_info = dianping_client.info()
        logger.info(f"✅ XHC数据库连接初始化成功！集群信息: {cluster_info.get('cluster_name', 'unknown')}")
        return True

    except Exception as e:
        logger.error(f"❌ XHC数据库连接初始化失败: {e}")
        logger.error(f"📊 错误详情: {type(e).__name__}: {str(e)}")
        dianping_client = None
        return False

# 尝试初始化客户端
init_success = init_xhc_client()


@tool(description="用于查询旅游目的地、美食、景点等相关信息的专业数据库工具。当用户询问任何地方的旅游、美食、景点、住宿等信息时使用此工具")
def get_data_from_xhc(query: str) -> str:
    """
    用于查询旅游目的地、美食、景点等相关信息的专业数据库工具。
    根据用户查询关键词，从XHC数据库中检索相关的旅游、美食、景点、住宿、交通等信息。

    适用场景：
    - 用户询问某个地方的美食（如"马尔代夫美食"、"北京小吃"）
    - 用户询问旅游景点（如"巴厘岛景点"、"日本樱花"）
    - 用户询问住宿信息（如"三亚酒店"、"民宿推荐"）
    - 用户询问旅游攻略（如"泰国旅游"、"欧洲自由行"）

    Args:
        query: 查询关键词，建议提取用户问题中的核心地名+需求，如"马尔代夫美食"、"巴厘岛景点"、"日本旅游"等

    Returns:
        str: 查询结果的文本内容列表，以字符串形式返回
    """
    global dianping_client

    # 如果客户端未初始化，尝试重新初始化
    if not dianping_client:
        logger.warning("⚠️ XHC数据库客户端未初始化，尝试重新连接...")
        if not init_xhc_client():
            logger.error("❌ XHC数据库连接失败，请检查网络连接和配置信息")
            return "❌ XHC数据库连接失败，无法获取旅游信息。请稍后再试或联系管理员。"

    try:
        logger.info(f"🔍 开始执行XHC数据库查询，关键词: '{query}'")

        # 获取所有索引的别名信息
        indices = dianping_client.indices.get_alias(index="*")
        index_names = list(filter(lambda x: 'xhc' in x, list(indices.keys())))

        if not index_names:
            logger.warning("⚠️ 未找到任何XHC相关索引")
            return "未找到相关数据索引，请检查数据库配置"

        logger.info(f"📋 找到XHC索引: {index_names}")

        all_results = []
        successful_queries = 0

        # 对每个索引执行查询
        for index_name in index_names:
            try:
                logger.debug(f"🔎 正在查询索引: {index_name}")

                # 构建搜索查询体
                search_body = {
                    "query": {
                        "match": {
                            "text": query
                        }
                    },
                    "size": int(get_value("xiaomei.humanrelation.xhc_query_size", 10))
                }

                response = dianping_client.search(
                    index=index_name,
                    body=search_body
                )

                # 提取hits部分的结果
                hits = response.get('hits', {}).get('hits', [])
                all_results.extend(hits)
                successful_queries += 1

                logger.info(f"✅ 索引 {index_name} 查询完成，找到 {len(hits)} 条结果")
                if hits:
                    logger.debug(f"📄 第一条结果预览: {hits[0].get('_source', {}).get('text', '无内容')[:100]}...")

            except Exception as e:
                logger.error(f"❌ 查询索引 {index_name} 时发生错误: {e}")
                continue

        logger.info(f"📊 XHC查询阶段完成，成功查询 {successful_queries} 个索引，总共获得 {len(all_results)} 条结果")

        if not all_results:
            logger.warning("⚠️ 所有索引查询均未返回结果")
            return "未找到相关信息，请尝试使用不同的关键词"

        # 随机打乱结果并提取文本内容
        import random
        random.shuffle(all_results)

        # 获取配置的返回结果数量
        result_num = int(get_value("xiaomei.humanrelation.xhc_result_num", 5))
        selected_results = all_results[:result_num]

        # 提取文本内容
        text_results = []
        for result in selected_results:
            text_content = result.get('_source', {}).get('text', '')
            if text_content:
                text_results.append(text_content)

        logger.info(f"🎯 XHC查询完成，返回 {len(text_results)} 条有效结果")
        return text_results

    except Exception as e:
        error_msg = f"执行XHC数据库查询时发生错误: {e}"
        logger.error(f"❌ {error_msg}")
        return error_msg



if __name__ == "__main__":
    # 测试XHC数据库查询工具
    test_queries = ["马尔代夫", "马尔代夫美食", "旅游景点"]

    for query in test_queries:
        print(f"\n{'='*60}")
        print(f"测试查询: {query}")
        print(f"{'='*60}")

        result = get_data_from_xhc(query)

        if isinstance(result, list) and result:
            print(f"✅ 查询成功，返回 {len(result)} 条结果")
            print(f"📄 第一条结果预览: {result[0][:200]}...")
        elif isinstance(result, str):
            print(f"⚠️ 查询结果: {result}")
        else:
            print("❌ 查询失败，未返回有效结果")