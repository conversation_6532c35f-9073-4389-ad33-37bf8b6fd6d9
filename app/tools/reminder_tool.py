from langchain_core.tools import tool
import requests
import os
import re
from datetime import datetime
from configs.lion_config import get_value
from my_mysql.entity.reminders import insert_reminder, delete_reminder_by_id, query_reminders_by_user, query_reminder_by_id
import contextvars

# 使用contextvars来在异步环境中传递user_id
current_user_id = contextvars.ContextVar('current_user_id', default=None)

def create_recurring_reminders(user_id: str, reminder_text: str, start_time_str: str, recurrence_rule: str):
    """
    创建重复提醒
    """
    try:
        from datetime import timedelta

        start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
        base_time = start_time.time()  # 获取时间部分（小时:分钟:秒）
        created_count = 0

        # 解析重复规则并创建提醒
        if recurrence_rule == "DAILY":
            # 每天重复，创建未来30天的提醒
            for i in range(30):
                trigger_date = start_time.date() + timedelta(days=i)
                trigger_datetime = datetime.combine(trigger_date, base_time)
                if trigger_datetime > datetime.now():  # 只创建未来的提醒
                    insert_reminder(
                        user_id=user_id,
                        reminder_text_template=reminder_text,
                        next_trigger_time=trigger_datetime,
                        base_event_date=start_time,
                        recurrence_rule=recurrence_rule,
                        status="active"
                    )
                    created_count += 1

        elif recurrence_rule == "WEEKDAYS":
            # 工作日重复（周一到周五），创建未来30天的工作日提醒
            current_date = start_time.date()
            for i in range(60):  # 检查60天内的工作日
                check_date = current_date + timedelta(days=i)
                if check_date.weekday() < 5:  # 0-4 表示周一到周五
                    trigger_datetime = datetime.combine(check_date, base_time)
                    if trigger_datetime > datetime.now() and created_count < 30:
                        insert_reminder(
                            user_id=user_id,
                            reminder_text_template=reminder_text,
                            next_trigger_time=trigger_datetime,
                            base_event_date=start_time,
                            recurrence_rule=recurrence_rule,
                            status="active"
                        )
                        created_count += 1

        elif recurrence_rule == "WEEKENDS":
            # 周末重复（周六、周日）
            current_date = start_time.date()
            for i in range(60):
                check_date = current_date + timedelta(days=i)
                if check_date.weekday() >= 5:  # 5,6 表示周六、周日
                    trigger_datetime = datetime.combine(check_date, base_time)
                    if trigger_datetime > datetime.now() and created_count < 20:
                        insert_reminder(
                            user_id=user_id,
                            reminder_text_template=reminder_text,
                            next_trigger_time=trigger_datetime,
                            base_event_date=start_time,
                            recurrence_rule=recurrence_rule,
                            status="active"
                        )
                        created_count += 1

        elif recurrence_rule.startswith("WEEKLY:"):
            # 每周特定日期重复，如 WEEKLY:1,3,5
            days_str = recurrence_rule.split(":")[1]
            weekdays = [int(d) - 1 for d in days_str.split(",")]  # 转换为Python的weekday格式(0-6)

            current_date = start_time.date()
            for i in range(84):  # 检查12周
                check_date = current_date + timedelta(days=i)
                if check_date.weekday() in weekdays:
                    trigger_datetime = datetime.combine(check_date, base_time)
                    if trigger_datetime > datetime.now() and created_count < 30:
                        insert_reminder(
                            user_id=user_id,
                            reminder_text_template=reminder_text,
                            next_trigger_time=trigger_datetime,
                            base_event_date=start_time,
                            recurrence_rule=recurrence_rule,
                            status="active"
                        )
                        created_count += 1

        elif recurrence_rule.startswith("MONTHLY:"):
            # 每月特定日期重复，如 MONTHLY:1,15
            days_str = recurrence_rule.split(":")[1]
            month_days = [int(d) for d in days_str.split(",")]

            current_date = start_time.date()
            for month_offset in range(6):  # 创建未来6个月的提醒
                for day in month_days:
                    try:
                        target_date = current_date.replace(month=current_date.month + month_offset, day=day)
                        # 处理跨年的情况
                        if current_date.month + month_offset > 12:
                            target_date = current_date.replace(
                                year=current_date.year + 1,
                                month=(current_date.month + month_offset) % 12,
                                day=day
                            )

                        trigger_datetime = datetime.combine(target_date, base_time)
                        if trigger_datetime > datetime.now():
                            insert_reminder(
                                user_id=user_id,
                                reminder_text_template=reminder_text,
                                next_trigger_time=trigger_datetime,
                                base_event_date=start_time,
                                recurrence_rule=recurrence_rule,
                                status="active"
                            )
                            created_count += 1
                    except ValueError:
                        # 处理月份没有这一天的情况（如2月30日）
                        continue

        return f"✅ 重复提醒已成功设置！已创建 {created_count} 个提醒实例。重复规则：{recurrence_rule}，内容：{reminder_text}"

    except Exception as e:
        return f"❌ 创建重复提醒异常: {str(e)}"

@tool("add_reminder", return_direct=True)
def add_reminder_tool(reminder_text: str, next_trigger_time: str, event_time: str = "", recurrence_rule: str = ""):
    """
    添加提醒。参数：
    - reminder_text: 提醒内容
    - next_trigger_time: 触发时间（如 2025-07-02 09:00:00）
    - event_time: 事件时间（可选，如 2025-07-02 11:00:00），如果不提供则使用触发时间
    - recurrence_rule: 重复规则（可选），如：
      * "DAILY" - 每天
      * "WEEKLY:1,3,5" - 每周一、三、五（1=周一，7=周日）
      * "MONTHLY:1,15" - 每月1日、15日
      * "WEEKDAYS" - 工作日（周一到周五）
      * "WEEKENDS" - 周末（周六、周日）
      * "" - 不重复（默认）
    """

    # 智能时间解析和修正
    def parse_and_fix_time(time_str):
        """解析并修正时间字符串，确保年份为当前年份"""
        current_year = datetime.now().year
        current_date = datetime.now()
        print(f"当前时间: {current_date}")
        print(f"输入时间字符串: {time_str}")



        # 如果没有年份，自动添加当前年份
        if re.match(r'^\d{2}-\d{2} \d{2}:\d{2}:\d{2}$', time_str):  # MM-DD HH:MM:SS
            time_str = f"{current_year}-{time_str}"
            print(f"添加年份后的时间: {time_str}")
        elif re.match(r'^\d{2}-\d{2} \d{2}:\d{2}$', time_str):  # MM-DD HH:MM
            time_str = f"{current_year}-{time_str}:00"
            print(f"添加年份和秒数后的时间: {time_str}")

        try:
            # 尝试解析时间
            parsed_time = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
            print(f"解析后的时间: {parsed_time}")

            # 确保时间是未来时间
            now = datetime.now()
            print(f"当前时间: {now}")
            if parsed_time <= now:
                return None, f"提醒时间必须是未来时间。当前时间：{now.strftime('%Y-%m-%d %H:%M:%S')}，您设置的时间：{time_str}"

            result_time = parsed_time.strftime("%Y-%m-%d %H:%M:%S")
            print(f"最终返回的时间: {result_time}")
            return result_time, None
        except ValueError as e:
            return None, f"时间格式错误：{time_str}，请使用格式：YYYY-MM-DD HH:MM:SS"

    # 解析和修正时间
    fixed_time, error_msg = parse_and_fix_time(next_trigger_time)
    if error_msg:
        return f"❌ {error_msg}"

    try:
        # 从上下文获取user_id
        user_id = current_user_id.get()
        if not user_id:
            return "❌ 系统错误：无法获取用户身份信息"

        # 处理重复规则
        if recurrence_rule:
            return create_recurring_reminders(user_id, reminder_text, fixed_time, recurrence_rule)

        # 处理事件时间
        if event_time:
            # 如果提供了事件时间，解析并修正
            fixed_event_time, event_error_msg = parse_and_fix_time(event_time)
            if event_error_msg:
                return f"❌ 事件时间格式错误: {event_error_msg}"
        else:
            # 如果没有提供事件时间，使用触发时间
            fixed_event_time = fixed_time

        # 直接调用数据库函数（单次提醒）
        reminder_id = insert_reminder(
            user_id=user_id,
            reminder_text_template=reminder_text,
            next_trigger_time=datetime.strptime(fixed_time, "%Y-%m-%d %H:%M:%S"),
            base_event_date=datetime.strptime(fixed_event_time, "%Y-%m-%d %H:%M:%S"),
            recurrence_rule=recurrence_rule,
            status="active"
        )

        if reminder_id:
            return f"✅ 提醒已成功设置！我会在 {fixed_time} 提醒您：{reminder_text}"
        else:
            return f"❌ 添加提醒失败"
    except Exception as e:
        return f"❌ 添加提醒异常: {str(e)}"

@tool("delete_reminder", return_direct=True)
def delete_reminder_tool(reminder_description: str):
    """
    删除提醒。参数：
    - reminder_description: 提醒描述（可以是提醒内容、时间或部分内容）
    """
    try:
        # 从上下文获取user_id
        user_id = current_user_id.get()
        if not user_id:
            return "❌ 系统错误：无法获取用户身份信息"

        print(f"正在为用户 {user_id} 删除提醒，描述: {reminder_description}")

        # 获取用户的所有提醒
        reminders = query_reminders_by_user(user_id)
        print(f"查询到的提醒数量: {len(reminders) if reminders else 0}")

        if not reminders:
            return f"📝 您目前没有任何提醒。"

        # 查找匹配的提醒
        matched_reminders = []
        for reminder in reminders:
            try:
                # SQLAlchemy Row对象转换为字典
                if hasattr(reminder, '_asdict'):
                    reminder_dict = reminder._asdict()
                elif hasattr(reminder, '__dict__'):
                    reminder_dict = reminder.__dict__
                else:
                    # 如果是元组或其他格式，尝试转换为字典
                    reminder_dict = dict(reminder) if hasattr(reminder, '__iter__') else {}

                reminder_text = str(reminder_dict.get('reminder_text_template', '')).lower()
                reminder_time = str(reminder_dict.get('next_trigger_time', '')).lower()
                reminder_id = str(reminder_dict.get('reminder_id', ''))

                # 检查描述是否匹配提醒内容、时间或ID
                description_lower = reminder_description.lower()
                if (description_lower in reminder_text or
                    description_lower in reminder_time or
                    description_lower == reminder_id):
                    matched_reminders.append(reminder_dict)
                    print(f"找到匹配的提醒: ID={reminder_id}, 内容={reminder_text}")
            except Exception as e:
                print(f"处理提醒匹配时出错: {e}")
                continue

        print(f"匹配的提醒数量: {len(matched_reminders)}")

        if not matched_reminders:
            # 如果没有找到匹配的提醒，返回所有提醒供用户选择
            reminder_list = []
            for i, reminder in enumerate(reminders):
                try:
                    # SQLAlchemy Row对象转换为字典
                    if hasattr(reminder, '_asdict'):
                        reminder_dict = reminder._asdict()
                    elif hasattr(reminder, '__dict__'):
                        reminder_dict = reminder.__dict__
                    else:
                        # 如果是元组或其他格式，尝试转换为字典
                        reminder_dict = dict(reminder) if hasattr(reminder, '__iter__') else {}

                    reminder_id = reminder_dict.get('reminder_id', '未知')
                    status = reminder_dict.get('status', '未知')
                    next_trigger_time = reminder_dict.get('next_trigger_time', '未知')
                    reminder_text = reminder_dict.get('reminder_text_template', '无内容')

                    # 格式化时间
                    if isinstance(next_trigger_time, datetime):
                        time_str = next_trigger_time.strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        time_str = str(next_trigger_time)

                    status_text = {
                        'active': '🟢 活跃',
                        'processing': '🟡 处理中',
                        'completed': '🔴 已完成'
                    }.get(status, '❓ 未知')

                    reminder_list.append(
                        f"{i+1}. ID: {reminder_id} | "
                        f"状态: {status_text} | "
                        f"时间: {time_str} | "
                        f"内容: {reminder_text}"
                    )
                except Exception as e:
                    print(f"格式化提醒 {i} 时出错: {e}")
                    reminder_list.append(f"{i+1}. 提醒数据格式错误")

            return f"❓ 没有找到匹配的提醒。您的所有提醒如下：\n" + "\n".join(reminder_list) + "\n\n请提供更具体的描述，或者直接提供提醒ID。"

        elif len(matched_reminders) == 1:
            # 找到唯一匹配的提醒，直接删除
            reminder = matched_reminders[0]
            reminder_id = reminder['reminder_id']

            print(f"准备删除提醒 ID: {reminder_id}")

            # 执行删除操作
            success = delete_reminder_by_id(reminder_id, user_id)
            if success:
                result = f"✅ 已成功删除提醒：{reminder.get('reminder_text_template', '无内容')} (ID: {reminder_id})"
                print(f"删除成功: {result}")
                return result
            else:
                result = f"❌ 删除提醒失败"
                print(f"删除失败: {result}")
                return result

        else:
            # 找到多个匹配的提醒，列出供用户选择
            reminder_list = []
            for i, reminder in enumerate(matched_reminders):
                try:
                    # 这里reminder已经是字典了，因为之前已经转换过
                    reminder_id = reminder.get('reminder_id', '未知')
                    status = reminder.get('status', '未知')
                    next_trigger_time = reminder.get('next_trigger_time', '未知')
                    reminder_text = reminder.get('reminder_text_template', '无内容')

                    # 格式化时间
                    if isinstance(next_trigger_time, datetime):
                        time_str = next_trigger_time.strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        time_str = str(next_trigger_time)

                    status_text = {
                        'active': '🟢 活跃',
                        'processing': '🟡 处理中',
                        'completed': '🔴 已完成'
                    }.get(status, '❓ 未知')

                    reminder_list.append(
                        f"{i+1}. ID: {reminder_id} | "
                        f"状态: {status_text} | "
                        f"时间: {time_str} | "
                        f"内容: {reminder_text}"
                    )
                except Exception as e:
                    print(f"格式化匹配提醒 {i} 时出错: {e}")
                    reminder_list.append(f"{i+1}. 提醒数据格式错误")

            result = f"🔍 找到 {len(matched_reminders)} 个匹配的提醒：\n" + "\n".join(reminder_list) + "\n\n请提供更具体的描述或直接提供提醒ID来删除特定的提醒。"
            print(f"多个匹配结果: {result}")
            return result

    except Exception as e:
        error_msg = f"❌ 删除提醒异常: {str(e)}"
        print(f"删除提醒时发生异常: {error_msg}")
        return error_msg

@tool("list_reminders", return_direct=True)
def list_reminders_tool():
    """
    获取用户的所有提醒列表。
    """
    try:
        # 从上下文获取user_id
        user_id = current_user_id.get()
        if not user_id:
            return "❌ 系统错误：无法获取用户身份信息"

        # 添加调试信息
        print(f"正在查询用户 {user_id} 的提醒列表...")

        reminders = query_reminders_by_user(user_id)
        print(f"查询结果类型: {type(reminders)}")
        print(f"查询结果数量: {len(reminders) if reminders else 0}")

        if not reminders:
            return f"📝 您目前没有任何提醒。"

        reminder_list = []
        for i, reminder in enumerate(reminders):
            try:
                # SQLAlchemy Row对象转换为字典
                if hasattr(reminder, '_asdict'):
                    reminder_dict = reminder._asdict()
                elif hasattr(reminder, '__dict__'):
                    reminder_dict = reminder.__dict__
                else:
                    # 如果是元组或其他格式，尝试转换为字典
                    reminder_dict = dict(reminder) if hasattr(reminder, '__iter__') else {}

                print(f"提醒 {i} 原始数据: {reminder}")
                print(f"提醒 {i} 转换后: {reminder_dict}")

                # 安全地获取字段值
                reminder_id = reminder_dict.get('reminder_id', '未知')
                status = reminder_dict.get('status', '未知')
                next_trigger_time = reminder_dict.get('next_trigger_time', '未知')
                reminder_text = reminder_dict.get('reminder_text_template', '无内容')

                # 格式化时间
                if isinstance(next_trigger_time, datetime):
                    time_str = next_trigger_time.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    time_str = str(next_trigger_time)

                # 状态文本映射
                status_text = {
                    'active': '🟢 活跃',
                    'processing': '🟡 处理中',
                    'completed': '🔴 已完成'
                }.get(status, '❓ 未知')

                reminder_list.append(
                    f"{i+1}. ID: {reminder_id} | "
                    f"状态: {status_text} | "
                    f"时间: {time_str} | "
                    f"内容: {reminder_text}"
                )
            except Exception as e:
                print(f"处理提醒 {i} 时出错: {e}")
                print(f"提醒 {i} 数据类型: {type(reminder)}")
                print(f"提醒 {i} 数据内容: {reminder}")
                reminder_list.append(f"{i+1}. 提醒数据格式错误: {str(reminder)}")

        result = f"📝 您的提醒列表（共 {len(reminders)} 条）：\n" + "\n".join(reminder_list)
        print(f"返回结果: {result}")
        return result

    except Exception as e:
        error_msg = f"❌ 获取提醒列表异常: {str(e)}"
        print(f"错误详情: {error_msg}")
        return error_msg

@tool("delete_recurring_reminders", return_direct=True)
def delete_recurring_reminders_tool(recurrence_description: str):
    """
    删除重复提醒（根据重复规则删除所有相关提醒）。参数：
    - recurrence_description: 重复提醒的描述（如"每天起床"、"工作日上班"等）
    """
    try:
        # 从上下文获取user_id
        user_id = current_user_id.get()
        if not user_id:
            return "❌ 系统错误：无法获取用户身份信息"

        print(f"正在为用户 {user_id} 删除重复提醒，描述: {recurrence_description}")

        # 获取用户的所有提醒
        reminders = query_reminders_by_user(user_id)
        print(f"查询到的提醒数量: {len(reminders) if reminders else 0}")

        if not reminders:
            return f"📝 您目前没有任何提醒。"

        # 查找匹配的重复提醒
        matched_reminders = []
        description_lower = recurrence_description.lower()

        for reminder in reminders:
            try:
                # SQLAlchemy Row对象转换为字典
                if hasattr(reminder, '_asdict'):
                    reminder_dict = reminder._asdict()
                elif hasattr(reminder, '__dict__'):
                    reminder_dict = reminder.__dict__
                else:
                    reminder_dict = dict(reminder) if hasattr(reminder, '__iter__') else {}

                reminder_text = str(reminder_dict.get('reminder_text_template', '')).lower()
                recurrence_rule = str(reminder_dict.get('recurrence_rule', ''))

                # 检查是否匹配重复提醒（有重复规则且内容匹配）
                if recurrence_rule and description_lower in reminder_text:
                    matched_reminders.append(reminder_dict)
                    print(f"找到匹配的重复提醒: ID={reminder_dict.get('reminder_id')}, 内容={reminder_text}, 规则={recurrence_rule}")

            except Exception as e:
                print(f"处理提醒匹配时出错: {e}")
                continue

        if not matched_reminders:
            return f"❓ 没有找到匹配的重复提醒。请检查描述是否正确，或使用list_reminders查看所有提醒。"

        # 删除所有匹配的重复提醒
        deleted_count = 0
        for reminder in matched_reminders:
            reminder_id = reminder['reminder_id']
            success = delete_reminder_by_id(reminder_id, user_id)
            if success:
                deleted_count += 1

        if deleted_count > 0:
            return f"✅ 已成功删除 {deleted_count} 个重复提醒。内容包含：{recurrence_description}"
        else:
            return f"❌ 删除重复提醒失败"

    except Exception as e:
        error_msg = f"❌ 删除重复提醒异常: {str(e)}"
        print(f"删除重复提醒时发生异常: {error_msg}")
        return error_msg

@tool("edit_reminder", return_direct=True)
def edit_reminder_tool(reminder_id: str, reminder_text: str = "", next_trigger_time: str = "", event_time: str = "", recurrence_rule: str = ""):
    """
    编辑提醒。参数：
    - reminder_id: 提醒ID（必填）
    - reminder_text: 提醒内容（可选）
    - next_trigger_time: 触发时间（可选，如 2025-07-02 09:00:00）
    - event_time: 事件时间（可选，如 2025-07-02 11:00:00）
    - recurrence_rule: 重复规则（可选），如：
      * "DAILY" - 每天
      * "WEEKLY:1,3,5" - 每周一、三、五（1=周一，7=周日）
      * "MONTHLY:1,15" - 每月1日、15日
      * "WEEKDAYS" - 工作日（周一到周五）
      * "WEEKENDS" - 周末（周六、周日）
      * "" - 不重复
    """
    try:
        # 从上下文获取user_id
        user_id = current_user_id.get()
        if not user_id:
            return "❌ 系统错误：无法获取用户身份信息"

        # 验证reminder_id
        if not reminder_id:
            return "❌ 请提供要编辑的提醒ID"

        # 构建更新参数
        update_params = {}
        
        if reminder_text:
            update_params['reminder_text_template'] = reminder_text
            
        if next_trigger_time:
            fixed_time, error_msg = parse_and_fix_time(next_trigger_time)
            if error_msg:
                return f"❌ {error_msg}"
            update_params['next_trigger_time'] = datetime.strptime(fixed_time, "%Y-%m-%d %H:%M:%S")
            
        if event_time:
            fixed_event_time, event_error_msg = parse_and_fix_time(event_time)
            if event_error_msg:
                return f"❌ 事件时间格式错误: {event_error_msg}"
            update_params['base_event_date'] = datetime.strptime(fixed_event_time, "%Y-%m-%d %H:%M:%S")
        elif next_trigger_time:
            # 如果没有提供事件时间但提供了触发时间，使用触发时间作为事件时间
            update_params['base_event_date'] = update_params['next_trigger_time']
            
        if recurrence_rule is not None:  # 允许设置为空字符串来清除重复规则
            update_params['recurrence_rule'] = recurrence_rule

        if not update_params:
            return "❌ 请提供要更新的字段（提醒内容、触发时间、事件时间或重复规则）"

        # 调用数据库更新函数
        from my_mysql.entity.reminders import update_reminder
        success = update_reminder(int(reminder_id), user_id, **update_params)
        
        if success:
            update_info = []
            if 'reminder_text_template' in update_params:
                update_info.append(f"内容: {reminder_text}")
            if 'next_trigger_time' in update_params:
                update_info.append(f"触发时间: {next_trigger_time}")
            if 'recurrence_rule' in update_params:
                update_info.append(f"重复规则: {recurrence_rule if recurrence_rule else '无重复'}")
                
            return f"✅ 提醒编辑成功！已更新：{', '.join(update_info)}"
        else:
            return f"❌ 编辑提醒失败，请检查提醒ID是否正确"
            
    except ValueError:
        return f"❌ 提醒ID格式错误，请提供数字ID"
    except Exception as e:
        return f"❌ 编辑提醒异常: {str(e)}"

def parse_and_fix_time(time_str):
    """解析并修正时间字符串，确保年份为当前年份"""
    current_year = datetime.now().year
    current_date = datetime.now()

    # 如果没有年份，自动添加当前年份
    if re.match(r'^\d{2}-\d{2} \d{2}:\d{2}:\d{2}$', time_str):  # MM-DD HH:MM:SS
        time_str = f"{current_year}-{time_str}"
    elif re.match(r'^\d{2}-\d{2} \d{2}:\d{2}$', time_str):  # MM-DD HH:MM
        time_str = f"{current_year}-{time_str}:00"

    try:
        # 尝试解析时间
        parsed_time = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")

        # 确保时间是未来时间
        now = datetime.now()
        if parsed_time <= now:
            return None, f"提醒时间必须是未来时间。当前时间：{now.strftime('%Y-%m-%d %H:%M:%S')}，您设置的时间：{time_str}"

        result_time = parsed_time.strftime("%Y-%m-%d %H:%M:%S")
        return result_time, None
    except ValueError as e:
        return None, f"时间格式错误：{time_str}，请使用格式：YYYY-MM-DD HH:MM:SS"
