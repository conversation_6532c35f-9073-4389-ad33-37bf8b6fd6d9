import contextvars
import datetime
import json

import requests
from configs.config import CURRENT_ENV
from configs.lion_config import get_value
from langchain_core.tools import tool
from pydantic import BaseModel, Field
from utils.logger import logger

# 全局上下文变量，用于存储当前用户ID
current_user_id = contextvars.ContextVar("current_user_id", default=None)

# 记录当前环境配置
logger.info(f"Weather工具初始化，当前环境: {CURRENT_ENV}")


class WeatherInfoInput(BaseModel):
    destination: str = Field(description="需要查询的目的地城市或地点名，例如北京，上海，恒电大厦")
    time: str = Field(description="需要查询的时间，格式必须为YYYY-MM-DD，例如2025-06-05")


def get_time_interval(time: str):
    """
    计算时间字符串与当前时间之间的间隔获取时间间隔
    """
    current_time = datetime.datetime.now().strftime("%Y-%m-%d")
    time_interval = (
        datetime.datetime.strptime(time, "%Y-%m-%d") - datetime.datetime.strptime(current_time, "%Y-%m-%d")
    ).days
    return time_interval


@tool(
    description="基础天气查询工具（仅在智能天气工具失败时使用）- 查询指定地点和日期的基础天气信息",
    args_schema=WeatherInfoInput,
)
def get_weather_info(destination: str, time: str):
    """
    根据目的地和时间间隔获取天气信息
    """
    try:
        # 根据目的地获取adcode
        adcode = get_adcode(destination)
        # 根据adcode和时间间隔获取天气信息
        if adcode is not None:
            time_interval = get_time_interval(time)
            weather_info = get_weather_info_by_adcode(adcode, time_interval, time, destination)

            # 如果天气服务不可用，提供友好的回复
            if "天气服务暂时不可用" in weather_info or "HTTP状态码" in weather_info:
                return f"抱歉，天气服务暂时不可用，无法获取{destination}在{time}的天气信息。请稍后再试。"

            return weather_info
        else:
            return f"无法获取{destination}的地理位置信息，请检查地点名称是否正确"

    except Exception as e:
        logger.error(f"天气查询异常: {e}")
        return f"查询{destination}在{time}的天气信息时发生错误，请稍后再试。"


def get_weather_info_by_adcode(adcode: str, time_interval: int, time: str, destination: str):
    """
    根据adcode和时间间隔获取天气信息
    支持备选天气API：优先使用和风天气，失败时使用原有API
    """
    if time_interval == 0:
        # 使用统一的天气获取接口
        result = get_weather_with_fallback(adcode, destination, return_format="text")

        if result.get("success"):
            return result["data"]
        else:
            return result.get("error", f"获取{destination}天气信息失败")
    elif time_interval < 0:
        return "不能查询过去时间的天气！"
    else:
        # 根据环境使用不同的天气API
        if CURRENT_ENV == "test":
            url = "https://weather.ai.test.sankuai.com/weather/dailyWeather"
        else:
            url = "http://*************:8080/weather/dailyWeather"

        # 天气 API 的 days 参数含义：
        # days=1: 返回昨天
        # days=2: 返回昨天+今天
        # days=3: 返回昨天+今天+明天
        # 所以要查询未来的天气，需要 days = time_interval + 2
        api_days = time_interval + 2

        params = {"adcode": adcode, "days": api_days}

        try:
            logger.info(f"请求天气信息接口：{url}，参数：{params}")
            weather_response = requests.get(url, params=params, timeout=10)

            # 检查 HTTP 状态码
            logger.info(f"HTTP 状态码: {weather_response.status_code}")
            if weather_response.status_code != 200:
                logger.error(f"HTTP 请求失败，状态码: {weather_response.status_code}")
                return f"天气服务暂时不可用，HTTP状态码: {weather_response.status_code}"

            # 检查响应内容
            response_text = weather_response.text
            logger.info(f"原始响应内容: {response_text[:500]}...")  # 只记录前500字符

            if not response_text.strip():
                logger.error("天气 API 返回空响应")
                return "天气服务暂时不可用，返回空响应"

            # 尝试解析 JSON
            try:
                weather_json = weather_response.json()
            except json.JSONDecodeError as json_error:
                logger.error(f"JSON 解析失败: {json_error}, 响应内容: {response_text}")
                return "天气服务返回格式错误，无法解析数据"

            logger.info(f"获取天气信息查询结果：{weather_json}")

            # 检查 API 响应状态
            if "responseStatus" not in weather_json:
                logger.error("响应中缺少 responseStatus 字段")
                return "天气服务响应格式异常"

            if weather_json["responseStatus"]["code"] != 0:
                logger.error(f"获取天气信息失败: {weather_json['responseStatus']['msg']}")
                return f"获取天气信息失败: {weather_json['responseStatus']['msg']}"

            # 检查天气数据
            if "forecastWeather" not in weather_json or "dailyWeatherMap" not in weather_json["forecastWeather"]:
                logger.error("响应中缺少天气数据")
                return "天气服务返回数据不完整"

            daily_weather_map = weather_json["forecastWeather"]["dailyWeatherMap"]
            search_result = daily_weather_map.get(time, "")

            if not search_result:
                # 如果没有找到指定日期，列出可用的日期
                available_dates = list(daily_weather_map.keys())
                logger.info(f"可用的日期: {available_dates}")
                return f"没有找到 {time} 的天气信息！可用的日期有: {', '.join(available_dates)}"
            else:
                # 解析天气数据
                forecast_condition = search_result.get("forecastCondition", {})
                day_condition = forecast_condition.get("dayCondition", {})
                night_condition = forecast_condition.get("nightCondition", {})

                # 获取城市信息
                city_info = weather_json["forecastWeather"].get("city", {})
                city_name = city_info.get("districtName", destination)

                result_str = f"""{time}，{city_name}的天气预报：
白天：{day_condition.get("iconDesc", "未知")}，气温{day_condition.get("temperature", "未知")}°C，{day_condition.get("windDirectionDesc", "未知")}{day_condition.get("windLevel", "")}级
夜间：{night_condition.get("iconDesc", "未知")}，气温{night_condition.get("temperature", "未知")}°C，{night_condition.get("windDirectionDesc", "未知")}{night_condition.get("windLevel", "")}级
日出：{forecast_condition.get("sunRise", "未知").split(' ')[1] if forecast_condition.get("sunRise") else "未知"}，日落：{forecast_condition.get("sunSet", "未知").split(' ')[1] if forecast_condition.get("sunSet") else "未知"}"""

                return result_str
        except requests.exceptions.RequestException as req_error:
            logger.error(f"网络请求失败: {req_error}")
            return f"网络连接失败: {req_error}"
        except Exception as e:
            logger.error(f"获取天气信息失败: {e}")
            return f"获取天气信息失败: {e}"


def get_adcode(destination: str):
    """
    根据目的地名获取adcode，如果获取失败则使用默认的北京adcode
    """
    # 常用城市的adcode映射（兜底策略）
    # 注意：使用更具体的区域代码，避免"非法的城市请求"错误
    default_adcodes = {
        "北京": "110100",  # 北京市区（而不是110000）
        "上海": "310100",  # 上海市区
        "广州": "440100",
        "深圳": "440300",
        "杭州": "330100",
        "南京": "320100",
        "成都": "510100",
        "武汉": "420100",
        "西安": "610100",
        "重庆": "500100",  # 重庆市区
    }

    # 如果是常用城市，直接返回预设的adcode
    if destination in default_adcodes:
        adcode = default_adcodes[destination]
        logger.info(f"使用预设adcode: {destination} -> {adcode}")
        return adcode

    # 地理编码API需要根据环境使用不同的端点
    if CURRENT_ENV == "test":
        url = "http://lbsapi.map.test.sankuai.com/v1/search/text"
    else:
        url = "https://lbsapi.meituan.com/v1/search/text"

    # 从Lion配置中心获取API密钥，如果获取失败则使用默认值
    api_key = get_value("humanrelation.geocoding.api_key", "m86081a5071e4f8aac2aa3a03b74c04w")

    params = {"key": api_key, "keywords": destination}

    # 记录当前环境和使用的API端点
    masked_key = api_key[:8] + "***" + api_key[-4:] if len(api_key) > 12 else "***"
    logger.info(f"当前环境: {CURRENT_ENV}, 使用地理编码API: {url}, API密钥: {masked_key}, 查询关键词: {destination}")

    try:
        adcode_response = requests.get(url, params=params, timeout=10)

        # 记录请求详情
        logger.info(f"地理编码API请求: {url}, 参数: {params}, HTTP状态码: {adcode_response.status_code}")

        # 检查HTTP状态码
        if adcode_response.status_code != 200:
            logger.error(
                f"获取adcode HTTP请求失败，状态码: {adcode_response.status_code}, 响应内容: {adcode_response.text[:500]}"
            )
            return None

        # 检查响应内容是否为空
        response_text = adcode_response.text.strip()
        if not response_text:
            logger.error("获取adcode API返回空响应")
            return None

        # 尝试解析JSON
        try:
            adcode_json = adcode_response.json()
        except json.JSONDecodeError as json_error:
            logger.error(f"获取adcode JSON解析失败: {json_error}, 响应内容: {response_text[:500]}")
            return None

        logger.info(f"获取当前地址关键词的adcode查询结果：{adcode_json}")

        # 检查API响应状态
        if "status" not in adcode_json:
            logger.error(f"API响应缺少status字段: {adcode_json}")
            # 使用兜底策略
            fallback_adcode = "110100"  # 北京市区的adcode
            logger.info(f"API响应异常，使用兜底adcode: {destination} -> {fallback_adcode} (北京市区)")
            return fallback_adcode

        if adcode_json["status"] != 200:
            logger.error(
                f"获取adcode失败，API状态: {adcode_json.get('status')}, 消息: {adcode_json.get('message', '未知错误')}"
            )
            # 使用兜底策略
            fallback_adcode = "110100"  # 北京市区的adcode
            logger.info(f"API获取失败，使用兜底adcode: {destination} -> {fallback_adcode} (北京市区)")
            return fallback_adcode

        if not adcode_json.get("pois") or len(adcode_json["pois"]) == 0:
            logger.error(f"未找到地点 '{destination}' 的位置信息，API返回: {adcode_json}")
            # 使用兜底策略
            fallback_adcode = "110100"  # 北京市区的adcode
            logger.info(f"未找到地点信息，使用兜底adcode: {destination} -> {fallback_adcode} (北京市区)")
            return fallback_adcode

        adcode = adcode_json["pois"][0]["adcode"]
        logger.info(f"成功获取 '{destination}' 的adcode: {adcode}")
        return adcode
    except requests.exceptions.Timeout:
        logger.error(f"获取adcode请求超时: {url}")
        fallback_adcode = "110100"  # 北京市区的adcode
        logger.info(f"请求超时，使用兜底adcode: {destination} -> {fallback_adcode} (北京市区)")
        return fallback_adcode
    except requests.exceptions.ConnectionError:
        logger.error(f"获取adcode连接失败: {url}")
        fallback_adcode = "110100"  # 北京市区的adcode
        logger.info(f"连接失败，使用兜底adcode: {destination} -> {fallback_adcode} (北京市区)")
        return fallback_adcode
    except requests.exceptions.RequestException as req_error:
        logger.error(f"获取adcode网络请求失败: {req_error}")
        fallback_adcode = "110100"  # 北京市区的adcode
        logger.info(f"网络请求失败，使用兜底adcode: {destination} -> {fallback_adcode} (北京市区)")
        return fallback_adcode
    except Exception as e:
        logger.error(f"获取adcode失败: {e}")
        fallback_adcode = "110100"  # 北京市区的adcode
        logger.info(f"获取失败，使用兜底adcode: {destination} -> {fallback_adcode} (北京市区)")
        return fallback_adcode


# ==================== 和风天气API支持 ====================

# 和风天气API配置
QWEATHER_API_KEY = "a798b095fc7347939e6d8bd12f1feb91"
QWEATHER_BASE_URL = "https://devapi.qweather.com/v7"

# 城市代码映射（从adcode到和风天气location_id的映射）
ADCODE_TO_QWEATHER_LOCATION = {
    "110000": "101010100",  # 北京
    "310000": "101020100",  # 上海
    "120000": "101030100",  # 天津
    "500000": "101040100",  # 重庆
    "440100": "101280101",  # 广州
    "440300": "101280601",  # 深圳
    "330100": "101210101",  # 杭州
    "320100": "101190101",  # 南京
    "420100": "101200101",  # 武汉
    "510100": "101270101",  # 成都
    "610100": "101110101",  # 西安
    # 可以根据需要继续添加更多城市映射
}


def get_qweather_location_by_adcode(adcode: str) -> str:
    """
    根据adcode获取和风天气的location_id

    Args:
        adcode: 行政区划代码

    Returns:
        和风天气的location_id，如果没有找到则返回北京的ID
    """
    return ADCODE_TO_QWEATHER_LOCATION.get(adcode, "101010100")  # 默认返回北京


def get_qweather_current_weather(location_id: str) -> dict:
    """
    获取和风天气实时天气信息

    Args:
        location_id: 和风天气的城市代码

    Returns:
        天气信息字典
    """
    try:
        url = f"{QWEATHER_BASE_URL}/weather/now"
        params = {"location": location_id, "key": QWEATHER_API_KEY}
        headers = {"X-QW-Api-Key": QWEATHER_API_KEY}

        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == "200":
            now = data.get("now", {})
            return {
                "success": True,
                # 基础天气信息
                "temperature": now.get("temp", ""),
                "feelsLike": now.get("feelsLike", ""),
                "weather": now.get("text", ""),
                "weatherCode": now.get("icon", ""),  # 天气图标代码
                # 风力信息
                "windDirection": now.get("windDir", ""),
                "windSpeed": now.get("windSpeed", "") + "km/h" if now.get("windSpeed") else "",
                "windPower": now.get("windScale", "") + "级" if now.get("windScale") else "",
                "wind360": now.get("wind360", ""),  # 风向360角度
                # 大气信息
                "pressure": now.get("pressure", "") + "hPa" if now.get("pressure") else "",
                "humidity": now.get("humidity", "") + "%" if now.get("humidity") else "",
                "visibility": now.get("vis", "") + "km" if now.get("vis") else "",
                # 降水信息
                "precipitation": now.get("precip", "") + "mm" if now.get("precip") else "",  # 当前小时降水量
                # 云量和露点
                "cloud": now.get("cloud", "") + "%" if now.get("cloud") else "",  # 云量
                "dewPoint": now.get("dew", "") + "°C" if now.get("dew") else "",  # 露点温度
                # 时间信息
                "updateTime": now.get("obsTime", ""),
                # 原始数据（保留完整信息）
                "raw_data": now,
            }
        else:
            logger.error(f"和风天气API返回错误: {data.get('code')} - {data.get('refer', {}).get('sources', [])}")
            return {"success": False, "error": f"API错误: {data.get('code')}"}

    except Exception as e:
        logger.error(f"调用和风天气API失败: {e}")
        return {"success": False, "error": str(e)}


def get_qweather_daily_forecast(location_id: str, days: int = 3) -> dict:
    """
    获取和风天气天气预报

    Args:
        location_id: 和风天气的城市代码
        days: 预报天数，支持3天或7天

    Returns:
        天气预报信息字典
    """
    try:
        endpoint = "3d" if days <= 3 else "7d"
        url = f"{QWEATHER_BASE_URL}/weather/{endpoint}"
        params = {"location": location_id, "key": QWEATHER_API_KEY}
        headers = {"X-QW-Api-Key": QWEATHER_API_KEY}

        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == "200":
            daily = data.get("daily", [])
            forecast_list = []

            for day in daily:
                forecast_list.append(
                    {
                        # 基础信息
                        "date": day.get("fxDate", ""),
                        "tempMax": day.get("tempMax", ""),
                        "tempMin": day.get("tempMin", ""),
                        # 天气描述
                        "textDay": day.get("textDay", ""),
                        "textNight": day.get("textNight", ""),
                        "iconDay": day.get("iconDay", ""),  # 白天天气图标代码
                        "iconNight": day.get("iconNight", ""),  # 夜间天气图标代码
                        # 风力信息
                        "windDirDay": day.get("windDirDay", ""),
                        "windDirNight": day.get("windDirNight", ""),
                        "windScaleDay": day.get("windScaleDay", "") + "级" if day.get("windScaleDay") else "",
                        "windScaleNight": day.get("windScaleNight", "") + "级" if day.get("windScaleNight") else "",
                        "windSpeedDay": day.get("windSpeedDay", "") + "km/h" if day.get("windSpeedDay") else "",
                        "windSpeedNight": day.get("windSpeedNight", "") + "km/h" if day.get("windSpeedNight") else "",
                        "wind360Day": day.get("wind360Day", ""),  # 白天风向360角度
                        "wind360Night": day.get("wind360Night", ""),  # 夜间风向360角度
                        # 大气信息
                        "humidity": day.get("humidity", "") + "%" if day.get("humidity") else "",
                        "pressure": day.get("pressure", "") + "hPa" if day.get("pressure") else "",
                        "visibility": day.get("vis", "") + "km" if day.get("vis") else "",
                        # 降水信息
                        "precipitation": day.get("precip", "") + "mm" if day.get("precip") else "",  # 降水量
                        # 紫外线和云量
                        "uvIndex": day.get("uvIndex", ""),
                        "cloud": day.get("cloud", "") + "%" if day.get("cloud") else "",  # 云量
                        # 日出日落
                        "sunrise": day.get("sunrise", ""),
                        "sunset": day.get("sunset", ""),
                        "moonrise": day.get("moonrise", ""),
                        "moonset": day.get("moonset", ""),
                        "moonPhase": day.get("moonPhase", ""),  # 月相名称
                        "moonPhaseIcon": day.get("moonPhaseIcon", ""),  # 月相图标代码
                        # 原始数据（保留完整信息）
                        "raw_data": day,
                    }
                )

            return {"success": True, "forecast": forecast_list}
        else:
            logger.error(f"和风天气预报API返回错误: {data.get('code')}")
            return {"success": False, "error": f"API错误: {data.get('code')}"}

    except Exception as e:
        logger.error(f"调用和风天气预报API失败: {e}")
        return {"success": False, "error": str(e)}


def get_qweather_hourly_forecast(location_id: str, hours: int = 24) -> dict:
    """
    获取和风天气逐小时预报

    Args:
        location_id: 和风天气的城市代码
        hours: 预报小时数，支持24小时或72小时

    Returns:
        逐小时预报信息字典
    """
    try:
        endpoint = "24h" if hours <= 24 else "72h"
        url = f"{QWEATHER_BASE_URL}/weather/{endpoint}"
        params = {"location": location_id, "key": QWEATHER_API_KEY}
        headers = {"X-QW-Api-Key": QWEATHER_API_KEY}

        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == "200":
            hourly = data.get("hourly", [])
            hourly_list = []

            for hour in hourly:
                hourly_list.append(
                    {
                        # 基础信息
                        "fxTime": hour.get("fxTime", ""),  # 预报时间
                        "temp": hour.get("temp", ""),  # 温度
                        "feelsLike": hour.get("feelsLike", ""),  # 体感温度
                        "text": hour.get("text", ""),  # 天气状况
                        "icon": hour.get("icon", ""),  # 天气图标代码
                        # 风力信息
                        "windDir": hour.get("windDir", ""),  # 风向
                        "windScale": hour.get("windScale", "") + "级" if hour.get("windScale") else "",
                        "windSpeed": hour.get("windSpeed", "") + "km/h" if hour.get("windSpeed") else "",
                        "wind360": hour.get("wind360", ""),  # 风向360角度
                        # 大气信息
                        "humidity": hour.get("humidity", "") + "%" if hour.get("humidity") else "",
                        "pressure": hour.get("pressure", "") + "hPa" if hour.get("pressure") else "",
                        # 降水信息
                        "precipitation": hour.get("precip", "") + "mm" if hour.get("precip") else "",
                        "pop": hour.get("pop", "") + "%" if hour.get("pop") else "",  # 降水概率
                        # 云量和露点
                        "cloud": hour.get("cloud", "") + "%" if hour.get("cloud") else "",
                        "dewPoint": hour.get("dew", "") + "°C" if hour.get("dew") else "",
                        # 原始数据
                        "raw_data": hour,
                    }
                )

            return {"success": True, "hourly": hourly_list}
        else:
            logger.error(f"和风天气逐小时预报API返回错误: {data.get('code')}")
            return {"success": False, "error": f"API错误: {data.get('code')}"}

    except Exception as e:
        logger.error(f"调用和风天气逐小时预报API失败: {e}")
        return {"success": False, "error": str(e)}


def get_weather_with_fallback(adcode: str, location_name: str, return_format: str = "text") -> dict:
    """
    使用备选方案获取天气信息（统一接口）
    优先使用和风天气API，失败时使用原有API

    Args:
        adcode: 行政区划代码
        location_name: 地点名称
        return_format: 返回格式，"text"返回文本格式，"dict"返回字典格式

    Returns:
        天气信息（格式根据return_format决定）
    """
    try:
        # 方案1：尝试使用和风天气API
        logger.info(f"🌤️ 尝试使用和风天气API获取 {location_name} 天气")
        qweather_result = get_enhanced_weather_info_by_adcode_qweather(adcode)

        if qweather_result.get("result") == "success":
            weather_info = qweather_result.get("weather_info", {})
            current_weather = weather_info.get("current", {})

            if return_format == "text":
                # 返回文本格式（用于原有接口兼容）
                current_time = datetime.datetime.now().strftime("%Y-%m-%d")
                weather_text = _format_qweather_to_text(current_weather, location_name, current_time)
                logger.info(f"✅ {location_name} 天气获取成功 (来源: qweather_api)")
                return {"success": True, "data": weather_text, "source": "qweather_api"}
            else:
                # 返回字典格式（用于综合天气服务）
                unified_weather = {
                    "temperature": current_weather.get("temperature", ""),
                    "feelsLike": current_weather.get("feelsLike", ""),
                    "weather": current_weather.get("weather", ""),
                    "humidity": current_weather.get("humidity", ""),
                    "windDirection": current_weather.get("windDirection", ""),
                    "windSpeed": current_weather.get("windSpeed", ""),
                    "windPower": current_weather.get("windPower", ""),
                    "pressure": current_weather.get("pressure", ""),
                    "visibility": current_weather.get("visibility", ""),
                    "precipitation": current_weather.get("precipitation", ""),
                    "cloud": current_weather.get("cloud", ""),
                    "dewPoint": current_weather.get("dewPoint", ""),
                    "updateTime": current_weather.get("updateTime", ""),
                    "forecast": weather_info.get("forecast", []),
                    "hourly": weather_info.get("hourly", []),
                }
                logger.info(f"✅ {location_name} 天气获取成功 (来源: qweather_api)")
                return {"success": True, "data": unified_weather, "source": "qweather_api"}
        else:
            logger.warning(f"⚠️ 和风天气API失败: {qweather_result.get('reason')}")

    except Exception as e:
        logger.warning(f"⚠️ 和风天气API异常: {e}")

    # 方案2：使用原有天气API
    try:
        logger.info(f"🌤️ 使用原有天气API获取 {location_name} 天气")
        current_time = datetime.datetime.now().strftime("%Y-%m-%d")

        if return_format == "text":
            # 直接调用原有的文本格式接口
            weather_result = _get_original_weather_api(adcode, location_name)
            if weather_result.get("success"):
                return {"success": True, "data": weather_result["data"], "source": "original_api"}
            else:
                return {"success": False, "error": weather_result.get("error", "原有API获取失败")}
        else:
            # 调用原有API并解析为字典格式
            weather_result = _get_original_weather_api(adcode, location_name)
            if weather_result.get("success"):
                # 解析天气信息字符串，提取关键信息
                weather_info = _parse_weather_string(weather_result["data"])
                weather_info["weather_text"] = weather_result["data"]  # 保留原始文本
                return {"success": True, "data": weather_info, "source": "original_api"}
            else:
                return {"success": False, "error": weather_result.get("error", "原有API获取失败")}

    except Exception as e:
        logger.error(f"❌ 原有天气API异常: {e}")
        return {"success": False, "error": f"所有天气API都失败: {str(e)}"}


def get_enhanced_weather_info_by_adcode_qweather(adcode: str) -> dict:
    """
    使用和风天气API获取增强的天气信息

    Args:
        adcode: 行政区划代码

    Returns:
        包含实时天气和预报的完整天气信息
    """
    try:
        # 获取和风天气的location_id
        location_id = get_qweather_location_by_adcode(adcode)

        # 获取实时天气
        current_weather = get_qweather_current_weather(location_id)
        if not current_weather.get("success"):
            return {"result": "error", "reason": f"获取实时天气失败: {current_weather.get('error')}"}

        # 获取3天预报
        forecast = get_qweather_daily_forecast(location_id, 3)
        if not forecast.get("success"):
            logger.warning(f"获取天气预报失败: {forecast.get('error')}")
            forecast = {"forecast": []}

        # 获取24小时预报
        hourly_forecast = get_qweather_hourly_forecast(location_id, 24)
        if not hourly_forecast.get("success"):
            logger.warning(f"获取逐小时预报失败: {hourly_forecast.get('error')}")
            hourly_forecast = {"hourly": []}

        # 组合结果
        weather_info = {
            "current": current_weather,
            "forecast": forecast.get("forecast", []),
            "hourly": hourly_forecast.get("hourly", []),
            "location_id": location_id,
            "adcode": adcode,
        }

        return {"result": "success", "weather_info": weather_info}

    except Exception as e:
        logger.error(f"获取和风天气信息失败: {e}")
        return {"result": "error", "reason": str(e)}


def _format_qweather_to_text(current_weather: dict, destination: str, time: str) -> str:
    """
    将和风天气API数据格式化为原有API的文本格式

    Args:
        current_weather: 和风天气的当前天气数据
        destination: 目的地名称
        time: 查询时间

    Returns:
        格式化的天气文本
    """
    try:
        temperature = current_weather.get("temperature", "")
        feels_like = current_weather.get("feelsLike", "")
        weather_desc = current_weather.get("weather", "")
        humidity = current_weather.get("humidity", "")
        wind_dir = current_weather.get("windDirection", "")
        wind_power = current_weather.get("windPower", "")
        pressure = current_weather.get("pressure", "")
        visibility = current_weather.get("visibility", "")
        update_time = current_weather.get("updateTime", "")

        # 构建天气描述文本，模仿原有API格式
        weather_text = f"{destination}今天的天气情况如下：\n"
        weather_text += f"天气：{weather_desc}\n"
        weather_text += f"温度：{temperature}°C"

        if feels_like and feels_like != temperature:
            weather_text += f"（体感温度：{feels_like}°C）"
        weather_text += "\n"

        if humidity:
            weather_text += f"湿度：{humidity}\n"

        if wind_dir and wind_power:
            weather_text += f"风向风力：{wind_dir}{wind_power}\n"
        elif wind_dir:
            weather_text += f"风向：{wind_dir}\n"
        elif wind_power:
            weather_text += f"风力：{wind_power}\n"

        if pressure:
            weather_text += f"气压：{pressure}\n"

        if visibility:
            weather_text += f"能见度：{visibility}\n"

        # 添加更多详细信息
        precipitation = current_weather.get("precipitation", "")
        if precipitation and precipitation != "0mm":
            weather_text += f"降水量：{precipitation}\n"

        cloud = current_weather.get("cloud", "")
        if cloud:
            weather_text += f"云量：{cloud}\n"

        dew_point = current_weather.get("dewPoint", "")
        if dew_point:
            weather_text += f"露点温度：{dew_point}\n"

        if update_time:
            weather_text += f"数据更新时间：{update_time}\n"

        weather_text += f"\n温馨提示：以上是{destination}在{time}的天气信息，请根据实际情况合理安排出行。"

        return weather_text.strip()

    except Exception as e:
        logger.error(f"格式化和风天气数据失败: {e}")
        return f"{destination}的天气信息获取成功，但格式化时出现问题。温度：{current_weather.get('temperature', '未知')}°C，天气：{current_weather.get('weather', '未知')}"


def _get_original_weather_api(adcode: str, location_name: str) -> dict:
    """
    调用原有天气API获取天气信息

    Args:
        adcode: 行政区划代码
        location_name: 地点名称

    Returns:
        包含success和data/error的字典
    """
    try:
        # 根据环境使用不同的天气API
        # 注意：测试环境的API不稳定，统一使用生产环境API
        if CURRENT_ENV == "test":
            url = "http://*************:8080/weather/realtimeWeather"  # 使用生产环境API
        else:
            url = "http://*************:8080/weather/realtimeWeather"
        params = {"adcode": adcode}

        logger.info(f"请求天气信息接口：{url}，参数：{params}")
        weather_response = requests.get(url, params=params, timeout=10)

        # 检查HTTP状态码
        logger.info(f"HTTP 状态码: {weather_response.status_code}")
        if weather_response.status_code != 200:
            logger.error(f"HTTP 请求失败，状态码: {weather_response.status_code}")
            return {"success": False, "error": f"天气服务暂时不可用，HTTP状态码: {weather_response.status_code}"}

        # 检查响应内容是否为空
        response_text = weather_response.text.strip()
        if not response_text:
            logger.error("天气 API 返回空响应")
            return {"success": False, "error": "天气服务暂时不可用，返回空响应"}

        # 尝试解析 JSON
        try:
            weather_json = weather_response.json()
        except json.JSONDecodeError as json_error:
            logger.error(f"JSON 解析失败: {json_error}, 响应内容: {response_text}")
            return {"success": False, "error": "天气服务返回格式错误，无法解析数据"}

        logger.info(f"获取天气信息查询结果：{weather_json}")
        if weather_json["responseStatus"]["code"] != 0:
            logger.error(f"获取天气信息失败: {weather_json['responseStatus']['msg']}")
            return {"success": False, "error": f"获取天气信息失败: {weather_json['responseStatus']['msg']}"}

        search_result = weather_json.get("realTimeWeather", {})
        if not search_result:
            logger.error("天气信息为空")
            return {"success": False, "error": "天气信息为空"}

        # 构建天气信息文本
        weather_text = f"{location_name}今天的天气情况如下：\n"
        weather_text += f"天气：{search_result.get('weather', '未知')}\n"
        weather_text += f"温度：{search_result.get('temperature', '未知')}°C\n"
        weather_text += f"湿度：{search_result.get('humidity', '未知')}\n"
        weather_text += (
            f"风向风力：{search_result.get('windDirection', '未知')}{search_result.get('windPower', '未知')}\n"
        )
        weather_text += f"\n温馨提示：以上是{location_name}的天气信息，请根据实际情况合理安排出行。"

        return {"success": True, "data": weather_text}

    except Exception as e:
        logger.error(f"调用原有天气API失败: {e}")
        return {"success": False, "error": f"获取天气信息时发生错误: {str(e)}"}


def _parse_weather_string(weather_text: str) -> dict:
    """
    解析天气信息字符串，提取关键信息

    Args:
        weather_text: 天气信息文本

    Returns:
        解析后的天气信息字典
    """
    try:
        import re

        weather_info = {}

        # 提取温度
        temp_match = re.search(r"温度：(\d+)°C", weather_text)
        if temp_match:
            weather_info["temperature"] = temp_match.group(1)

        # 提取天气描述
        weather_match = re.search(r"天气：([^\n]+)", weather_text)
        if weather_match:
            weather_info["weather"] = weather_match.group(1)

        # 提取湿度
        humidity_match = re.search(r"湿度：([^\n]+)", weather_text)
        if humidity_match:
            weather_info["humidity"] = humidity_match.group(1)

        # 提取风向风力
        wind_match = re.search(r"风向风力：([^\n]+)", weather_text)
        if wind_match:
            wind_info = wind_match.group(1)
            weather_info["windDirection"] = wind_info
            weather_info["windPower"] = wind_info

        return weather_info

    except Exception as e:
        logger.error(f"解析天气信息字符串失败: {e}")
        return {}


class SmartWeatherInput(BaseModel):
    query: str = Field(description="天气查询内容，如'今天北京天气如何'、'天气'、'上海明天天气'等任何天气相关问题")


@tool(
    description="智能天气查询工具 - 获取天气数据和个性化分析，AI需要基于返回的数据生成用户友好的回答",
    args_schema=SmartWeatherInput,
)
def get_smart_weather_info(query: str):
    """
    智能天气查询工具，结合用户个人信息提供个性化天气提醒
    当用户询问天气时，会自动获取用户的综合天气信息，包括：
    1. 用户常用地址的天气
    2. 即将发生事件地址的天气
    3. 直属亲属地址的天气
    4. 老板地址的天气
    5. AI生成的个性化天气提醒
    """
    try:
        # 获取当前用户ID
        user_id = current_user_id.get()
        if not user_id:
            return "抱歉，无法获取用户信息，请重新登录后再试。"

        logger.info(f"🌤️ 用户 {user_id} 请求智能天气信息: {query}")

        # 解析用户查询，提取地点信息
        from datetime import datetime, timedelta

        # 尝试从查询中提取地点
        extracted_location = None

        # 常见城市匹配
        cities = [
            "北京",
            "上海",
            "广州",
            "深圳",
            "杭州",
            "南京",
            "武汉",
            "成都",
            "重庆",
            "西安",
            "天津",
            "苏州",
            "长沙",
            "郑州",
            "青岛",
            "大连",
            "宁波",
            "厦门",
            "福州",
            "沈阳",
            "石家庄",
            "长春",
            "哈尔滨",
            "济南",
            "合肥",
            "南昌",
            "太原",
            "昆明",
            "贵阳",
            "南宁",
            "海口",
            "三亚",
            "拉萨",
            "银川",
            "西宁",
            "乌鲁木齐",
            "呼和浩特",
        ]

        for city in cities:
            if city in query:
                extracted_location = city
                break

        # 如果用户指定了具体地点，优先查询该地点
        if extracted_location:
            logger.info(f"🎯 从查询中提取到地点: {extracted_location}")

            # 解析时间
            today = datetime.now().strftime("%Y-%m-%d")
            if "明天" in query or "明日" in query:
                target_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
            elif "后天" in query:
                target_date = (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d")
            else:
                target_date = today

            # 获取详细的天气数据（字典格式）
            try:
                adcode = get_adcode(extracted_location)
                weather_result = get_weather_with_fallback(adcode, extracted_location, return_format="dict")

                if weather_result.get("success"):
                    weather_data_dict = weather_result.get("data", {})
                    # 构建详细的天气信息
                    basic_weather = _format_detailed_weather_info(weather_data_dict, extracted_location)
                else:
                    # 如果字典格式失败，尝试文本格式
                    basic_weather = get_weather_with_fallback(adcode, extracted_location, return_format="text")
                    if isinstance(basic_weather, dict) and not basic_weather.get("success"):
                        basic_weather = f"抱歉，无法获取{extracted_location}的天气信息，请稍后再试。"
                    elif isinstance(basic_weather, dict):
                        basic_weather = basic_weather.get(
                            "data", f"抱歉，无法获取{extracted_location}的天气信息，请稍后再试。"
                        )
            except Exception as e:
                logger.warning(f"调用天气查询函数失败: {e}")
                basic_weather = f"抱歉，无法获取{extracted_location}的天气信息，请稍后再试。"

            # 为指定地点生成个性化提醒
            from service.comprehensive_weather_service import ComprehensiveWeatherService

            weather_service = ComprehensiveWeatherService()

            # 获取指定地点的天气数据用于生成个性化提醒
            try:
                logger.info(f"🤖 开始为{extracted_location}生成个性化提醒")

                # 获取指定地点的详细天气数据（字典格式用于个性化提醒）
                adcode = get_adcode(extracted_location)
                weather_data = get_weather_with_fallback(adcode, extracted_location, return_format="dict")

                logger.info(
                    f"🌤️ {extracted_location}天气数据获取结果: {weather_data.get('success') if weather_data else 'None'}"
                )

                if weather_data and weather_data.get("success"):
                    # 为指定地点生成个性化AI提醒
                    ai_reminder = weather_service._generate_personalized_reminder_for_location(
                        user_id, extracted_location, weather_data
                    )
                    logger.info(f"🎯 {extracted_location}个性化提醒生成结果: {'成功' if ai_reminder else '失败'}")
                else:
                    logger.warning(f"⚠️ {extracted_location}天气数据获取失败，跳过个性化提醒")
                    ai_reminder = ""
            except Exception as e:
                logger.error(f"❌ 为{extracted_location}生成个性化提醒失败: {e}")
                import traceback

                logger.error(f"详细错误: {traceback.format_exc()}")
                ai_reminder = ""

            # 返回完整的详细天气数据
            if isinstance(basic_weather, str) and "温馨提示：" in basic_weather:
                # 如果是文本格式，移除温馨提示部分
                weather_data = basic_weather.split("温馨提示：")[0].strip()
            else:
                # 如果是详细格式化的天气信息，直接使用
                weather_data = basic_weather

            # 获取用户基本信息用于AI生成回答
            from service.comprehensive_weather_service import ComprehensiveWeatherService

            weather_service = ComprehensiveWeatherService()
            user_info = weather_service._get_user_basic_info(user_id)

            # 构建用户档案摘要
            user_summary = "暂无用户信息"
            if user_info:
                key_attrs = user_info.get("key_attributes", {})
                basic_info = key_attrs.get("基本信息", {}) if isinstance(key_attrs, dict) else {}
                family_info = basic_info.get("家庭情况", {}) if isinstance(basic_info, dict) else {}

                # 安全地计算年龄
                birth_year_str = basic_info.get("生日", "1985年")
                try:
                    if birth_year_str and "年" in birth_year_str:
                        birth_year = int(birth_year_str.replace("年", ""))
                        age = 2025 - birth_year
                    else:
                        age = "未知"
                except (ValueError, TypeError):
                    age = "未知"

                user_summary = f"""用户档案：
- 姓名：{user_info.get('canonical_name', '未知')}
- 年龄：{age}岁
- 家乡：{basic_info.get('家乡', '未知')}，现居：{basic_info.get('当前城市', '未知')}
- 家庭：配偶{family_info.get('配偶姓名', '未知')}，{family_info.get('子女信息', ['未知'])}
- 职业：技术专家"""

            # 获取近期事件摘要
            recent_events = weather_service._get_user_recent_events_and_reminders(user_id)
            events_summary = "暂无近期事件"
            if recent_events and "北京正在下暴雨" in recent_events:
                events_summary = "近期事件：北京正在下暴雨，用户准备去上海"

            # 返回天气数据、用户信息和AI指令
            return f"""天气数据：
{weather_data}

{user_summary}

{events_summary}

{get_value('weather.ai_instruction', '请基于以上天气数据和用户信息，为用户生成个性化的天气回答。要求：1. 结合用户的具体情况（年龄、家庭、居住地等）2. 关注家人朋友的安全（特别是配偶和孩子）3. 考虑近期事件的影响4. 语言温暖贴心，像家人朋友一样关心5. 提供实用的出行和健康建议')}"""

        else:
            # 没有指定地点，使用综合天气服务
            from service.comprehensive_weather_service import ComprehensiveWeatherService

            weather_service = ComprehensiveWeatherService()
            result = weather_service.get_comprehensive_weather_info(user_id)

        if result.get("result") == "success":
            # 提取AI个性化提醒
            ai_reminder = result.get("ai_reminder", "")
            weather_summary = result.get("weather_summary", {})
            locations = result.get("locations", [])

            # 构建回复
            response_parts = []

            # 添加天气概况
            total_locations = weather_summary.get("total_locations", 0)
            successful_queries = weather_summary.get("successful_queries", 0)

            if total_locations > 0:
                response_parts.append(
                    f"📍 为您查询了 {total_locations} 个相关地点的天气，成功获取 {successful_queries} 个地点的信息。"
                )

                # 列出查询的地点
                location_names = [loc.get("name", "未知地点") for loc in locations]
                if location_names:
                    response_parts.append(f"涉及地点：{', '.join(location_names)}")

            # 添加AI个性化提醒（这是最重要的部分）
            if ai_reminder:
                response_parts.append(f"\n🤖 个性化天气提醒：\n{ai_reminder}")
            else:
                response_parts.append("\n暂时无法生成个性化提醒，但天气数据已成功获取。")

            return "\n".join(response_parts)

        else:
            error_reason = result.get("reason", "未知错误")
            return f"抱歉，获取天气信息失败：{error_reason}。请稍后再试或检查您的个人档案中是否设置了地点信息。"

    except Exception as e:
        logger.error(f"智能天气查询异常: {e}")
        return "抱歉，天气查询过程中发生错误，请稍后再试。如果问题持续存在，请联系技术支持。"


def _format_detailed_weather_info(weather_data: dict, location: str) -> str:
    """
    格式化详细的天气信息

    Args:
        weather_data: 天气数据字典
        location: 地点名称

    Returns:
        格式化的天气信息字符串
    """
    try:
        # 基础天气信息
        temperature = weather_data.get("temperature", "未知")
        feels_like = weather_data.get("feelsLike", "未知")
        weather = weather_data.get("weather", "未知")
        humidity = weather_data.get("humidity", "未知")

        # 风力信息
        wind_direction = weather_data.get("windDirection", "未知")
        wind_power = weather_data.get("windPower", "未知")
        wind_speed = weather_data.get("windSpeed", "")

        # 大气信息
        pressure = weather_data.get("pressure", "未知")
        visibility = weather_data.get("visibility", "未知")
        precipitation = weather_data.get("precipitation", "未知")
        cloud = weather_data.get("cloud", "")
        dew_point = weather_data.get("dewPoint", "")

        # 预报信息
        forecast = weather_data.get("forecast", [])
        hourly = weather_data.get("hourly", [])

        # 构建详细天气信息
        weather_info = f"""{location}今天的详细天气情况：

🌡️ 温度信息：
- 当前温度：{temperature}°C
- 体感温度：{feels_like}°C
- 天气状况：{weather}
- 空气湿度：{humidity}

💨 风力信息：
- 风向风力：{wind_direction}{wind_power}"""

        if wind_speed:
            weather_info += f"\n- 风速：{wind_speed}"

        weather_info += f"""

🌍 大气信息：
- 气压：{pressure}
- 能见度：{visibility}
- 降水量：{precipitation}"""

        if cloud:
            weather_info += f"\n- 云量：{cloud}"
        if dew_point:
            weather_info += f"\n- 露点：{dew_point}"

        # 添加预报信息
        if forecast and len(forecast) > 1:
            tomorrow = forecast[1]
            temp_max = tomorrow.get("tempMax", "未知")
            temp_min = tomorrow.get("tempMin", "未知")
            text_day = tomorrow.get("textDay", "未知")
            weather_info += f"""

📅 明天预报：
- 温度范围：{temp_min}°C - {temp_max}°C
- 天气状况：{text_day}"""

        # 添加今日逐小时预报（接下来6小时）
        if hourly:
            weather_info += "\n\n⏰ 今日逐小时预报（接下来6小时）："
            for hour in hourly[:6]:
                fx_time = hour.get("fxTime", "")
                temp = hour.get("temp", "")
                text = hour.get("text", "")
                pop = hour.get("pop", "")
                if fx_time and temp:
                    # 提取时间部分（如：15:00）
                    time_part = fx_time.split("T")[1][:5] if "T" in fx_time else fx_time
                    weather_info += f"\n- {time_part}: {temp}°C, {text}"
                    if pop:
                        weather_info += f", 降水概率{pop}"

        return weather_info

    except Exception as e:
        logger.error(f"格式化天气信息失败: {e}")
        return f"{location}天气信息格式化失败，请稍后再试。"


# if __name__ == "__main__":
#     print(get_weather_info("北京", "2025-06-05"))
