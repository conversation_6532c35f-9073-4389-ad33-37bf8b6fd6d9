import contextvars
from typing import Optional

from langchain_core.tools import tool
from service.mysql_person_service import logger

# 全局上下文变量，用于存储当前用户ID
current_user_id = contextvars.ContextVar("current_user_id", default=None)


def _get_user_phone_by_user_id(user_id: str) -> Optional[str]:
    """
    根据 user_id 获取用户手机号（从 person_memory 表的 key_attributes 字段）。
    查找所有 is_user 为 true 的记录，只要有一条有手机号就返回。
    """
    try:
        from my_mysql import sql_client
        from my_mysql.entity.person_table import person_memory
        from sqlalchemy import select

        stmt = select(person_memory).where(person_memory.c.user_id == user_id, person_memory.c.is_user == True)
        results = sql_client.select_many(stmt)
        if not results:
            return None

        for row in results:
            person = dict(row._mapping)
            key_attributes = person.get("key_attributes")
            if not key_attributes:
                continue

            # 解析 json
            if isinstance(key_attributes, str):
                import json

                key_attributes = json.loads(key_attributes)

            # 方法1: 优先从新的标准化结构中提取手机号（兼容reminder-api-fix分支的简洁实现）
            if isinstance(key_attributes, dict) and "基本信息" in key_attributes:
                basic_info = key_attributes.get("基本信息", {})
                if isinstance(basic_info, dict):
                    contact_info = basic_info.get("联系方式", {})
                    if isinstance(contact_info, dict):
                        phone = contact_info.get("电话")
                        if phone and str(phone).strip():
                            phone = str(phone).strip()
                            logger.info(f"从标准化结构中找到手机号: {phone}")
                            return phone

            # 方法2: 兼容旧的扁平结构（保留原分支的完善逻辑）
            for key in ["phone", "手机号", "mobile", "电话", "手机号码", "电话号码", "电话号"]:
                if key in key_attributes and key_attributes[key]:
                    phone = str(key_attributes[key]).strip()
                    if phone:
                        logger.info(f"从扁平结构中找到手机号: {key} = {phone}")
                        return phone

            # 方法3: 递归查找（保留原分支的高级功能）
            def find_phone_recursive(data, prefix=""):
                if isinstance(data, dict):
                    for key, value in data.items():
                        # 如果值是字符串且不为空，检查是否为手机号字段
                        if isinstance(value, str) and value.strip():
                            # 兼容多种手机号字段名
                            phone_keywords = ["phone", "手机号", "mobile", "电话", "手机号码", "电话号码", "电话号"]
                            for keyword in phone_keywords:
                                if keyword.lower() in key.lower() or keyword in key:
                                    phone = value.strip()
                                    logger.info(f"从递归查找中找到手机号: {key} = {phone}")
                                    return phone

                        # 如果值是字典，递归处理
                        elif isinstance(value, dict):
                            result = find_phone_recursive(value, f"{prefix}.{key}" if prefix else key)
                            if result:
                                return result
                return None

            # 执行递归查找作为最后的兜底
            phone = find_phone_recursive(key_attributes)
            if phone:
                return phone
        return None
    except Exception as e:
        logger.error(f"获取手机号异常: {e}")
        return None


# 工具函数，供大模型调用
@tool("get_user_phone", return_direct=True)
def get_user_phone_tool():
    """
    查询当前用户的手机号。
    """
    try:
        # 从全局上下文变量获取用户ID
        user_id = current_user_id.get()

        if not user_id:
            return "❌ 系统错误：无法获取用户身份信息"

        phone = _get_user_phone_by_user_id(user_id)
        if phone:
            return f"您的手机号是：{phone}"
        else:
            return "未找到您的手机号信息。"
    except Exception as e:
        logger.error(f"获取用户手机号工具执行异常: {e}")
        return "❌ 获取手机号时发生错误"
