import os
import random
import sys
from typing import Any, Dict, List, Literal, Optional, Tuple

# 动态获取app目录路径，支持不同环境
current_dir = os.path.dirname(os.path.abspath(__file__))  # tools目录
app_dir = os.path.dirname(current_dir)  # app目录
sys.path.append(app_dir)
import hashlib
import json

# 移除 lxml 依赖，使用 BeautifulSoup 的内置解析器
# from lxml.html import fromstring
import re
import socket
import time
import urllib.parse
from concurrent.futures import ThreadPoolExecutor, as_completed
from socket import inet_aton
from struct import unpack
from urllib.parse import urlparse

import psutil
import requests
from bs4 import BeautifulSoup
from configs.lion_config import get_value
from langchain_core.callbacks import CallbackManagerForToolRun
from langchain_core.messages import ToolMessage
from langchain_core.tools import BaseTool, tool
from langchain_core.utils import get_from_dict_or_env
from pydantic import BaseModel, ConfigDict, Field, model_validator
from typing_extensions import override
from utils.logger import logger

DEFAULT_BING_SEARCH_ENDPOINT = "https://aigc.sankuai.com/v1/friday/api/search"
DEFAULT_APPID = "1872462822987792398"
DEFAULT_ENGINE_NAME = "bing-search"


# 搜索缓存类
class LocalSearchCache:
    def __init__(self, max_size=100, ttl_seconds=300):  # 5分钟缓存
        self.cache = {}
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds

    def _generate_key(self, query: str) -> str:
        return hashlib.md5(query.encode()).hexdigest()

    def get(self, query: str) -> Any:
        key = self._generate_key(query)
        if key in self.cache:
            data, timestamp = self.cache[key]
            if time.time() - timestamp < self.ttl_seconds:
                return data
            else:
                del self.cache[key]  # 过期删除
        return None

    def set(self, query: str, data: Any):
        key = self._generate_key(query)

        # 清理过期缓存
        self._cleanup_expired()

        # 如果缓存满了，删除最旧的
        if len(self.cache) >= self.max_size:
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][1])
            del self.cache[oldest_key]

        self.cache[key] = (data, time.time())

    def _cleanup_expired(self):
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in self.cache.items() if current_time - timestamp >= self.ttl_seconds
        ]
        for key in expired_keys:
            del self.cache[key]


# 超时和熔断管理器
class SearchTimeoutManager:
    def __init__(self):
        self.failure_count = {}
        self.last_failure_time = {}
        self.circuit_breaker_threshold = 3  # 连续失败3次后熔断
        self.circuit_breaker_timeout = 60  # 熔断60秒

    def is_circuit_open(self, engine_name: str) -> bool:
        """检查熔断器是否打开"""
        if engine_name in self.failure_count:
            if (
                self.failure_count[engine_name] >= self.circuit_breaker_threshold
                and time.time() - self.last_failure_time.get(engine_name, 0) < self.circuit_breaker_timeout
            ):
                return True
        return False

    def record_failure(self, engine_name: str):
        """记录失败"""
        self.failure_count[engine_name] = self.failure_count.get(engine_name, 0) + 1
        self.last_failure_time[engine_name] = time.time()

    def record_success(self, engine_name: str):
        """记录成功"""
        if engine_name in self.failure_count:
            self.failure_count[engine_name] = 0


# 全局实例
search_cache = LocalSearchCache()
timeout_manager = SearchTimeoutManager()


# 智能结果筛选器
def intelligent_result_filter(query: str, search_results: List[Dict]) -> List[Dict]:
    """基于查询内容智能筛选搜索结果"""
    try:
        import jieba

        # 1. 提取查询关键词
        query_keywords = [word.lower() for word in jieba.cut(query) if len(word) > 1]

        # 2. 计算相关性得分
        def calculate_relevance_score(result, keywords):
            title = result.get("title", "").lower()
            snippet = result.get("snippet", "").lower()

            score = 0
            for keyword in keywords:
                if keyword in title:
                    score += 3  # 标题匹配权重高
                elif keyword in snippet:
                    score += 1  # 摘要匹配权重低

            # 时效性加分
            time_keywords = ["今天", "昨天", "最新", "刚刚", "今日", "新闻"]
            for time_word in time_keywords:
                if time_word in title or time_word in snippet:
                    score += 2

            return score

        # 3. 计算得分并排序
        scored_results = []
        for result in search_results:
            score = calculate_relevance_score(result, query_keywords)
            if score > 0:  # 只保留有相关性的结果
                result["relevance_score"] = score
                scored_results.append(result)

        # 4. 按得分排序，只返回前6个最相关的
        scored_results.sort(key=lambda x: x["relevance_score"], reverse=True)
        filtered_results = scored_results[:6]

        return filtered_results

    except Exception as e:
        logger.debug(f"智能筛选失败: {e}，返回原始结果")
        return search_results[:6]  # 降级处理


# 获取优化的爬虫工作线程数
def get_optimal_crawl_workers(result_count: int) -> int:
    """动态计算最优爬虫线程数"""
    try:
        cpu_count = psutil.cpu_count()
        return min(max(result_count // 2, 2), cpu_count, 6)  # 最多6个线程
    except:
        return min(result_count, 4)  # 降级处理


# 带超时的搜索引擎调用
def call_search_engine_with_timeout(engine_name: str, query: str, timeout: int = 6) -> List[Dict]:
    """带超时和熔断的搜索引擎调用"""

    # 检查熔断器
    if timeout_manager.is_circuit_open(engine_name):
        return []

    try:
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(searchToolDict[engine_name]._run, query)
            content, artifact = future.result(timeout=timeout)

            timeout_manager.record_success(engine_name)
            return artifact if artifact else []

    except Exception as e:
        timeout_manager.record_failure(engine_name)
        logger.debug(f"❌ {engine_name} 调用失败: {str(e)}")
        return []


"""Tool for the Bing search API."""


# 改自BingSearchAPIWrapper，因此里面的内容都以Bing命名，但是实际上起功能不仅限于Bing
class SearchAPIWrapper(BaseModel):
    """Wrapper for Bing Web Search API."""

    bing_subscription_key: str = DEFAULT_APPID  # 申请到的appId
    bing_search_url: str = DEFAULT_BING_SEARCH_ENDPOINT  # Friday搜索网址
    k: int = 10
    search_engine_name: str = DEFAULT_ENGINE_NAME
    search_kwargs: dict = Field(default_factory=dict)
    """Additional keyword arguments to pass to the search request."""

    model_config = ConfigDict(
        extra="forbid",
    )

    def _bing_search_results(self, search_term: str, count: int) -> List[dict]:
        # print("teststststs")
        headers = {"Authorization": f"Bearer {self.bing_subscription_key}", "Content-Type": "application/json"}
        params = {
            "query": search_term,
            "api": self.search_engine_name if self.search_engine_name else DEFAULT_ENGINE_NAME,
            "top_k": count,
            **self.search_kwargs,
        }
        response = requests.post(
            self.bing_search_url,
            headers=headers,
            json=params,
        )
        response.raise_for_status()
        search_results = response.json()
        if self.search_engine_name == "google-search":
            if "googleItems" in search_results:
                return search_results["googleItems"]
        elif self.search_engine_name == "bing-search":
            if "results" in search_results:
                return search_results["results"]
        # elif self.search_engine_name == "baidu-search": # 目前有问题
        #     if "results" in search_results:
        #         return search_results["results"]
        elif self.search_engine_name == "tencent-search":
            if "tencentSearchResults" in search_results:
                return list(map(lambda x: json.loads(x), search_results["tencentSearchResults"]["Response"]["Pages"]))
        elif self.search_engine_name == "wenxin-search":
            if "results" in search_results:
                return search_results["results"]
        elif self.search_engine_name == "bing-search-pro":
            if "bingSearchProResults" in search_results:
                return search_results["bingSearchProResults"]["webPages"]["value"]
        else:
            print(f"不支持的搜索引擎: {self.search_engine_name}")
        return []

    @model_validator(mode="before")
    @classmethod
    def validate_environment(cls, values: Dict) -> Any:
        """Validate that api key and endpoint exists in environment."""
        bing_subscription_key = get_from_dict_or_env(
            values, "bing_subscription_key", "BING_SUBSCRIPTION_KEY", default=DEFAULT_APPID
        )
        values["bing_subscription_key"] = bing_subscription_key

        bing_search_url = get_from_dict_or_env(
            values,
            "bing_search_url",
            "BING_SEARCH_URL",
            default=DEFAULT_BING_SEARCH_ENDPOINT,
        )

        values["bing_search_url"] = bing_search_url

        return values

    @override
    def run(self, query: str) -> str:
        """Run query through BingSearch and parse result."""
        print("Heloo")
        snippets = []
        results = self._bing_search_results(query, count=self.k)
        if len(results) == 0:
            return "No good Bing Search Result was found"
        for result in results:
            snippets.append(result["snippet"])

        return snippets

    def results(self, query: str, num_results: int) -> List[Dict]:
        """Run query through BingSearch and return metadata.

        Args:
            query: The query to search for.
            num_results: The number of results to return.

        Returns:
            A list of dictionaries with the following keys:
                snippet - The description of the result.
                title - The title of the result.
                link - The link to the result.
        """
        metadata_results = []
        results = self._bing_search_results(query, count=num_results)
        if len(results) == 0:
            return [{"Result": "No good Bing Search Result was found"}]
        for result in results:
            metadata_result = {
                "snippet": result.get("snippet", None) or result.get("content", None) or result.get("passage", None),
                "title": result.get("name", None) or result.get("title", None),
                "link": result.get("url", None) or result.get("link", None),
                "images": result.get("images", []),
            }
            metadata_results.append(metadata_result)
        return metadata_results


class SearchRun(BaseTool):
    """Tool that queries the Bing search API."""

    name: str = "bing_search"
    description: str = (
        "A wrapper around Bing Search. "
        "Useful for when you need to answer questions about current events. "
        "Input should be a search query."
    )
    api_wrapper: SearchAPIWrapper

    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Use the tool."""
        return self.api_wrapper.run(query)


class SearchResults(BaseTool):
    """Bing Search tool.

    Setup:
        Install ``langchain-community`` and set environment variable ``BING_SUBSCRIPTION_KEY``.

        .. code-block:: bash

            pip install -U langchain-community
            export BING_SUBSCRIPTION_KEY="your-api-key"

    Instantiation:
        .. code-block:: python

            from langchain_community.tools.bing_search import BingSearchResults
            from langchain_community.utilities import BingSearchAPIWrapper

            api_wrapper = BingSearchAPIWrapper()
            tool = BingSearchResults(api_wrapper=api_wrapper)

    Invocation with args:
        .. code-block:: python

            tool.invoke({"query": "what is the weather in SF?"})

        .. code-block:: python

            "[{'snippet': '<b>San Francisco, CA</b> <b>Weather</b> Forecast, with current conditions, wind, air quality, and what to expect for the next 3 days.', 'title': 'San Francisco, CA Weather Forecast | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/weather-forecast/347629'}, {'snippet': 'Tropical Storm Ernesto Forms; Fire <b>Weather</b> Concerns in the Great Basin: Hot Temperatures Return to the South-Central U.S. ... <b>San Francisco CA</b> 37.77°N 122.41°W (Elev. 131 ft) Last Update: 2:21 pm PDT Aug 12, 2024. Forecast Valid: 6pm PDT Aug 12, 2024-6pm PDT Aug 19, 2024 .', 'title': 'National Weather Service', 'link': 'https://forecast.weather.gov/zipcity.php?inputstring=San+Francisco,CA'}, {'snippet': 'Current <b>weather</b> <b>in San Francisco, CA</b>. Check current conditions <b>in San Francisco, CA</b> with radar, hourly, and more.', 'title': 'San Francisco, CA Current Weather | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/current-weather/347629'}, {'snippet': 'Everything you need to know about today&#39;s <b>weather</b> <b>in San Francisco, CA</b>. High/Low, Precipitation Chances, Sunrise/Sunset, and today&#39;s Temperature History.', 'title': 'Weather Today for San Francisco, CA | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/weather-today/347629'}]"

    Invocation with ToolCall:

        .. code-block:: python

            tool.invoke({"args": {"query":"what is the weather in SF?"}, "id": "1", "name": tool.name, "type": "tool_call"})

        .. code-block:: python

            ToolMessage(
                content="[{'snippet': 'Get the latest <b>weather</b> forecast for <b>San Francisco, CA</b>, including temperature, RealFeel, and chance of precipitation. Find out how the <b>weather</b> will affect your plans and activities in the city of ...', 'title': 'San Francisco, CA Weather Forecast | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/weather-forecast/347629'}, {'snippet': 'Radar. Be prepared with the most accurate 10-day forecast for <b>San Francisco, CA</b> with highs, lows, chance of precipitation from The <b>Weather</b> Channel and <b>Weather</b>.com.', 'title': '10-Day Weather Forecast for San Francisco, CA - The Weather Channel', 'link': 'https://weather.com/weather/tenday/l/San+Francisco+CA+USCA0987:1:US'}, {'snippet': 'Tropical Storm Ernesto Forms; Fire <b>Weather</b> Concerns in the Great Basin: Hot Temperatures Return to the South-Central U.S. ... <b>San Francisco CA</b> 37.77°N 122.41°W (Elev. 131 ft) Last Update: 2:21 pm PDT Aug 12, 2024. Forecast Valid: 6pm PDT Aug 12, 2024-6pm PDT Aug 19, 2024 .', 'title': 'National Weather Service', 'link': 'https://forecast.weather.gov/zipcity.php?inputstring=San+Francisco,CA'}, {'snippet': 'Current <b>weather</b> <b>in San Francisco, CA</b>. Check current conditions <b>in San Francisco, CA</b> with radar, hourly, and more.', 'title': 'San Francisco, CA Current Weather | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/current-weather/347629'}]",
                artifact=[{'snippet': 'Get the latest <b>weather</b> forecast for <b>San Francisco, CA</b>, including temperature, RealFeel, and chance of precipitation. Find out how the <b>weather</b> will affect your plans and activities in the city of ...', 'title': 'San Francisco, CA Weather Forecast | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/weather-forecast/347629'}, {'snippet': 'Radar. Be prepared with the most accurate 10-day forecast for <b>San Francisco, CA</b> with highs, lows, chance of precipitation from The <b>Weather</b> Channel and <b>Weather</b>.com.', 'title': '10-Day Weather Forecast for San Francisco, CA - The Weather Channel', 'link': 'https://weather.com/weather/tenday/l/San+Francisco+CA+USCA0987:1:US'}, {'snippet': 'Tropical Storm Ernesto Forms; Fire <b>Weather</b> Concerns in the Great Basin: Hot Temperatures Return to the South-Central U.S. ... <b>San Francisco CA</b> 37.77°N 122.41°W (Elev. 131 ft) Last Update: 2:21 pm PDT Aug 12, 2024. Forecast Valid: 6pm PDT Aug 12, 2024-6pm PDT Aug 19, 2024 .', 'title': 'National Weather Service', 'link': 'https://forecast.weather.gov/zipcity.php?inputstring=San+Francisco,CA'}, {'snippet': 'Current <b>weather</b> <b>in San Francisco, CA</b>. Check current conditions <b>in San Francisco, CA</b> with radar, hourly, and more.', 'title': 'San Francisco, CA Current Weather | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/current-weather/347629'}],
                name='bing_search_results_json',
                tool_call_id='1'
            )

    """  # noqa: E501

    name: str = "bing_search_results_json"
    description: str = (
        "A wrapper around Bing Search. "
        "Useful for when you need to answer questions about current events. "
        "Input should be a search query. Output is an array of the query results."
    )
    num_results: int = 4
    """Max search results to return, default is 4."""
    api_wrapper: SearchAPIWrapper
    response_format: Literal["content_and_artifact"] = "content_and_artifact"

    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> Tuple[str, List[Dict]]:
        """Use the tool."""
        try:
            results = self.api_wrapper.results(query, self.num_results)
            return str(results), results
        except Exception as e:
            logger.debug(f"❌ {self.name}搜索失败: {str(e)}")
            return "[]", []

    def _set_num_results(self, num_results: int):
        self.num_results = num_results


def _init_search_api_wrapper(search_engine_name: str) -> SearchAPIWrapper:
    logger.info(f"开始初始化bing_search_results_json, search_engine_name: {search_engine_name}")
    return SearchAPIWrapper(
        bing_subscription_key=DEFAULT_APPID,
        bing_search_url=DEFAULT_BING_SEARCH_ENDPOINT,
        k=1,
        search_engine_name=search_engine_name,
    )


def _get_encoding_from_response(response: requests.Response) -> str | None:
    """从requests的Response对象中获取或猜测编码（优化版）"""
    # 1. 从 HTTP headers 的 Content-Type 获取
    content_type = response.headers.get("content-type", "").lower()
    if content_type:
        charset_match = re.search(r"charset=([\w-]+)", content_type, re.I)
        if charset_match:
            encoding = charset_match.group(1).lower()
            # 常见中文编码映射
            encoding_map = {
                "gb2312": "gbk",  # gb2312 -> gbk (更完整)
                "iso-8859-1": "utf-8",  # 很多中文网站错误声明为iso-8859-1
            }
            encoding = encoding_map.get(encoding, encoding)

            try:  # 验证编码是否有效
                "测试".encode(encoding)
                logger.info(f"从 HTTP header 获取编码: {encoding}")
                return encoding
            except (LookupError, UnicodeEncodeError):
                logger.warning(f"HTTP header 中的编码无效: {encoding}")

    # 2. 检测中文网站常见编码顺序
    encodings_to_try = ["utf-8", "gbk", "gb18030", "big5"]

    # 取前1024字节进行编码检测
    content_sample = response.content[:1024]

    for encoding in encodings_to_try:
        try:
            decoded_sample = content_sample.decode(encoding, errors="strict")
            # 检查是否包含中文字符，如果是中文网站更可能用对应编码
            if any("\u4e00" <= char <= "\u9fff" for char in decoded_sample):
                logger.info(f"检测到中文内容，使用编码: {encoding}")
                return encoding
        except UnicodeDecodeError:
            continue

    # 3. 使用 requests 的 apparent_encoding
    if response.apparent_encoding and response.apparent_encoding.lower() != "iso-8859-1":
        logger.info(f"使用 requests.apparent_encoding: {response.apparent_encoding}")
        return response.apparent_encoding.lower()

    logger.warning("无法确定编码，使用 UTF-8 作为默认")
    return "utf-8"


def has_excessive_replacement_chars(text: str, threshold_ratio: float = 0.005, min_length_for_check: int = 50) -> bool:
    """检查文本中乱码的比例（优化版，更严格）"""
    if not text or len(text) < min_length_for_check:
        return False

    # 检查多种乱码模式
    replacement_char = "\ufffd"  # Unicode替换字符
    weird_chars = 0

    for char in text:
        # 1. Unicode替换字符
        if char == replacement_char:
            weird_chars += 1
        # 2. 控制字符（除了常见的空白字符）
        elif ord(char) < 32 and char not in "\t\n\r":
            weird_chars += 1
        # 3. 常见乱码字符范围
        elif "\u00c0" <= char <= "\u00ff":  # 扩展拉丁字符（常见乱码）
            weird_chars += 1

    ratio = weird_chars / len(text)
    is_garbled = ratio > threshold_ratio

    if is_garbled:
        logger.warning(f"🔍 检测到乱码: 异常字符={weird_chars}, 总长={len(text)}, 比例={ratio:.4f}")

    return is_garbled


def _is_seriously_garbled(text: str) -> bool:
    """检测严重乱码内容（如日志中出现的那种乱码）"""
    if not text or len(text) < 20:
        return False

    # 统计各种异常字符
    total_chars = len(text)
    problem_count = 0

    for char in text:
        code = ord(char)
        # 1. 高位ASCII字符（128-255）
        if 128 <= code <= 255:
            problem_count += 1
        # 2. 常见乱码字符模式
        elif char in "ÄÏÎßÃÜýýý¢KÑé÷Gã»)MÞÖÕw¥AH":
            problem_count += 1
        # 3. 非打印控制字符
        elif code < 32 and char not in "\t\n\r ":
            problem_count += 1

    # 如果异常字符超过20%，认为是严重乱码
    ratio = problem_count / total_chars
    is_garbled = ratio > 0.2

    if is_garbled:
        logger.warning(f"🔍 严重乱码检测: 异常字符={problem_count}, 总长={total_chars}, 比例={ratio:.3f}")
        # 输出前50个字符用于调试
        sample = text[:50].replace("\n", "\\n").replace("\r", "\\r")
        logger.debug(f"🔍 乱码样本: {sample}")

    return is_garbled


def check_ssrf(url):
    hostname = urlparse(url).hostname

    def ip2long(ip_addr):
        return unpack("!L", inet_aton(ip_addr))[0]

    def is_inner_ipaddress(ip):
        ip = ip2long(ip)
        return (
            ip2long("*********") >> 24 == ip >> 24
            or ip2long("**********") >> 20 == ip >> 20
            or ip2long("***********") >> 16 == ip >> 16
            or ip2long("0.0.0.0") >> 24 == ip >> 24
        )

    try:
        # if not re.match(r"^https?://.*/.*$", url):
        #     raise BaseException("url format error")
        ip_address = socket.getaddrinfo(hostname, "http")[0][4][0]
        if is_inner_ipaddress(ip_address):
            raise BaseException("inner ip address attack")
        return True, "success"
    except BaseException as e:
        return False, str(e)
    except:
        return False, "unknow error"


def _extract_key_content(url: str, related_info: List[Any], index: int, pdf_only: bool, need_conclude: bool) -> None:
    """
    提取网页关键内容

    Args:
        url: 网页URL
        related_info: 存储爬取结果的数组
        query: 搜索关键词
        index: 当前网页索引
        pdf_only: 是否只爬取PDF文件
        need_conclude: 是否需要总结
    """
    if not url:
        logger.debug(f"🚫 [{index+1}] URL为空，跳过爬取")
        related_info[index] = ["", "None", "", "", ""]
        return

    try:
        if not check_ssrf(url):
            logger.debug(f"⚠️ [{index+1}] 检测到SSRF攻击，跳过爬取")
            related_info[index] = ["", "None", "", "", ""]
            return
        else:
            logger.debug(f"✅ [{index+1}] 未检测到SSRF攻击，继续爬取")
    except Exception as e:
        logger.debug(f"⚠️ [{index+1}] 检测到SSRF攻击，跳过爬取: {e}")
        related_info[index] = ["", "None", "", "", ""]
        return

    # 如果是PDF文件且pdf_only为False，或者不是PDF文件且pdf_only为True，则跳过
    is_pdf = url.lower().endswith(".pdf")
    if (is_pdf and not pdf_only) or (not is_pdf and pdf_only):
        logger.debug(f"⏭️ [{index+1}] 根据pdf_only={pdf_only}设置跳过URL: {url}, 是否为PDF: {is_pdf}")
        related_info[index] = ["", "None", "", "", ""]
        return

    logger.debug(f"🌐 [{index+1}] 开始爬取: {url}")

    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7",
            "Accept-Encoding": "gzip, deflate, br",  # 明确接受压缩，requests会自动处理解压
        }

        parsed_url = urllib.parse.urlparse(url)
        if not parsed_url.scheme:
            url = "http://" + url

        logger.debug(f"📡 [{index+1}] 发送HTTP请求: {url}")
        # 调整超时时间，适应网络受限环境
        response = requests.get(url, headers=headers, timeout=10, stream=True)  # 减少超时时间从15秒到10秒
        response.raise_for_status()
        logger.debug(f"✅ [{index+1}] HTTP响应成功，状态码: {response.status_code}")

        if url.lower().endswith(".pdf"):
            logger.debug(f"📄 [{index+1}] 检测到PDF文件，暂不支持处理")
            related_info[index] = [f"[PDF文件，无法直接提取内容] {url}", "PDF文件内容需要专门处理", "", "", ""]
            return

        # --- 关键的编码处理 ---
        logger.debug(f"🔤 [{index+1}] 开始分析网页编码")
        # 1. 从响应头或内容猜测编码
        encoding = _get_encoding_from_response(response)

        # 2. 使用获取到的编码解码 response.content
        try:
            # response.content 是原始字节
            html_content_bytes = response.content  # 读取所有内容
            html_str = html_content_bytes.decode(encoding, errors="replace")
            logger.debug(f"✅ [{index+1}] 使用编码 '{encoding}' 解码成功，内容长度: {len(html_str)}")
        except Exception as e:
            logger.debug(f"❌ [{index+1}] 使用编码 '{encoding}' 解码失败: {e}. 尝试UTF-8")
            # 如果指定的编码解码失败，尝试用UTF-8（最常见的）
            try:
                html_str = response.content.decode("utf-8", errors="replace")
                encoding = "utf-8"  # 更新实际使用的编码
                logger.debug(f"✅ [{index+1}] 使用UTF-8解码成功")
            except Exception as e_utf8:
                logger.error(f"💥 [{index+1}] 使用UTF-8解码也失败: {e_utf8}")
                related_info[index] = [f"解码失败: {url}", "None", "", "", ""]
                return
        # --- 编码处理结束 ---

        # 现在 html_str 是一个解码后的字符串

        # 使用 BeautifulSoup 解析 HTML（移除 lxml 依赖）
        try:
            logger.debug(f"🔧 [{index+1}] 开始使用BeautifulSoup解析HTML")
            # 使用BeautifulSoup解析已经解码的 html_str
            soup = BeautifulSoup(html_str, "html.parser")

            # 移除脚本和样式标签
            for script_or_style in soup(["script", "style"]):
                script_or_style.extract()

            # 提取文本内容
            text = soup.get_text(separator="\n")
            lines = [line.strip() for line in text.splitlines() if line.strip()]
            raw_text = "\n".join(lines)

            # 清理多余的空白字符
            raw_text = re.sub(r"\s+", " ", raw_text).strip()

            if has_excessive_replacement_chars(raw_text):
                logger.debug(f"⚠️ [{index+1}] 检测到过多替换字符，识别为无效字符串")
                related_info[index] = [f"爬取到无效字符串: {url}", "None", "", "", ""]
                return

            if len(raw_text) < 50:
                logger.debug(f"⚠️ [{index+1}] 提取内容过短 ({len(raw_text)} 字符)，可能是无效页面")
                related_info[index] = [raw_text, "None", "", "", ""]
                return

            # 限制长度
            summary = raw_text[:1000] if len(raw_text) > 1000 else raw_text
            related_info[index] = [raw_text, summary, "", "", ""]
            logger.debug(f"✅ [{index+1}] BeautifulSoup解析成功，原始长度: {len(raw_text)}, 摘要长度: {len(summary)}")

        except Exception as e_bs:
            logger.debug(f"💥 [{index+1}] BeautifulSoup解析失败: {e_bs}")
            related_info[index] = [f"解析失败: {url}", "None", "", "", ""]

    except requests.exceptions.RequestException as e_req:
        logger.debug(f"🌐 [{index+1}] 请求网页失败: {str(e_req)}")
        related_info[index] = [f"请求失败: {url}, 错误: {str(e_req)}", "None", "", "", ""]
    except Exception as e_global:
        logger.debug(f"💥 [{index+1}] 提取网页内容时发生未知错误: {str(e_global)}")
        related_info[index] = [f"未知错误: {url}, 错误: {str(e_global)}", "None", "", "", ""]


def run_crawl_page(search_results: List[Dict[str, Any]], pdf_only: bool = False, need_conclude: bool = True) -> None:
    """
    爬取搜索结果页面内容

    Args:
        query: 搜索关键词
        search_results: 搜索结果列表
        pdf_only: 是否只爬取PDF文件
        need_conclude: 是否需要总结
    """
    if not search_results:
        logger.warning("⚠️ 没有搜索结果需要爬取")
        return

    logger.info(f"🌐 开始爬取搜索结果, 共 {len(search_results)} 个网页")
    logger.info(f"⚙️ 爬取配置: pdf_only={pdf_only}, need_conclude={need_conclude}")

    # 创建数组存储爬取结果
    related_info = [0] * len(search_results)

    # 使用线程池并发爬取网页
    start_time = time.time()
    successful_crawls = 0

    logger.info(f"🧵 启动线程池，最大并发数: {min(len(search_results), 8)}")

    with ThreadPoolExecutor(max_workers=min(len(search_results), 8)) as executor:
        # 提交所有爬取任务
        futures = [
            executor.submit(_extract_key_content, result.get("link", ""), related_info, i, pdf_only, need_conclude)
            for i, result in enumerate(search_results)
        ]

        logger.info(f"📋 已提交 {len(futures)} 个爬取任务到线程池")

        try:
            # 等待所有线程完成，设置超时时间 - 调整为更长的超时时间
            completed_tasks = 0
            for future in as_completed(futures, timeout=60):
                try:
                    future.result()  # 获取结果，如果有异常会抛出
                    completed_tasks += 1
                    if completed_tasks % 5 == 0:  # 每完成5个任务记录一次进度
                        logger.info(f"📈 爬取进度: {completed_tasks}/{len(futures)} 个任务已完成")
                except Exception as e:
                    logger.debug(f"🔧 单个爬取任务执行失败: {str(e)}")
                    pass  # 单个任务失败不影响整体流程

            logger.info(f"✅ 所有爬取任务完成，共处理 {completed_tasks}/{len(futures)} 个任务")

        except Exception as e:
            logger.warning(f"⏰ 并发爬取部分任务超时: {str(e)}")
            # 不要立即取消所有任务，而是等待已经在执行的任务
            completed_count = sum(1 for f in futures if f.done())
            logger.info(f"📊 爬取超时统计：已完成 {completed_count}/{len(futures)} 个任务")

    end_time = time.time()
    logger.info(f"⏱️ 网页爬取完成，总耗时 {end_time - start_time:.2f} 秒")

    # 处理爬取结果
    valid_content_count = 0
    failed_quality_count = 0
    failed_format_count = 0
    failed_total_count = 0

    logger.info("🔍 开始分析爬取结果...")

    for i, result in enumerate(search_results):
        if i < len(related_info) and related_info[i] != 0:
            # 解析爬取结果
            # related_info[i] 格式: [原始文本, 提取/摘要文本, PDF封面图片, PDF图片列表, 网页图片列表]
            if isinstance(related_info[i], list) and len(related_info[i]) >= 2:
                raw_text, extracted_text = related_info[i][0:2]

                # 检查内容质量
                if extracted_text and extracted_text != "None":
                    result["crawled_content"] = raw_text
                    result["content_summary"] = extracted_text

                    # 如果有图片，也添加到结果中
                    if len(related_info[i]) > 2 and related_info[i][2]:
                        result["pdf_cover_image"] = related_info[i][2]

                    if len(related_info[i]) > 3 and related_info[i][3]:
                        result["pdf_images"] = related_info[i][3]

                    if len(related_info[i]) > 4 and related_info[i][4]:
                        result["web_images"] = related_info[i][4]

                    valid_content_count += 1
                    logger.debug(
                        f"✅ [{i+1}] 成功爬取: {result.get('title', '')[:30]}..., 摘要长度: {len(extracted_text)}"
                    )
                else:
                    failed_quality_count += 1
                    logger.debug(f"⚠️ [{i+1}] 内容质量不佳，使用搜索摘要: {result.get('title', '')[:30]}...")
                    result["crawl_status"] = "failed_quality"
                    # 爬取失败时，使用搜索摘要作为备用内容
                    result["crawled_content"] = result.get("snippet", "")
                    result["content_summary"] = result.get("snippet", "")
            else:
                failed_format_count += 1
                logger.debug(f"❌ [{i+1}] 格式错误，使用搜索摘要: {result.get('title', '')[:30]}...")
                result["crawl_status"] = "failed_format"
                # 爬取失败时，使用搜索摘要作为备用内容
                result["crawled_content"] = result.get("snippet", "")
                result["content_summary"] = result.get("snippet", "")
        else:
            failed_total_count += 1
            logger.debug(f"💥 [{i+1}] 爬取失败，使用搜索摘要: {result.get('title', '')[:30]}...")
            result["crawl_status"] = "failed"
            # 爬取失败时，使用搜索摘要作为备用内容
            result["crawled_content"] = result.get("snippet", "")
            result["content_summary"] = result.get("snippet", "")

    # 如果所有爬取都失败，至少保证有搜索摘要内容
    if valid_content_count == 0:
        logger.warning("⚠️ 所有网页爬取都失败，正在应用搜索摘要降级策略")
        for result in search_results:
            if not result.get("crawled_content"):
                result["crawled_content"] = result.get("snippet", "")
                result["content_summary"] = result.get("snippet", "")

    logger.info(f"📊 爬取结果统计:")
    logger.info(f"   ✅ 成功爬取: {valid_content_count} 个")
    logger.info(f"   ⚠️ 质量问题: {failed_quality_count} 个")
    logger.info(f"   ❌ 格式错误: {failed_format_count} 个")
    logger.info(f"   💥 完全失败: {failed_total_count} 个")
    logger.info(f"   📋 总计处理: {len(search_results)} 个")

    if valid_content_count > 0:
        success_rate = (valid_content_count / len(search_results)) * 100
        logger.info(f"🎯 爬取成功率: {success_rate:.1f}%")
    else:
        logger.warning("🚨 爬取成功率: 0% (已启用降级策略)")


searchToolDict: Dict[str, BaseTool] = {
    "bing_search": SearchResults(
        name="bing_search",
        description="用于Bing搜索",
        api_wrapper=_init_search_api_wrapper("bing-search"),
        response_format="content_and_artifact",
    ),
    "google_search": SearchResults(
        name="google_search",
        description="用于Google搜索",
        api_wrapper=_init_search_api_wrapper("google-search"),
        response_format="content_and_artifact",
    ),
    "wenxin_search": SearchResults(
        name="wenxin_search",
        description="用于文心搜索",
        api_wrapper=_init_search_api_wrapper("wenxin-search"),
        response_format="content_and_artifact",
    ),
    "tencent_search": SearchResults(
        name="tencent_search",
        description="用于腾讯搜索",
        api_wrapper=_init_search_api_wrapper("tencent-search"),
        response_format="content_and_artifact",
    ),
    "bing_search_pro": SearchResults(
        name="bing_search_pro",
        description="用于Bing专业搜索",
        api_wrapper=_init_search_api_wrapper("bing-search-pro"),
        response_format="content_and_artifact",
    ),
}


def init_search_tools(tools_config):
    using_engines = []

    # 检查配置是否包含必要的字段
    if not tools_config or "SearchEngineStatus" not in tools_config:
        logger.warning("搜索工具配置为空或缺少 SearchEngineStatus，使用默认搜索引擎")
        using_engines = ["google_search", "wenxin_search", "tencent_search"]  # 默认使用可用的搜索引擎，不包含bing
    else:
        for engine_name, engine_status in tools_config["SearchEngineStatus"].items():
            if engine_status:
                using_engines.append(engine_name)

    # 如果没有启用任何搜索引擎，使用默认的（不包含bing）
    if not using_engines:
        logger.warning("没有启用任何搜索引擎，使用默认的可用搜索引擎")
        using_engines = ["google_search", "wenxin_search", "tencent_search"]

    logger.info(f"初始化搜索工具，使用以下搜索引擎: {using_engines}")

    # 从Lion配置中心获取工具描述，如果没有配置则使用默认值
    tool_description = str(
        get_value(
            "xiaomei.humanrelation.search_tool_description", "联网搜索，用于获取新闻信息，你不知道的知识都可以使用"
        )
    )
    logger.info(f"🔧 搜索工具描述配置: {tool_description}")

    @tool(description=tool_description)
    def web_search(query: str) -> str:
        """
        联网搜索，可以用于搜集信息数据
        """
        try:
            # 检查缓存
            cached_result = search_cache.get(query)
            if cached_result:
                return format_cached_results(cached_result)

            # 并行调用所有搜索引擎
            start_time = time.time()
            all_results = []

            # 使用线程池并行调用搜索引擎
            with ThreadPoolExecutor(max_workers=len(using_engines)) as executor:
                future_to_engine = {
                    executor.submit(call_search_engine_with_timeout, engine, query, 8): engine
                    for engine in using_engines
                }

                # 等待所有搜索完成，设置总超时时间
                for future in as_completed(future_to_engine, timeout=12):
                    engine_name = future_to_engine[future]
                    try:
                        results = future.result()
                        if results:
                            all_results.extend(results)
                    except Exception as e:
                        logger.debug(f"❌ {engine_name} 处理失败: {str(e)}")

            search_end_time = time.time()

            if not all_results:
                return "搜索未返回任何结果，请尝试使用不同的关键词。"

            # 智能筛选结果
            filtered_results = intelligent_result_filter(query, all_results)

            # 缓存筛选后的结果
            search_cache.set(query, filtered_results)

            # 爬取网页内容
            run_crawl_page_optimized(filtered_results)

            # 优化返回逻辑：优先返回爬取内容，如果没有则返回搜索摘要
            content_parts = []
            crawled_count = 0
            fallback_count = 0

            # 限制返回结果数量以避免上下文过长
            max_results = 5  # 最多保留5条结果
            sampled_results = filtered_results[:max_results]

            for i, result in enumerate(sampled_results, 1):
                # 优先使用爬取的内容，如果没有则使用搜索摘要
                content = result.get("crawled_content", "") or result.get("snippet", "")
                if content:
                    # 统计内容来源
                    if result.get("crawled_content"):
                        crawled_count += 1
                    else:
                        fallback_count += 1

                    # 添加标题和链接信息以便用户识别来源
                    title = result.get("title", "无标题")
                    link = result.get("link", "")
                    relevance_score = result.get("relevance_score", 0)
                    content_with_meta = f"【{title}】(相关度:{relevance_score})\n{content}\n来源: {link}\n"
                    content_parts.append(content_with_meta)

            if not content_parts:
                return "搜索完成，但未能获取到有效内容。"

            final_content = "\n".join(content_parts)
            total_time = time.time() - start_time

            # 只在debug模式下输出详细统计
            logger.debug(f"搜索完成: 耗时{total_time:.2f}s，爬取{crawled_count}个，摘要{fallback_count}个，共{len(content_parts)}条结果")

            if not final_content.strip():
                return "搜索完成，但内容为空。"
            return final_content

        except Exception as e:
            logger.error(f"💥 web_search 工具执行过程中发生异常: {str(e)}")
            return f"搜索过程中遇到技术问题，但我会尽力为您提供帮助。请问您具体想了解什么信息？"

    return web_search


def format_cached_results(cached_results: List[Dict]) -> str:
    """格式化缓存的搜索结果"""
    content_parts = []
    for i, result in enumerate(cached_results[:5], 1):  # 最多5条
        content = result.get("crawled_content", "") or result.get("snippet", "")
        if content:
            title = result.get("title", "无标题")
            link = result.get("link", "")
            relevance_score = result.get("relevance_score", 0)
            content_with_meta = f"【{title}】(相关度:{relevance_score})\n{content}\n来源: {link}\n"
            content_parts.append(content_with_meta)

    return "\n".join(content_parts) if content_parts else "缓存中暂无有效内容。"


# 慢网站检测和跳过机制
class SlowSiteTracker:
    def __init__(self):
        self.slow_sites = {}  # domain -> last_slow_time
        self.slow_threshold = 8  # 超过8秒认为是慢网站
        self.skip_duration = 300  # 5分钟内跳过慢网站

    def should_skip(self, url: str) -> bool:
        """检查是否应该跳过这个域名"""
        domain = self._get_domain(url)
        if domain in self.slow_sites:
            last_slow_time = self.slow_sites[domain]
            if time.time() - last_slow_time < self.skip_duration:
                return True
        return False

    def mark_slow(self, url: str, response_time: float):
        """标记网站为慢网站"""
        if response_time > self.slow_threshold:
            domain = self._get_domain(url)
            self.slow_sites[domain] = time.time()

    def _get_domain(self, url: str) -> str:
        """提取域名"""
        try:
            from urllib.parse import urlparse

            return urlparse(url).netloc
        except:
            return url


# 全局慢网站跟踪器
slow_site_tracker = SlowSiteTracker()


def get_adaptive_timeout(url: str) -> int:
    """根据域名动态调整超时时间（优化版：更激进的超时）"""
    # 检查是否是已知慢网站
    if slow_site_tracker.should_skip(url):
        return 2  # 对慢网站使用极短超时

    # 大型新闻网站稍微宽松一点
    if any(domain in url for domain in ["sina.com", "qq.com", "sohu.com", "netease.com"]):
        return 5  # 从8秒降低到5秒

    # 其他网站使用更短超时
    return 3  # 从5秒降低到3秒


def prioritize_crawl_urls(results: List[Dict]) -> List[Dict]:
    """对爬取URL进行优先级排序（优化版：过滤慢网站）"""
    priority_domains = ["news.sina.com.cn", "news.163.com", "news.qq.com", "xinhuanet.com", "people.com.cn"]
    high_priority = []
    medium_priority = []
    low_priority = []

    for result in results:
        url = result.get("link", "")
        relevance_score = result.get("relevance_score", 0)

        # 跳过已知的慢网站
        if slow_site_tracker.should_skip(url):
            continue

        # 根据域名和相关性得分分类
        if any(domain in url for domain in priority_domains):
            high_priority.append(result)
        elif relevance_score >= 3:  # 高相关性
            medium_priority.append(result)
        else:
            low_priority.append(result)

    # 按优先级排序，每类内部按相关性得分排序
    high_priority.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
    medium_priority.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
    low_priority.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

    # 限制总数量，避免爬取过多网站
    all_prioritized = high_priority[:3] + medium_priority[:3] + low_priority[:2]  # 最多8个网站
    return all_prioritized


def run_crawl_page_optimized(search_results: List[Dict[str, Any]]) -> None:
    """
    优化的网页爬取函数
    """
    if not search_results:
        logger.warning("⚠️ 没有搜索结果需要爬取")
        return

    # 优先级排序
    prioritized_results = prioritize_crawl_urls(search_results)
    optimal_workers = get_optimal_crawl_workers(len(prioritized_results))

    # 创建会话复用连接
    session = requests.Session()
    adapter = requests.adapters.HTTPAdapter(pool_connections=10, pool_maxsize=20, max_retries=2)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # 创建数组存储爬取结果
    related_info = [0] * len(prioritized_results)

    start_time = time.time()

    with ThreadPoolExecutor(max_workers=optimal_workers) as executor:
        # 提交所有爬取任务
        futures = []
        for i, result in enumerate(prioritized_results):
            url = result.get("link", "")
            timeout = get_adaptive_timeout(url)
            future = executor.submit(_extract_key_content_optimized, url, related_info, i, session, timeout)
            futures.append(future)

        logger.info(f"📋 已提交 {len(futures)} 个爬取任务到优化线程池")

        try:
            # 更激进的快速失败策略：8秒内未完成的任务直接跳过
            completed_tasks = 0
            failed_tasks = 0
            timeout_start = time.time()

            for future in as_completed(futures, timeout=8):  # 从15秒减少到8秒
                try:
                    future.result()
                    completed_tasks += 1

                    # 简化进度报告
                    pass
                except Exception as e:
                    failed_tasks += 1
                    logger.debug(f"🔧 单个爬取任务执行失败: {str(e)}")

            total_elapsed = time.time() - timeout_start
            logger.debug(f"✅ 爬取完成，成功: {completed_tasks}个，失败: {failed_tasks}个")

        except Exception as e:
            completed_count = sum(1 for f in futures if f.done())
            logger.debug(f"⏰ 爬取超时: 已完成 {completed_count}个")

            # 取消未完成的任务
            for future in futures:
                if not future.done():
                    future.cancel()

    # 处理爬取结果并更新原始结果
    valid_content_count = 0
    for i, result in enumerate(prioritized_results):
        if i < len(related_info) and related_info[i] != 0:
            if isinstance(related_info[i], list) and len(related_info[i]) >= 2:
                raw_text, extracted_text = related_info[i][0:2]
                if extracted_text and extracted_text != "None":
                    result["crawled_content"] = raw_text
                    result["content_summary"] = extracted_text
                    valid_content_count += 1

    end_time = time.time()
    logger.info(f"�️ 爬取完成: {valid_content_count}/{len(prioritized_results)} 个成功，耗时 {end_time - start_time:.2f}s")


def _extract_key_content_optimized(
    url: str, related_info: List[Any], index: int, session: requests.Session, timeout: int
) -> None:
    """优化的网页内容提取函数（增加响应时间监控）"""
    if not url:
        logger.debug(f"🚫 [{index+1}] URL为空，跳过爬取")
        related_info[index] = ["", "None", "", "", ""]
        return

    # 检查是否应该跳过这个网站
    if slow_site_tracker.should_skip(url):
        logger.debug(f"⏭️ [{index+1}] 跳过已知慢网站: {url}")
        related_info[index] = ["", "None", "", "", ""]
        return

    try:
        # SSRF检查
        if not check_ssrf(url):
            logger.debug(f"⚠️ [{index+1}] 检测到SSRF攻击，跳过爬取")
            related_info[index] = ["", "None", "", "", ""]
            return
    except Exception as e:
        logger.debug(f"⚠️ [{index+1}] SSRF检查失败: {e}")
        related_info[index] = ["", "None", "", "", ""]
        return

    logger.debug(f"🌐 [{index+1}] 开始优化爬取: {url}")
    start_time = time.time()  # 开始计时

    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7",
            "Accept-Encoding": "gzip, deflate, br",
        }

        parsed_url = urllib.parse.urlparse(url)
        if not parsed_url.scheme:
            url = "http://" + url

        logger.debug(f"📡 [{index+1}] 发送HTTP请求: {url} (超时: {timeout}秒)")

        # 使用传入的session和动态超时
        response = session.get(url, headers=headers, timeout=timeout, stream=True)
        response.raise_for_status()

        # 计算响应时间
        response_time = time.time() - start_time
        logger.debug(f"✅ [{index+1}] HTTP响应成功，状态码: {response.status_code}，耗时: {response_time:.2f}秒")

        # 如果响应时间过长，标记为慢网站
        slow_site_tracker.mark_slow(url, response_time)

        # 如果响应时间超过阈值，直接跳过后续处理
        if response_time > 5:
            logger.warning(f"⏰ [{index+1}] 响应时间过长({response_time:.2f}秒)，跳过内容解析")
            related_info[index] = [f"响应超时: {url}", "None", "", "", ""]
            return

        # PDF文件处理
        if url.lower().endswith(".pdf"):
            logger.debug(f"📄 [{index+1}] 检测到PDF文件，暂不支持处理")
            related_info[index] = [f"[PDF文件，无法直接提取内容] {url}", "None", "", "", ""]
            return

        # --- 关键的编码处理（优化版）---
        logger.debug(f"🔤 [{index+1}] 开始分析网页编码")

        # 1. 获取推荐编码
        encoding = _get_encoding_from_response(response)
        html_str = None
        successful_encoding = None

        # 2. 多编码重试机制
        encodings_to_try = [encoding, "utf-8", "gbk", "gb18030", "big5", "latin1"]
        # 去重并保持顺序
        seen = set()
        encodings_to_try = [x for x in encodings_to_try if not (x in seen or seen.add(x))]

        for try_encoding in encodings_to_try:
            try:
                html_str = response.content.decode(try_encoding, errors="replace")

                # 检查是否有严重乱码
                if not has_excessive_replacement_chars(html_str):
                    successful_encoding = try_encoding
                    logger.debug(f"✅ [{index+1}] 使用编码 '{try_encoding}' 解码成功，内容长度: {len(html_str)}")
                    break
                else:
                    logger.debug(f"⚠️ [{index+1}] 编码 '{try_encoding}' 产生乱码，尝试下一个")

            except Exception as e:
                logger.debug(f"❌ [{index+1}] 编码 '{try_encoding}' 解码失败: {e}")
                continue

        # 3. 如果所有编码都失败，使用最后一次的结果
        if html_str is None:
            logger.error(f"💥 [{index+1}] 所有编码尝试都失败")
            related_info[index] = [f"编码解析失败: {url}", "None", "", "", ""]
            return

        # 4. 最终乱码检查
        if has_excessive_replacement_chars(html_str):
            logger.warning(f"🔍 [{index+1}] 最终内容仍存在乱码，使用 '{successful_encoding}' 编码")
        # --- 编码处理结束 ---

        # BeautifulSoup解析（增加超时保护）
        parse_start = time.time()
        try:
            logger.debug(f"🔧 [{index+1}] 开始BeautifulSoup解析")
            soup = BeautifulSoup(html_str, "html.parser")

            # 移除脚本和样式标签
            for script_or_style in soup(["script", "style"]):
                script_or_style.extract()

            # 提取文本内容
            text_content = soup.get_text(strip=True)
            raw_text = " ".join(text_content.split())

            # 增强乱码检测（专门检测日志中的那种乱码）
            if _is_seriously_garbled(raw_text):
                logger.warning(f"🔍 [{index+1}] 检测到严重乱码内容，跳过处理: {url}")
                related_info[index] = [f"内容乱码: {url}", "None", "", "", ""]
                return

            if has_excessive_replacement_chars(raw_text):
                logger.debug(f"⚠️ [{index+1}] 检测到过多替换字符，识别为无效字符串")
                related_info[index] = [f"爬取到无效字符串: {url}", "None", "", "", ""]
                return

            if len(raw_text) < 50:
                logger.debug(f"⚠️ [{index+1}] 提取内容过短 ({len(raw_text)} 字符)")
                related_info[index] = [raw_text, "None", "", "", ""]
                return

            # 限制长度并生成摘要
            summary = raw_text[:800] if len(raw_text) > 800 else raw_text
            related_info[index] = [raw_text, summary, "", "", ""]

            total_time = time.time() - start_time
            logger.debug(
                f"✅ [{index+1}] 优化解析成功，原始长度: {len(raw_text)}, 摘要长度: {len(summary)}, 总耗时: {total_time:.2f}秒"
            )

        except Exception as e_bs:
            logger.debug(f"💥 [{index+1}] BeautifulSoup解析失败: {e_bs}")
            related_info[index] = [f"解析失败: {url}", "None", "", "", ""]

    except requests.exceptions.Timeout:
        response_time = time.time() - start_time
        logger.warning(f"⏰ [{index+1}] 请求超时: {url} (耗时{response_time:.2f}秒)")
        slow_site_tracker.mark_slow(url, response_time)
        related_info[index] = [f"请求超时: {url}", "None", "", "", ""]
    except requests.exceptions.RequestException as e_req:
        response_time = time.time() - start_time
        logger.debug(f"🌐 [{index+1}] 请求失败: {str(e_req)} (耗时{response_time:.2f}秒)")
        if response_time > 3:  # 即使失败，如果耗时过长也标记为慢网站
            slow_site_tracker.mark_slow(url, response_time)
        related_info[index] = [f"请求失败: {url}", "None", "", "", ""]
    except Exception as e_global:
        response_time = time.time() - start_time
        logger.debug(f"💥 [{index+1}] 未知错误: {str(e_global)} (耗时{response_time:.2f}秒)")
        related_info[index] = [f"未知错误: {url}", "None", "", "", ""]


if __name__ == "__main__":
    # result = googleSearchTool.invoke("查询北京天气")
    # print(result)
    test = init_search_tools(
        {
            "SearchEngineStatus": {
                "bing_search": False,
                "google_search": True,
                "wenxin_search": True,
                "tencent_search": True,
                "bing_search_pro": False,
            }
        }
    )
    # result = test("查询北京天气")
    print(test.get_input_jsonschema())
    # print(result)
    # print(list(searchToolDict.values()))
