"""
添加同步状态字段到person_memory表
"""
import sys
import os

# 将当前目录的父目录（app目录）添加到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
app_dir = os.path.dirname(current_dir)
sys.path.insert(0, app_dir)

from my_mysql.sql_client import execute
from sqlalchemy import text

def migrate():
    """执行数据库迁移"""
    try:
        # 检查字段是否已存在
        check_sql = """
            SELECT COLUMN_NAME FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'person_memory' 
            AND COLUMN_NAME IN ('last_sync_key_attributes', 'last_sync_intimacy_score', 'last_sync_time')
        """
        
        # 使用sql_client的execute函数
        result = execute(check_sql)
        existing_columns = [row[0] for row in result.fetchall()]
        
        # 添加缺失的字段
        if 'last_sync_key_attributes' not in existing_columns:
            execute("ALTER TABLE person_memory ADD COLUMN last_sync_key_attributes TEXT COMMENT '上次同步的key_attributes JSON'")
            print("添加字段: last_sync_key_attributes")
            
        if 'last_sync_intimacy_score' not in existing_columns:
            execute("ALTER TABLE person_memory ADD COLUMN last_sync_intimacy_score INT COMMENT '上次同步的好感度分数'")
            print("添加字段: last_sync_intimacy_score")
            
        if 'last_sync_time' not in existing_columns:
            execute("ALTER TABLE person_memory ADD COLUMN last_sync_time DATETIME COMMENT '上次同步时间'")
            print("添加字段: last_sync_time")
            
        print("数据库迁移完成")
        
    except Exception as e:
        print(f"迁移失败: {e}")
        raise

if __name__ == "__main__":
    migrate()
