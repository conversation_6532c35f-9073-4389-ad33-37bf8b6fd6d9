import json
import numpy as np
import re
from typing import List, Dict, Optional, Tuple, Generator
from functools import lru_cache
from utils.logger import logger
from service.ESmemory.es_memory_client import client
from configs.lion_config import get_value
import requests
from sklearn.metrics.pairwise import cosine_similarity
from vex.vex_thrift import embedding
from service.ai_client import send_to_ai

def parse_tags(raw_tags):
    """解析标签字段，兼容数组和字符串格式"""
    if not raw_tags:
        return []
    if isinstance(raw_tags, list):
        return raw_tags
    # 如果是字符串，按逗号、分号、空格分割
    return [tag.strip() for tag in re.split(r'[，,;；\\s]+', raw_tags) if tag.strip()]

class RAGRetriever:
    """
    基于RAG原理的知识检索器
    使用向量嵌入和相似度计算来实现精确的知识检索
    """

    def __init__(self, index_name: str = "humanrelation_news_v3"):
        self.index_name = index_name
        self.client = client
        logger.info("使用 vex_thrift embedding 进行向量转换")
        self.top_k = 10
        self.similarity_threshold = float(get_value("humanrelation.similarity_threshold", "0.25"))
        self.max_react_cycles = int(get_value("humanrelation.max_react_cycles", "3"))
        self.vector_search_size = int(get_value("humanrelation.vector_search_size", "50"))

    @lru_cache(maxsize=128)
    def _get_embedding(self, text: str) -> Optional[List[float]]:
        """带缓存的向量生成方法"""
        try:
            return embedding(text)
        except Exception as e:
            logger.error(f"向量生成失败: {e}")
            return None

    def search_documents_native(self, query: str, top_k: int = 10) -> List[Dict]:
        """使用ES原生向量搜索（推荐方式）"""
        try:
            logger.info(f"🔍 RAG: 开始原生向量搜索，查询: '{query}'")

            # 1. 获取查询向量
            query_vector = self._get_embedding(query)
            if not query_vector:
                logger.error("获取查询向量失败")
                return []

            # 2. 使用ES原生KNN搜索
            search_body = {
                "size": top_k,
                "_source": ["title", "content", "tags", "source_url", "created_at",
                           "ai_judgment", "generated_keywords", "source_keyword"],
                "query": {
                    "knn": {
                        "field": "vector",
                        "query_vector": query_vector,
                        "num_candidates": self.vector_search_size,
                        "filter": {
                            "bool": {
                                "must": [
                                    {"exists": {"field": "vector"}}
                                ]
                            }
                        }
                    }
                }
            }

            response = self.client.search(index=self.index_name, body=search_body)

            documents = []
            for hit in response["hits"]["hits"]:
                source = hit["_source"]
                similarity = hit["_score"]  # ES原生相似度分数

                # 只保留相似度超过阈值的结果
                if similarity >= self.similarity_threshold:
                    documents.append({
                        "title": source.get("title", ""),
                        "content": source.get("content", ""),
                        "tags": parse_tags(source.get("tags", [])),
                        "source_url": source.get("source_url", ""),
                        "similarity": float(similarity),
                        "doc_id": hit["_id"],
                        "ai_judgment": source.get("ai_judgment", {}),
                        "generated_keywords": source.get("generated_keywords", []),
                        "created_at": source.get("created_at", ""),
                        "source_keyword": source.get("source_keyword", "")
                    })

            logger.info(f"📚 RAG: 原生向量搜索完成，找到 {len(documents)} 条相关文档")
            return documents

        except Exception as e:
            logger.error(f"原生向量搜索失败: {e}")
            return []

    def search_documents(self, query: str, top_k: int = 10) -> List[Dict]:
        """搜索相关文档，优先使用ES原生KNN，失败时使用本地相似度计算"""
        # 优先使用ES原生KNN搜索（服务器端相似度计算）
        results = self.search_documents_native(query, top_k)
        if results:
            return results

        # 回退到本地相似度计算方式
        logger.warning("ES原生KNN搜索失败，回退到本地相似度计算")
        return self._search_with_local_similarity(query, top_k)

    def _search_with_local_similarity(self, query: str, top_k: int = 10) -> List[Dict]:
        """使用本地相似度计算的搜索方法"""
        try:
            logger.info(f"🔍 RAG: 开始本地相似度计算搜索，查询: '{query}'")

            # 1. 向量化（服务器端）
            query_vector = self._get_embedding(query)
            if not query_vector:
                logger.error("获取查询向量失败")
                return []

            query_embedding = np.array(query_vector)

            # 2. 文档召回（服务器端）- 获取所有有向量的文档
            response = self.client.search(
                index=self.index_name,
                body={
                    "size": 1000,
                    "query": {"exists": {"field": "vector"}},
                    "_source": ["title", "content", "tags", "source_url", "created_at",
                               "vector", "ai_judgment", "generated_keywords", "source_keyword"]
                }
            )

            documents = []

            # 3. 相似度计算（本地端）
            for hit in response["hits"]["hits"]:
                source = hit["_source"]
                stored_vector = source.get("vector", [])

                if stored_vector and len(stored_vector) > 0:
                    doc_embedding = np.array(stored_vector)

                    # 本地相似度计算
                    similarity = cosine_similarity(
                        [query_embedding],
                        [doc_embedding]
                    )[0][0]

                    if similarity >= self.similarity_threshold:
                        documents.append({
                            "title": source.get("title", ""),
                            "content": source.get("content", ""),
                            "tags": parse_tags(source.get("tags", [])),
                        "source_url": source.get("source_url", ""),
                        "similarity": float(similarity),
                        "doc_id": hit["_id"],
                        "ai_judgment": source.get("ai_judgment", {}),
                        "generated_keywords": source.get("generated_keywords", []),
                        "created_at": source.get("created_at", ""),
                        "source_keyword": source.get("source_keyword", "")
                    })

            # 4. 排序和截取（本地端）
            documents.sort(key=lambda x: x["similarity"], reverse=True)
            result = documents[:top_k]

            logger.info(f"📚 RAG: 本地相似度计算搜索完成，找到 {len(result)} 条相关文档")
            return result

        except Exception as e:
            logger.error(f"本地相似度计算搜索失败: {e}")
            return []

    def hybrid_search(self, query: str, top_k: int = 10) -> List[Dict]:
        """混合搜索：结合向量搜索和关键词搜索"""
        try:
            logger.info(f"🔍 RAG: 开始混合搜索，查询: '{query}'")

            # 1. 向量搜索
            vector_results = self.search_documents(query, top_k * 2)

            # 2. 关键词搜索
            keyword_results = self._keyword_search(query, top_k * 2)

            # 3. 合并和重排序 (使用RRF)
            combined_results = self._combine_and_rerank(
                vector_results, keyword_results, top_k
            )

            logger.info(f"📚 RAG: 混合搜索完成，找到 {len(combined_results)} 条相关文档")
            return combined_results

        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            return self.search_documents(query, top_k)

    def _keyword_search(self, query: str, top_k: int = 10) -> List[Dict]:
        """关键词搜索"""
        try:
            search_body = {
                "size": top_k,
                "_source": ["title", "content", "tags", "source_url", "created_at",
                           "ai_judgment", "generated_keywords", "source_keyword"],
                "query": {
                    "bool": {
                        "should": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["title^2", "content", "tags^1.5", "generated_keywords^1.5"],
                                    "type": "best_fields"
                                }
                            }
                        ]
                    }
                }
            }

            response = self.client.search(index=self.index_name, body=search_body)

            documents = []
            for hit in response["hits"]["hits"]:
                source = hit["_source"]
                documents.append({
                    "title": source.get("title", ""),
                    "content": source.get("content", ""),
                    "tags": parse_tags(source.get("tags", [])),
                    "source_url": source.get("source_url", ""),
                    "similarity": float(hit["_score"]),
                    "doc_id": hit["_id"],
                    "ai_judgment": source.get("ai_judgment", {}),
                    "generated_keywords": source.get("generated_keywords", []),
                    "created_at": source.get("created_at", ""),
                    "source_keyword": source.get("source_keyword", "")
                })

            return documents

        except Exception as e:
            logger.error(f"关键词搜索失败: {e}")
            return []

    def _combine_and_rerank(self, vector_results: List[Dict], keyword_results: List[Dict],
                           top_k: int, rrf_k: int = 60) -> List[Dict]:
        """
        使用倒数排序融合 (Reciprocal Rank Fusion - RRF) 合并和重排序结果。
        RRF不关心原始分数，只关心排名，能有效融合不同来源的排序列表。
        """
        # 使用doc_id去重并合并RRF分数
        combined_docs = {}
        rrf_scores = {}

        # 处理向量搜索结果
        for rank, doc in enumerate(vector_results):
            doc_id = doc["doc_id"]
            if doc_id not in combined_docs:
                combined_docs[doc_id] = doc

            # 计算RRF分数
            rrf_score = 1 / (rrf_k + rank + 1)
            rrf_scores[doc_id] = rrf_scores.get(doc_id, 0) + rrf_score

        # 处理关键词搜索结果
        for rank, doc in enumerate(keyword_results):
            doc_id = doc["doc_id"]
            if doc_id not in combined_docs:
                combined_docs[doc_id] = doc

            # 计算RRF分数
            rrf_score = 1 / (rrf_k + rank + 1)
            rrf_scores[doc_id] = rrf_scores.get(doc_id, 0) + rrf_score

        # 将最终的RRF分数更新到文档的 "similarity" 字段
        for doc_id, score in rrf_scores.items():
            if doc_id in combined_docs:
                combined_docs[doc_id]["similarity"] = score

        # 按RRF分数排序并返回top_k
        sorted_docs = sorted(combined_docs.values(), key=lambda x: x["similarity"], reverse=True)
        return sorted_docs[:top_k]

    def react_search(self, query: str, top_k: int = 10, generator: Optional[Generator] = None) -> List[Dict]:
        """ReAct多轮召回搜索"""
        try:
            logger.info(f"🔍 RAG: 开始ReAct多轮搜索，查询: '{query}'")

            all_results = []
            current_query = query
            search_history = []

            for cycle in range(self.max_react_cycles):
                logger.info(f"📍 RAG: ReAct第{cycle + 1}轮搜索")

                # 流式更新状态
                if generator:
                    status_update = {
                        "step": f"ReAct搜索第{cycle + 1}轮",
                        "status": "RUNNING",
                        "query": current_query,
                        "reason": f"正在执行第{cycle + 1}轮向量搜索..."
                    }
                    generator.send(json.dumps(status_update))

                # 执行当前轮次搜索
                if cycle == 0:
                    # 第一轮使用混合搜索
                    cycle_results = self.hybrid_search(current_query, top_k)
                else:
                    # 后续轮次使用向量搜索
                    cycle_results = self.search_documents(current_query, top_k)

                if not cycle_results:
                    logger.info(f"第{cycle + 1}轮搜索无结果，停止ReAct")
                    break

                # 合并结果（去重）
                existing_ids = {doc["doc_id"] for doc in all_results}
                new_results = [doc for doc in cycle_results if doc["doc_id"] not in existing_ids]
                all_results.extend(new_results)

                search_history.append({
                    "cycle": cycle + 1,
                    "query": current_query,
                    "results_count": len(cycle_results),
                    "new_results_count": len(new_results)
                })

                # 流式更新完成状态
                if generator:
                    status_update = {
                        "step": f"ReAct搜索第{cycle + 1}轮",
                        "status": "FINISHED",
                        "query": current_query,
                        "results_count": len(cycle_results),
                        "new_results_count": len(new_results)
                    }
                    generator.send(json.dumps(status_update))

                # 如果是最后一轮或者没有新结果，停止
                if cycle == self.max_react_cycles - 1 or not new_results:
                    break

                # 生成下一轮查询
                next_query = self._generate_next_query(query, all_results, search_history)
                if not next_query or next_query == current_query:
                    logger.info("无法生成新的查询词，停止ReAct")
                    break

                current_query = next_query

            # 最终排序
            all_results.sort(key=lambda x: x["similarity"], reverse=True)
            final_results = all_results[:top_k]

            logger.info(f"📚 RAG: ReAct搜索完成，总共找到 {len(final_results)} 条相关文档")
            return final_results

        except Exception as e:
            logger.error(f"ReAct搜索失败: {e}")
            return self.search_documents(query, top_k)

    def _generate_next_query(self, original_query: str, current_results: List[Dict],
                           search_history: List[Dict]) -> Optional[str]:
        """使用AI生成下一轮搜索查询"""
        try:
            # 构建上下文
            context = f"原始查询: {original_query}\n\n"
            context += "当前搜索结果摘要:\n"
            for i, result in enumerate(current_results[:5]):  # 只取前5个结果
                context += f"{i+1}. {result['title'][:50]}...\n"

            context += f"\n搜索历史: {search_history}\n"

            prompt = get_value("humanrelation.react_query_generation_prompt",
                             "基于搜索结果，生成一个新的搜索查询来发现更多相关内容。只返回查询词，不要解释。")

            query_input = {
                "model": get_value("humanrelation.memory_extraction_model", "gpt-4o-mini"),
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": context}
                ],
                "stream": False,
                "temperature": 0.7,
                "max_tokens": 100
            }

            response = send_to_ai(query_input)
            if response:
                response_data = json.loads(response.text)
                next_query = response_data["choices"][0]["message"]["content"].strip()
                logger.info(f"生成的下一轮查询: {next_query}")
                return next_query

        except Exception as e:
            logger.error(f"生成下一轮查询失败: {e}")

        return None

    def semantic_search(self, query: str, top_k: int = 10, use_react: bool = False,
                       generator: Optional[Generator] = None) -> List[Dict]:
        """
        语义搜索：支持ReAct多轮召回和混合搜索
        """
        try:
            # 预处理查询文本
            processed_query = self._preprocess_query(query)
            logger.info(f"📝 RAG: 预处理后查询: '{processed_query}'")

            # 选择搜索策略
            if use_react:
                logger.info("🔍 RAG: 使用ReAct多轮搜索...")
                vector_results = self.react_search(processed_query, top_k, generator)
            else:
                logger.info("🔍 RAG: 使用混合搜索...")
                vector_results = self.hybrid_search(processed_query, top_k)

            # 检查并返回搜索结果
            if not vector_results:
                logger.info("❌ RAG: 搜索未找到任何相关结果。")
                return []

            # 对搜索结果进行AI判断和关键词生成（保持原有逻辑）
            enhanced_results = self._enhance_results_with_ai(vector_results)

            return enhanced_results[:top_k]

        except Exception as e:
            logger.error(f"语义搜索失败: {e}")
            return []

    def _enhance_results_with_ai(self, results: List[Dict]) -> List[Dict]:
        """使用AI增强搜索结果"""
        enhanced_results = []
        for result in results:
        for result in results:
            # 检查是否已有AI判断结果
            ai_judgment = result.get("ai_judgment", {})
                # 如果已有AI判断结果，直接使用
                if ai_judgment and ai_judgment.get("suitable") is not None:
                    # 只返回AI判断为适合的新闻
                    if ai_judgment.get("suitable", True):
                        enhanced_results.append(result)
                    else:
                        logger.info(f"⏭️ 跳过AI判断不适合的新闻: {result.get('title', '')[:50]}")
                else:
                    # 没有AI判断结果，进行判断
                    from service.ESmemory.es_news_crawler import ai_judge_news_suitability
                    title = result.get("title", "")
                    content = result.get("content", "")
                    judgment = ai_judge_news_suitability(content, title)
                    result["ai_judgment"] = judgment

                    # 如果新闻适合，生成关键词并添加到结果
                    if judgment.get("suitable", True):
                        from service.ESmemory.es_news_crawler import ai_generate_keywords
                        existing_tags = result.get("tags", [])
                        generated_keywords = ai_generate_keywords(content, title, existing_tags)
                        result["generated_keywords"] = generated_keywords

                        # 更新ES中的文档
                        try:
                            if "doc_id" in result:
                                self.client.update(
                                    index=self.index_name,
                                    id=result["doc_id"],
                                    body={
                                        "doc": {
                                            "ai_judgment": judgment,
                                            "generated_keywords": generated_keywords
                                        }
                                    }
                                )
                                logger.info(f"✅ 已更新文档 {result['doc_id']} 的AI判断和关键词")
                        except Exception as e:
                            logger.error(f"更新文档AI判断和关键词失败: {e}")

                        # 添加到结果列表
                        enhanced_results.append(result)
                    else:
                        logger.info(f"⏭️ 跳过AI判断不适合的新闻: {title[:50]}")

            # 如果过滤后没有结果，返回原始结果
        if not enhanced_results and results:
                logger.info("⚠️ AI过滤后没有适合的新闻，返回原始结果")
            return results

        return enhanced_results

    def _preprocess_query(self, query: str) -> str:
        """
        预处理查询文本
        """
        try:
            # 去除多余空格
            query = ' '.join(query.split())

            # 扩展查询词（如果查询很短）
            if len(query) < 10:
                # 为短查询添加相关词汇
                if "关系" in query:
                    query += " 人际交往 沟通"
                elif "工作" in query or "职场" in query:
                    query += " 职业发展 同事"
                elif "朋友" in query:
                    query += " 友谊 交友"
                elif "家庭" in query:
                    query += " 亲情 家人"

            return query
        except Exception as e:
            logger.error(f"查询预处理失败: {e}")
            return query

    def get_search_statistics(self, query: str) -> Dict:
        """
        获取搜索统计信息
        """
        try:
            vector_results = self.search_documents(query, 100)

            stats = {
                "query": query,
                "vector_results_count": len(vector_results),
                "avg_vector_similarity": np.mean([doc["similarity"] for doc in vector_results]) if vector_results else 0,
                "top_vector_similarity": max([doc["similarity"] for doc in vector_results]) if vector_results else 0,
            }

            return stats

        except Exception as e:
            logger.error(f"获取搜索统计失败: {e}")
            return {"query": query, "error": str(e)}

# 全局检索器实例
rag_retriever = RAGRetriever()
