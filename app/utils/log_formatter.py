"""
日志格式化工具，专门处理编码和格式问题
"""
import json
import re
import sys
from typing import Any, Dict, List, Union


def clean_log_content(content: str) -> str:
    """
    清理日志内容中的特殊字符和乱码
    
    Args:
        content: 原始日志内容
        
    Returns:
        清理后的内容
    """
    if not content:
        return content
    
    # 移除或替换可能导致乱码的字符
    # 这些字符在某些终端或日志查看器中可能显示为乱码
    replacements = {
        '\u00f6': 'o',  # ö
        '\u00e4': 'a',  # ä  
        '\u00e5': 'a',  # å
        '\u00c4': 'A',  # Ä
        '\u00c5': 'A',  # Å
        '\u00fc': 'u',  # ü
        '\u00dc': 'U',  # Ü
        '\u00df': 'ss', # ß
    }
    
    cleaned = content
    for old_char, new_char in replacements.items():
        cleaned = cleaned.replace(old_char, new_char)
    
    return cleaned


def safe_format_json(data: Any, max_length: int = 500, indent: int = None) -> str:
    """
    安全地格式化JSON数据，避免编码问题
    
    Args:
        data: 要格式化的数据
        max_length: 最大长度限制
        indent: JSON缩进，None表示紧凑格式
        
    Returns:
        格式化后的字符串
    """
    try:
        if data is None:
            return "null"
        
        if isinstance(data, (dict, list)):
            # 使用ensure_ascii=False确保中文正确显示
            # 使用separators减少空格
            json_str = json.dumps(
                data, 
                ensure_ascii=False, 
                separators=(',', ':') if indent is None else (',', ': '),
                indent=indent
            )
        else:
            json_str = str(data)
        
        # 清理可能的乱码字符
        json_str = clean_log_content(json_str)
        
        # 长度限制
        if len(json_str) > max_length:
            json_str = json_str[:max_length] + "...[截断]"
        
        return json_str
        
    except Exception as e:
        # 如果JSON序列化失败，返回安全的字符串表示
        safe_str = str(data)[:max_length]
        safe_str = clean_log_content(safe_str)
        return f"[JSON格式化失败: {str(e)}] {safe_str}"


def format_dict_for_log(data: Dict[str, Any], max_items: int = 10) -> str:
    """
    格式化字典用于日志输出
    
    Args:
        data: 字典数据
        max_items: 最大显示项目数
        
    Returns:
        格式化后的字符串
    """
    if not isinstance(data, dict):
        return safe_format_json(data)
    
    if len(data) <= max_items:
        return safe_format_json(data)
    
    # 如果项目太多，只显示前几项
    limited_data = dict(list(data.items())[:max_items])
    limited_data["..."] = f"还有{len(data) - max_items}项"
    
    return safe_format_json(limited_data)


def format_list_for_log(data: List[Any], max_items: int = 5) -> str:
    """
    格式化列表用于日志输出
    
    Args:
        data: 列表数据
        max_items: 最大显示项目数
        
    Returns:
        格式化后的字符串
    """
    if not isinstance(data, list):
        return safe_format_json(data)
    
    if len(data) <= max_items:
        return safe_format_json(data)
    
    # 如果项目太多，只显示前几项
    limited_data = data[:max_items]
    limited_data.append(f"...还有{len(data) - max_items}项")
    
    return safe_format_json(limited_data)


def sanitize_log_message(message: str) -> str:
    """
    清理日志消息，移除可能导致问题的字符
    
    Args:
        message: 原始消息
        
    Returns:
        清理后的消息
    """
    if not message:
        return message
    
    # 清理乱码字符
    cleaned = clean_log_content(message)
    
    # 移除控制字符（保留常见的空白字符）
    cleaned = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cleaned)
    
    return cleaned


def format_tool_info(tool) -> str:
    """
    格式化工具信息用于日志输出
    
    Args:
        tool: 工具对象
        
    Returns:
        格式化后的字符串
    """
    try:
        if hasattr(tool, 'name'):
            name = tool.name
        else:
            name = str(type(tool).__name__)
        
        if hasattr(tool, 'description'):
            desc = tool.description[:50] + "..." if len(tool.description) > 50 else tool.description
            return f"{name}({desc})"
        else:
            return name
            
    except Exception as e:
        return f"[工具信息格式化失败: {str(e)}]"


# 为了向后兼容，保留原来的函数名
safe_json_log = safe_format_json
