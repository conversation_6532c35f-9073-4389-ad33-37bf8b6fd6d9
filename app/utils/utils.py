from typing import Union
from utils.logger import logger
import time
import functools
import requests
import json
import hashlib
import datetime
from configs.config import API_MEITUAN_AI
def main(token, messages, mt_user_id, generator):
    try:
        # 准备请求数据
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        data = {
            "messages": messages,
            "user": mt_user_id,
            "stream": True
        }
        
        # 发送请求到美团 AI API
        response = requests.post(API_MEITUAN_AI, headers=headers, json=data, stream=True)
        response.raise_for_status()
        
        # 处理流式响应
        for line in response.iter_lines():
            if line:
                try:
                    json_response = json.loads(line.decode('utf-8'))
                    if 'choices' in json_response and json_response['choices']:
                        content = json_response['choices'][0].get('delta', {}).get('content', '')
                        if content:
                            generator.send(content)
                except json.JSONDecodeError:
                    logger.error(f"Failed to decode JSON: {line}")
                except Exception as e:
                    logger.error(f"Error processing response line: {str(e)}")
        
        generator.close()
        
    except Exception as e:
        logger.error(f"Error in main function: {str(e)}")
        generator.send(f"抱歉，系统出现了一些问题：{str(e)}")
        generator.close()



def timeit(func):
    """装饰器函数，用于统计函数运行时间"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        elapsed_time = end_time - start_time

        # 检查是否需要记录时间日志（通过need_logger参数控制）
        need_logger = kwargs.get('need_logger', True)
        if need_logger:
            logger.info(f"函数 {func.__name__} 执行时间: {elapsed_time:.4f} 秒")
        return result
    return wrapper


def calculate_md5(content: str) -> str:
    # 创建一个新的 md5 哈希对象
    md5_hash = hashlib.md5()
    # 更新哈希对象以包含字符串的字节表示
    md5_hash.update(content.encode('utf-8'))
    # 返回十六进制的哈希值
    return md5_hash.hexdigest()

def unixTimestamp2Time(timestamp:str)->str:
    return datetime.fromtimestamp(int(timestamp)).strftime("%Y-%m-%d %H:%M:%S")

def time2UnixTimestamp(time_str:str)->str:
    return int(time.mktime(time.strptime(time_str, "%Y-%m-%d %H:%M:%S")))
