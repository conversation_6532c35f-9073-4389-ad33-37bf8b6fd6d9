"""
线程限制器 - 防止创建过多后台线程
"""

import threading
import time
from typing import Any, Callable

from utils.logger import logger


class ThreadLimiter:
    def __init__(self, max_threads: int = 50):
        self.max_threads = max_threads
        self.active_threads = 0
        self.lock = threading.Lock()
        self.rejected_count = 0

    def submit_task(self, func: Callable, *args, **kwargs) -> bool:
        """
        提交任务到后台线程执行

        Returns:
            bool: True表示任务已提交，False表示被拒绝
        """
        with self.lock:
            if self.active_threads >= self.max_threads:
                self.rejected_count += 1
                logger.warning(f"🚫 线程数已达上限({self.max_threads})，拒绝新任务。已拒绝: {self.rejected_count}")
                return False

            self.active_threads += 1

        def wrapped_func():
            try:
                func(*args, **kwargs)
            except Exception as e:
                logger.error(f"后台任务执行失败: {e}")
            finally:
                with self.lock:
                    self.active_threads -= 1

        thread = threading.Thread(target=wrapped_func, daemon=True)
        thread.start()
        return True

    def get_stats(self) -> dict:
        """获取线程池统计信息"""
        with self.lock:
            return {
                "active_threads": self.active_threads,
                "max_threads": self.max_threads,
                "rejected_count": self.rejected_count,
                "utilization": self.active_threads / self.max_threads,
            }


# 全局线程限制器
thread_limiter = ThreadLimiter(max_threads=30)  # 保守设置


class SafeAsyncExecutor:
    """安全的异步执行器"""

    @staticmethod
    def execute_async(func: Callable, *args, **kwargs) -> bool:
        """
        安全地异步执行函数

        Returns:
            bool: True表示任务已提交，False表示系统负载过高被拒绝
        """
        # 检查系统负载
        if threading.active_count() > 80:
            logger.warning("🚫 系统线程数过高，拒绝异步任务")
            return False

        return thread_limiter.submit_task(func, *args, **kwargs)

    @staticmethod
    def execute_with_fallback(func: Callable, fallback_func: Callable = None, *args, **kwargs) -> Any:
        """
        尝试异步执行，失败时执行fallback

        Args:
            func: 主要执行的函数
            fallback_func: 失败时的备选函数
        """
        success = SafeAsyncExecutor.execute_async(func, *args, **kwargs)

        if not success and fallback_func:
            logger.info("异步执行失败，执行fallback函数")
            try:
                return fallback_func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Fallback函数执行失败: {e}")
                return None

        return success
