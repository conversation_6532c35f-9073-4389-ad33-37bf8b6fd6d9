########################################################
# 用户档案差异检测工具
########################################################

import json
from typing import Dict, Any
from utils.logger import logger

# 临时存储对话开始时的档案快照
_conversation_snapshots = {}


def extract_incremental_content(old_content: str, new_content: str) -> str:
    """
    从新内容中提取相对于旧内容的增量部分

    Args:
        old_content: 旧内容
        new_content: 新内容

    Returns:
        str: 新增的内容，如果无法提取则返回空字符串
    """
    if not old_content:
        return new_content

    if not new_content:
        return ""

    # 处理分号分隔的内容（如：喜欢喝酒；喜欢吃苹果）
    if "；" in new_content:
        old_items = set(item.strip() for item in old_content.split("；") if item.strip())
        new_items = [item.strip() for item in new_content.split("；") if item.strip()]

        # 找出新增的项目
        incremental_items = [item for item in new_items if item not in old_items]

        if incremental_items:
            return "；".join(incremental_items)

    # 处理逗号分隔的内容（如：画画，跳舞，唱歌）
    elif "，" in new_content:
        old_items = set(item.strip() for item in old_content.split("，") if item.strip())
        new_items = [item.strip() for item in new_content.split("，") if item.strip()]

        # 找出新增的项目
        incremental_items = [item for item in new_items if item not in old_items]

        if incremental_items:
            return "，".join(incremental_items)

    # 处理英文逗号分隔的内容
    elif "," in new_content:
        old_items = set(item.strip() for item in old_content.split(",") if item.strip())
        new_items = [item.strip() for item in new_content.split(",") if item.strip()]

        # 找出新增的项目
        incremental_items = [item for item in new_items if item not in old_items]

        if incremental_items:
            return ",".join(incremental_items)

    # 处理简单的字符串追加（新内容包含旧内容）
    elif old_content in new_content and new_content != old_content:
        # 尝试提取追加的部分
        if new_content.startswith(old_content):
            # 新内容以旧内容开头，提取后面的部分
            suffix = new_content[len(old_content):].strip()
            # 去掉可能的分隔符
            suffix = suffix.lstrip("；，,、 ")
            return suffix if suffix else ""

    # 如果无法提取增量内容，返回空字符串（将使用完整的新内容）
    return ""


def compare_key_attributes(old_attrs: Dict, new_attrs: Dict) -> Dict[str, Any]:
    """
    对比两个 key_attributes，返回有实际内容变化的字段
    
    Args:
        old_attrs: 旧的key_attributes
        new_attrs: 新的key_attributes
        
    Returns:
        Dict: 包含变化内容的字典，key为字段路径，value为新值
    """
    if not isinstance(old_attrs, dict):
        old_attrs = {}
    if not isinstance(new_attrs, dict):
        new_attrs = {}
    
    updates = {}
    
    def compare_nested(old_dict: Dict, new_dict: Dict, path: str = ""):
        """递归比较嵌套字典"""
        for key, new_value in new_dict.items():
            old_value = old_dict.get(key, "")
            current_path = f"{path}.{key}" if path else key
            
            if isinstance(new_value, dict) and isinstance(old_value, dict):
                # 递归处理嵌套字典
                compare_nested(old_value, new_value, current_path)
            elif isinstance(new_value, dict) and not isinstance(old_value, dict):
                # 新值是字典但旧值不是，递归处理新字典
                compare_nested({}, new_value, current_path)
            elif isinstance(new_value, list) and isinstance(old_value, list):
                # 处理数组（如子女信息）
                if new_value != old_value and new_value:  # 只有当新值不为空且与旧值不同时才记录
                    updates[current_path] = new_value
            elif isinstance(new_value, list) and not isinstance(old_value, list):
                # 新值是数组但旧值不是
                if new_value:  # 只有当新值不为空时才记录
                    updates[current_path] = new_value
            else:
                # 处理字符串值和其他基本类型
                # 避免将字典转换为字符串
                if isinstance(new_value, dict) or isinstance(old_value, dict):
                    return  # 跳过字典类型的直接比较

                old_str = str(old_value).strip() if old_value is not None else ""
                new_str = str(new_value).strip() if new_value is not None else ""

                # 只有当新值不为空且与旧值不同时才记录
                if new_str and new_str != old_str:
                    # 尝试提取新增内容
                    incremental_content = extract_incremental_content(old_str, new_str)
                    updates[current_path] = incremental_content if incremental_content else new_str
    
    try:
        logger.info(f"开始对比档案变化:")
        logger.info(f"  旧档案: {old_attrs}")
        logger.info(f"  新档案: {new_attrs}")

        compare_nested(old_attrs, new_attrs)

        if updates:
            logger.info(f"检测到用户档案变化: {len(updates)} 个字段")
            for path, value in updates.items():
                logger.info(f"  {path}: {value}")
        else:
            logger.info("未检测到档案变化")

        return updates
        
    except Exception as e:
        logger.error(f"对比key_attributes失败: {str(e)}")
        return {}


def format_profile_updates_for_display(updates: Dict[str, Any]) -> Dict[str, str]:
    """
    将档案更新格式化为适合显示的格式
    
    Args:
        updates: compare_key_attributes返回的更新字典
        
    Returns:
        Dict: 格式化后的更新内容，key为中文字段名，value为更新内容
    """
    if not updates:
        return {}
    
    # 字段路径到中文名称的映射
    field_mapping = {
        "兴趣": "兴趣",
        "期望": "期望", 
        "关心话题": "关心话题",
        "基本信息.家乡": "家乡",
        "基本信息.性别": "性别",
        "基本信息.生日": "生日",
        "基本信息.当前城市": "当前城市",
        "基本信息.家庭情况.婚育状况": "婚育状况",
        "基本信息.家庭情况.子女信息": "子女信息",
        "基本信息.家庭情况.配偶姓名": "配偶姓名",
        "基本信息.职业信息.公司": "公司",
        "基本信息.职业信息.职位": "职位",
        "基本信息.职业信息.行业": "行业",
        "基本信息.联系方式.电话": "电话",
        "基本信息.联系方式.邮箱": "邮箱",
        "基本信息.联系方式.社交账号.微信": "微信",
        "旅游历史": "旅游历史",
        "过往历史": "过往历史",
        "餐饮偏好": "餐饮偏好"
    }
    
    formatted_updates = {}
    
    for path, value in updates.items():
        # 获取中文字段名
        display_name = field_mapping.get(path, path)
        
        # 格式化值
        if isinstance(value, list):
            formatted_value = ", ".join(str(item) for item in value if item)
        else:
            formatted_value = str(value)
        
        if formatted_value:  # 只添加非空值
            formatted_updates[display_name] = formatted_value
    
    return formatted_updates


def get_user_profile_snapshot(user_id: str) -> Dict:
    """
    获取用户档案快照
    
    Args:
        user_id: 用户ID
        
    Returns:
        Dict: 用户档案数据，如果获取失败返回空字典
    """
    try:
        logger.info(f"开始获取用户档案快照: user_id={user_id}")
        from service.mysql_person_service import get_user_person

        user_person = get_user_person(user_id)
        logger.info(f"获取到的用户档案: {user_person}")

        if user_person:
            snapshot = {
                "key_attributes": user_person.get("key_attributes", {}),
                "profile_summary": user_person.get("profile_summary", ""),
                "canonical_name": user_person.get("canonical_name", ""),
                "intimacy_score": user_person.get("intimacy_score", 0)
            }
            logger.info(f"生成的档案快照: {snapshot}")
            return snapshot
        else:
            logger.warning(f"用户 {user_id} 档案不存在")
            return {}

    except Exception as e:
        logger.error(f"获取用户档案快照失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return {}


def track_profile_changes(user_id: str, start_profile: Dict) -> Dict[str, Any]:
    """
    追踪用户档案变化
    
    Args:
        user_id: 用户ID
        start_profile: 对话开始时的档案快照
        
    Returns:
        Dict: 包含变化信息的字典
    """
    try:
        # 获取当前档案
        current_profile = get_user_profile_snapshot(user_id)
        
        if not current_profile:
            return {}
        
        # 对比key_attributes变化
        old_attrs = start_profile.get("key_attributes", {})
        new_attrs = current_profile.get("key_attributes", {})
        
        profile_updates = compare_key_attributes(old_attrs, new_attrs)
        
        # 计算亲密度变化
        old_intimacy = start_profile.get("intimacy_score", 0)
        new_intimacy = current_profile.get("intimacy_score", 0)
        intimacy_change = new_intimacy - old_intimacy
        
        return {
            "profile_updates": profile_updates,
            "intimacy_change": intimacy_change,
            "formatted_updates": format_profile_updates_for_display(profile_updates)
        }
        
    except Exception as e:
        logger.error(f"追踪档案变化失败: {str(e)}")
        return {
            "profile_updates": {},
            "intimacy_change": 0,
            "formatted_updates": {}
        }


def save_conversation_snapshot(user_id: str, conversation_id: str, snapshot: Dict):
    """保存对话开始时的档案快照"""
    key = f"{user_id}_{conversation_id}"
    _conversation_snapshots[key] = snapshot
    logger.info(f"保存对话快照: {key}")


def get_conversation_snapshot(user_id: str, conversation_id: str) -> Dict:
    """获取对话开始时的档案快照"""
    key = f"{user_id}_{conversation_id}"
    snapshot = _conversation_snapshots.get(key, {})
    logger.info(f"获取对话快照: {key}, 结果: {bool(snapshot)}")
    return snapshot


def clear_conversation_snapshot(user_id: str, conversation_id: str):
    """清理对话快照（可选，避免内存泄漏）"""
    key = f"{user_id}_{conversation_id}"
    if key in _conversation_snapshots:
        del _conversation_snapshots[key]
        logger.info(f"清理对话快照: {key}")
