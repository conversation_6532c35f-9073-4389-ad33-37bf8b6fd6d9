import contextvars
import json
import os
import random
import socket
import sys

from configs.config import APP_KEY
from loguru import logger as loguru_logger

# 创建一个 ContextVar 用于存储 traceId
trace_id_var = contextvars.ContextVar("trace_id", default=None)

if sys.platform == "linux":
    LOG_PATH = f"/opt/logs/{APP_KEY}/app.log"
else:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    LOG_PATH = os.path.join(current_dir, "..", "..", "data", "applogs", APP_KEY, "app.log")

HOSTNAME = socket.gethostname()
# 修复日志格式，使用更简洁的格式避免编码问题
LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {extra[trace_id]} - {message}"


# 创建一个函数来生成新的 traceId
def generate_trace_id():
    """生成唯一的trace_id"""
    return str(random.randint(int(1e18), int(1e19) - 1))


# 创建一个函数来设置 traceId
def set_trace_id(request):
    if request:
        trace_id = request.headers.get("m-traceid")
        if not trace_id:
            trace_id = generate_trace_id()
    else:
        trace_id = generate_trace_id()
    trace_id_var.set(trace_id)


def update_trace_id(trace_id):
    trace_id_var.set(trace_id)


# 创建一个函数来获取当前的 traceId
def get_trace_id():
    return trace_id_var.get()


# 导入新的格式化工具
try:
    from .log_formatter import format_tool_info, safe_format_json, sanitize_log_message

    # 为了向后兼容
    safe_json_log = safe_format_json
except ImportError:
    # 如果导入失败，使用简化版本
    def safe_json_log(data, max_length=1000):
        """
        安全地格式化JSON数据用于日志输出，避免乱码问题
        """
        try:
            if isinstance(data, (dict, list)):
                json_str = json.dumps(data, ensure_ascii=False, separators=(",", ":"))
            else:
                json_str = str(data)

            if len(json_str) > max_length:
                json_str = json_str[:max_length] + "...[截断]"

            return json_str
        except Exception as e:
            return f"[JSON格式化失败: {str(e)}] {str(data)[:max_length]}"


# 创建一个绑定 traceId 的 logger
def get_logger_with_trace_id():
    trace_id = get_trace_id()
    return loguru_logger.bind(trace_id=trace_id)


# 封装 logger，使其自动绑定 traceId
class LoggerWrapper:
    def __getitem__(self, name):
        return getattr(get_logger_with_trace_id(), name)

    def __getattr__(self, name):
        # 获取绑定了 traceId 的 logger
        return getattr(get_logger_with_trace_id(), name)


# 导出封装的 logger
logger = LoggerWrapper()

# 移除默认的handler，避免重复输出
loguru_logger.remove()

# 添加控制台输出handler（用于开发调试）
loguru_logger.add(sys.stderr, format=LOG_FORMAT, level="DEBUG", colorize=True)

# 添加文件输出handler
loguru_logger.add(
    LOG_PATH,
    rotation="100MB",
    retention="30 days",
    level="DEBUG",
    format=LOG_FORMAT,
    enqueue=True,  # 异步写入，避免阻塞
    serialize=False,  # 不序列化为JSON，保持文本格式
)
