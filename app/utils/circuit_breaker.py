"""
熔断器模块 - 防止级联故障
"""

import threading
import time
from enum import Enum
from typing import Any, Callable, Optional

from configs.performance_config import perf_config
from utils.logger import logger


class CircuitState(Enum):
    CLOSED = "closed"  # 正常状态
    OPEN = "open"  # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态


class CircuitBreaker:
    def __init__(
        self, name: str, failure_threshold: int = None, timeout: int = None, expected_exception: type = Exception
    ):
        self.name = name
        self.failure_threshold = failure_threshold or perf_config.CIRCUIT_BREAKER_THRESHOLD
        self.timeout = timeout or perf_config.CIRCUIT_BREAKER_TIMEOUT
        self.expected_exception = expected_exception

        self.failure_count = 0
        self.last_failure_time = 0
        self.state = CircuitState.CLOSED
        self.lock = threading.Lock()

    def call(self, func: Callable, *args, **kwargs) -> Any:
        """执行函数调用，带熔断保护"""
        with self.lock:
            if self.state == CircuitState.OPEN:
                if time.time() - self.last_failure_time > self.timeout:
                    self.state = CircuitState.HALF_OPEN
                    logger.info(f"🔄 熔断器 {self.name} 进入半开状态")
                else:
                    raise Exception(f"熔断器 {self.name} 处于开启状态，拒绝调用")

        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e

    def _on_success(self):
        """成功时的处理"""
        with self.lock:
            self.failure_count = 0
            if self.state == CircuitState.HALF_OPEN:
                self.state = CircuitState.CLOSED
                logger.info(f"✅ 熔断器 {self.name} 恢复到关闭状态")

    def _on_failure(self):
        """失败时的处理"""
        with self.lock:
            self.failure_count += 1
            self.last_failure_time = time.time()

            if self.failure_count >= self.failure_threshold:
                self.state = CircuitState.OPEN
                logger.warning(f"🚨 熔断器 {self.name} 开启，失败次数: {self.failure_count}")

    def get_state(self) -> dict:
        """获取熔断器状态"""
        return {
            "name": self.name,
            "state": self.state.value,
            "failure_count": self.failure_count,
            "failure_threshold": self.failure_threshold,
            "last_failure_time": self.last_failure_time,
        }


class CircuitBreakerManager:
    """熔断器管理器"""

    def __init__(self):
        self.breakers = {}
        self.lock = threading.Lock()

    def get_breaker(self, name: str, **kwargs) -> CircuitBreaker:
        """获取或创建熔断器"""
        with self.lock:
            if name not in self.breakers:
                self.breakers[name] = CircuitBreaker(name, **kwargs)
            return self.breakers[name]

    def call_with_breaker(self, name: str, func: Callable, *args, **kwargs) -> Any:
        """使用熔断器执行函数"""
        breaker = self.get_breaker(name)
        return breaker.call(func, *args, **kwargs)

    def get_all_states(self) -> dict:
        """获取所有熔断器状态"""
        with self.lock:
            return {name: breaker.get_state() for name, breaker in self.breakers.items()}

    def reset_breaker(self, name: str) -> bool:
        """重置指定熔断器"""
        with self.lock:
            if name in self.breakers:
                breaker = self.breakers[name]
                breaker.failure_count = 0
                breaker.state = CircuitState.CLOSED
                logger.info(f"🔄 熔断器 {name} 已重置")
                return True
            return False


# 全局熔断器管理器
circuit_manager = CircuitBreakerManager()


# 装饰器形式的熔断器
def circuit_breaker(name: str, **breaker_kwargs):
    """熔断器装饰器"""

    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            return circuit_manager.call_with_breaker(name, func, *args, **kwargs)

        return wrapper

    return decorator


# 预定义的熔断器
def get_db_breaker() -> CircuitBreaker:
    """数据库熔断器"""
    return circuit_manager.get_breaker("database", failure_threshold=5, timeout=30)


def get_ai_breaker() -> CircuitBreaker:
    """AI服务熔断器"""
    return circuit_manager.get_breaker("ai_service", failure_threshold=3, timeout=60)


def get_search_breaker() -> CircuitBreaker:
    """搜索服务熔断器"""
    return circuit_manager.get_breaker("search_service", failure_threshold=3, timeout=60)
