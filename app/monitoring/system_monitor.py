"""
系统监控模块 - 监控关键资源使用情况
"""

import threading
import time
from typing import Any, Dict

import psutil
from my_mysql.sql_client import CLIENT
from utils.logger import logger


class SystemMonitor:
    def __init__(self):
        self.metrics = {
            "db_connections": 0,
            "active_threads": 0,
            "memory_usage": 0,
            "cpu_usage": 0,
            "last_update": time.time(),
        }
        self.alerts_sent = set()

    def get_db_connection_count(self) -> int:
        """获取数据库连接池使用情况"""
        try:
            pool = CLIENT.pool
            return pool.checkedout() if hasattr(pool, "checkedout") else 0
        except Exception as e:
            logger.error(f"获取数据库连接数失败: {e}")
            return -1

    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        try:
            self.metrics.update(
                {
                    "db_connections": self.get_db_connection_count(),
                    "active_threads": threading.active_count(),
                    "memory_usage": psutil.virtual_memory().percent,
                    "cpu_usage": psutil.cpu_percent(interval=1),
                    "last_update": time.time(),
                }
            )
            return self.metrics.copy()
        except Exception as e:
            logger.error(f"获取系统指标失败: {e}")
            return self.metrics.copy()

    def check_alerts(self) -> None:
        """检查是否需要发送告警"""
        metrics = self.get_system_metrics()

        # 数据库连接数告警
        if metrics["db_connections"] > 40:  # 80% of max (50)
            alert_key = "db_high"
            if alert_key not in self.alerts_sent:
                logger.warning(f"🚨 数据库连接数过高: {metrics['db_connections']}/50")
                self.alerts_sent.add(alert_key)
        elif metrics["db_connections"] < 30:
            self.alerts_sent.discard("db_high")

        # 线程数告警
        if metrics["active_threads"] > 100:
            alert_key = "thread_high"
            if alert_key not in self.alerts_sent:
                logger.warning(f"🚨 活跃线程数过高: {metrics['active_threads']}")
                self.alerts_sent.add(alert_key)
        elif metrics["active_threads"] < 80:
            self.alerts_sent.discard("thread_high")

        # 内存使用告警
        if metrics["memory_usage"] > 85:
            alert_key = "memory_high"
            if alert_key not in self.alerts_sent:
                logger.warning(f"🚨 内存使用率过高: {metrics['memory_usage']}%")
                self.alerts_sent.add(alert_key)
        elif metrics["memory_usage"] < 75:
            self.alerts_sent.discard("memory_high")


# 全局监控实例
system_monitor = SystemMonitor()


def start_monitoring():
    """启动后台监控"""

    def monitor_loop():
        while True:
            try:
                system_monitor.check_alerts()
                time.sleep(30)  # 每30秒检查一次
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(60)  # 异常时等待更长时间

    monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
    monitor_thread.start()
    logger.info("✅ 系统监控已启动")
