import time

import pymysql
from configs.config import APP_KEY, ZEBRA_REF_KEY
from sqlalchemy import text
from sqlalchemy.dialects import mysql
from utils.logger import logger
from zebraproxyclient.api.sqlalchemy import create_engine  # 引入 zebraproxyclient 封装的 create_engine 接口
from zebraproxyclient.config import ZebraConfig


# 创建 sqlalchemy 连接池
def make_engine():
    config = ZebraConfig(
        appname=APP_KEY,  # 必填，填你的appkey，业务服务部署在哪个appkey的机器上，就填哪个appkey
        ref_key=ZEBRA_REF_KEY,  # 必填，填你的ref_key,需要在RDS平台申请
    )
    # create_engine 第一个参数必须是 ZebraConfig，后面的参数和 sqlalchemy 的原生参数相同，可以参考sqlalchemy文档按需配置
    # 比如pool_recycle=3600表示连接池中的连接在3600s后会被回收，在SQL执行间隔过长时此参数能避免从连接池拿到失效连接
    return create_engine(
        config,
        connect_args={"connect_timeout": 5, "read_timeout": 10, "charset": "utf8mb4"},
        pool_size=20,  # 增加连接池大小
        max_overflow=30,  # 增加溢出连接数
        pool_recycle=3600,  # 连接回收时间
        pool_pre_ping=True,  # 连接预检，避免获取到失效连接
        echo=False,
    )


pymysql.install_as_MySQLdb()
# client不重复创建
CLIENT = make_engine()


def insert_return_id(sql):
    conn = CLIENT.connect()
    compiled_query = sql.compile(dialect=mysql.dialect(), compile_kwargs={"literal_binds": True})
    logger.info(f"insert_return_id {compiled_query}")
    result = conn.execute(sql)
    last_id = result.lastrowid
    conn.commit()
    conn.close()
    return last_id


def select_one(sql):
    start_time = time.time()

    # 检查连接池状态
    pool = CLIENT.pool
    if hasattr(pool, "checkedout") and pool.checkedout() > 40:  # 80% of max connections
        logger.warning(f"⚠️ 数据库连接池使用率过高: {pool.checkedout()}/{pool.size()}")

    conn = CLIENT.connect()
    try:
        compiled_query = sql.compile(dialect=mysql.dialect(), compile_kwargs={"literal_binds": True})
        logger.info(f"select_one {compiled_query}")

        result = conn.execute(sql).fetchone()
        conn.commit()

        elapsed = time.time() - start_time
        if elapsed > 0.5:  # 超过500ms记录慢查询
            logger.warning(f"慢查询检测: select_one耗时 {elapsed:.3f}秒")

        return result
    finally:
        conn.close()


def select_many(sql):
    start_time = time.time()
    conn = CLIENT.connect()
    try:
        compiled_query = sql.compile(dialect=mysql.dialect(), compile_kwargs={"literal_binds": True})
        logger.info(f"select_many {compiled_query}")

        result = conn.execute(sql).fetchall()
        conn.commit()

        elapsed = time.time() - start_time
        if elapsed > 1.0:  # 超过1秒记录慢查询
            logger.warning(f"慢查询检测: select_many耗时 {elapsed:.3f}秒")

        return result
    finally:
        conn.close()


def update(sql):
    conn = CLIENT.connect()
    compiled_query = sql.compile(dialect=mysql.dialect(), compile_kwargs={"literal_binds": True})
    logger.info(f"update {compiled_query}")
    result = conn.execute(sql)
    conn.commit()
    conn.close()
    return result


def execute(sql):
    start_time = time.time()
    conn = CLIENT.connect()
    try:
        compiled_query = sql.compile(dialect=mysql.dialect(), compile_kwargs={"literal_binds": True})
        logger.info(f"execute {compiled_query}")

        result = conn.execute(sql)
        conn.commit()

        elapsed = time.time() - start_time
        if elapsed > 1.0:  # 超过1秒记录慢查询
            logger.warning(f"慢查询检测: execute耗时 {elapsed:.3f}秒")

        return result
    finally:
        conn.close()


# 使用示例
def test_sql():
    sql = "select * from ingredient_analysis limit 10"
    conn = CLIENT.connect()  # 从连接池获取连接
    result = conn.execute(text(sql))
    logger.info(f"select {sql} result= {result}")
    conn.commit()  # SQLAlchemy版本 > 2.x支持
    conn.close()  # 将连接放回连接池


if __name__ == "__main__":
    test_sql()
