########################################################
# 人员表处理脚本
# 对应ES索引：memory_person_store
########################################################

import json
from typing import List, Optional

from my_mysql import sql_client
from sqlalchemy import (
    TIMESTAMP,
    Boolean,
    Column,
    Index,
    Integer,
    MetaData,
    String,
    Table,
    Text,
    and_,
    insert,
    or_,
    select,
    text,
    update,
)
from sqlalchemy.dialects.mysql import BIGINT, TINYINT
from sqlalchemy.exc import SQLAlchemyError
from utils.logger import logger

"""
CREATE TABLE `person_memory` (
  `person_id` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '人员唯一标识',
  `user_id` varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '关联的用户ID',
  `is_user` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为用户',
  `canonical_name` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '正式姓名',
  `aliases` text COLLATE utf8mb4_general_ci COMMENT '别名或昵称',
  `relationships` text COLLATE utf8mb4_general_ci COMMENT '人际关系列表JSON',
  `profile_summary` text COLLATE utf8mb4_general_ci COMMENT '个人简介',
  `key_attributes` text COLLATE utf8mb4_general_ci COMMENT '关键属性JSON',
  `avatar` varchar(512) COLLATE utf8mb4_general_ci COMMENT '头像URL',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`person_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_canonical_name` (`canonical_name`),
  KEY `idx_is_user` (`is_user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='人员记忆表'
"""

person_memory = Table(
    "person_memory",
    MetaData(),
    Column("person_id", String(128), primary_key=True, comment="人员唯一标识"),
    Column("user_id", String(255), nullable=True, comment="关联的用户ID"),
    Column("is_user", Boolean, nullable=False, default=False, comment="是否为用户"),
    Column("canonical_name", String(256), nullable=False, comment="正式姓名"),
    Column("aliases", Text, comment="别名或昵称"),
    Column("relationships", Text, comment="人际关系列表JSON"),
    Column("profile_summary", Text, comment="个人简介"),
    Column("key_attributes", Text, comment="关键属性JSON"),
    Column("avatar", String(512), comment="头像URL"),
    Column("intimacy_score", Integer, nullable=False, default=0, comment="与AI助手的亲密度分数"),
    Column("intimacy_updated_at", TIMESTAMP, nullable=True, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment="亲密度更新时间"),
    Column("created_at", TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"),
    Column(
        "updated_at",
        TIMESTAMP,
        nullable=False,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    ),
    Index("idx_user_id", "user_id"),
    Index("idx_canonical_name", "canonical_name"),
    Index("idx_is_user", "is_user"),
)


def insert_person(
    user_id: str,
    person_id: str,
    is_user: bool = False,
    canonical_name: str = "",
    aliases=None,  # 可以是字符串或列表
    relationships: list = [],
    profile_summary: str = "",
    key_attributes: dict = {},
    avatar: str = "",
    intimacy_score: int = 0,
):  # 添加人员
    logger.info(f"[数据库插入] insert_person开始处理 - 人物: {canonical_name}")

    # 处理aliases参数，支持字符串或列表
    if isinstance(aliases, list):
        aliases_str = json.dumps(aliases, ensure_ascii=False)
        logger.info(f"[数据库插入] aliases列表转JSON: {aliases} -> {aliases_str}")
    elif isinstance(aliases, str):
        aliases_str = aliases if aliases else None
        logger.info(f"[数据库插入] aliases字符串: {aliases_str}")
    elif aliases is None:
        aliases_str = None
        logger.info("[数据库插入] aliases为None")
    else:
        aliases_str = None
        logger.info(f"[数据库插入] aliases未知类型: {type(aliases)}")

    # 处理JSON字段
    relationships_str = json.dumps(relationships, ensure_ascii=False) if relationships is not None else None

    # 处理key_attributes，确保正确的JSON序列化
    if key_attributes is not None:
        key_attributes_str = json.dumps(key_attributes, ensure_ascii=False)
        logger.info(f"[数据库插入] key_attributes转换为JSON: {type(key_attributes)} -> {type(key_attributes_str)}")
    else:
        key_attributes_str = None

    logger.info("[数据库插入] JSON转换结果:")
    logger.info(f"[数据库插入] - relationships_str: {relationships_str} (type: {type(relationships_str)})")
    logger.info(f"[数据库插入] - key_attributes_str: {key_attributes_str} (type: {type(key_attributes_str)})")

    ins = person_memory.insert().values(
        user_id=user_id,
        person_id=person_id,
        is_user=is_user,
        canonical_name=canonical_name,
        aliases=aliases_str,
        relationships=relationships_str,
        profile_summary=profile_summary,
        key_attributes=key_attributes_str,
        avatar=avatar,
        intimacy_score=intimacy_score,
    )
    try:
        sql_client.insert_return_id(ins)
        logger.info(f"添加人员成功: {person_id} for user: {user_id}")
        return True
    except Exception as e:
        logger.error(f"添加人员失败: {e}")
        return False


def get_all_persons(user_id: str, limit: int = 100, offset: int = 0):  # 获取所有人员列表
    stmt = select(person_memory).where(person_memory.c.user_id == user_id).limit(limit).offset(offset)
    try:
        results = sql_client.select_many(stmt)
        persons = []
        for row in results:
            person = dict(row._mapping)
            if person["relationships"]:
                person["relationships"] = json.loads(person["relationships"])
            if person["key_attributes"]:
                person["key_attributes"] = json.loads(person["key_attributes"])
            persons.append(person)
        return persons
    except Exception as e:
        logger.error(f"获取人员列表失败: {e}")
        return []


def get_person_by_id(user_id: str, person_id: str, conn=None):  # 根据ID获取人员详情
    stmt = select(person_memory).where(and_(person_memory.c.person_id == person_id, person_memory.c.user_id == user_id))
    client = conn if conn else sql_client
    try:
        result = client.select_one(stmt)
        if result:
            person = dict(result._mapping)
            # JSON字段已经在服务层处理，这里保持原始数据
            return person
        return None
    except Exception as e:
        logger.error(f"获取人员详情失败: {e}")
        return None


# def update_person(user_id: str, person_id: str, conn=None, **kwargs):  # 更新人员信息
#     update_values = {}
#     for k, v in kwargs.items():
#         if v is not None:
#             if k in ["relationships", "key_attributes"] and isinstance(v, (dict, list)):
#                 update_values[k] = json.dumps(v, ensure_ascii=False)
#             else:
#                 update_values[k] = v
#     if not update_values:
#         return False
#     stmt = (
#         person_memory.update()
#         .where(and_(person_memory.c.person_id == person_id, person_memory.c.user_id == user_id))
#         .values(**update_values)
#     )
#     client = conn if conn else sql_client
#     try:
#         result = client.update(stmt)
#         logger.info(f"更新人员成功: {person_id} for user: {user_id}")
#         return result.rowcount > 0
#     except Exception as e:
#         logger.error(f"更新人员失败: {e}")
#         return False

###修改处
def _smart_merge_attributes(existing_attrs: dict, new_attrs: dict) -> dict:
    """
    智能合并属性，判断是增量更新还是覆盖更新
    
    Args:
        existing_attrs: 现有属性
        new_attrs: 新属性
        
    Returns:
        dict: 合并后的属性
    """
    if not existing_attrs:
        return new_attrs
    
    if not new_attrs:
        return existing_attrs
    
    result = existing_attrs.copy()
    
    for key, new_value in new_attrs.items():
        if key not in existing_attrs:
            # 新字段，直接添加
            result[key] = new_value
            logger.info(f"新增字段: {key} = {new_value}")
            continue
        
        existing_value = existing_attrs[key]
        
        # 处理嵌套字典（如基本信息）
        if isinstance(existing_value, dict) and isinstance(new_value, dict):
            # 递归合并嵌套字典
            merged_nested = _smart_merge_nested_dict(existing_value, new_value, key)
            result[key] = merged_nested
            logger.info(f"合并嵌套字典: {key} -> {merged_nested}")
        else:
            # 判断更新策略
            update_strategy = _determine_update_strategy(key, existing_value, new_value)
            
            if update_strategy == "replace":
                # 覆盖更新
                result[key] = new_value
                logger.info(f"覆盖更新: {key} {existing_value} -> {new_value}")
            elif update_strategy == "merge":
                # 增量更新
                merged_value = _merge_values(existing_value, new_value)
                result[key] = merged_value
                logger.info(f"增量更新: {key} {existing_value} + {new_value} -> {merged_value}")
            else:
                # 保持现有值
                logger.info(f"保持现有值: {key} = {existing_value}")
    
    return result


def _determine_update_strategy(key: str, existing_value: any, new_value: any) -> str:
    """
    判断更新策略：增量更新还是覆盖更新
    
    Args:
        key: 字段名
        existing_value: 现有值
        new_value: 新值
        
    Returns:
        str: "replace"、"merge" 或 "keep"
    """
    # 如果新值为空，保持现有值
    if not new_value:
        return "keep"
    
    # 如果现有值为空，直接替换
    if not existing_value:
        return "replace"
    
    # 基本信息下的所有字段都采用replace策略
    if key in ["基本信息"]:
        return "replace"
    
    # 基本信息下的子字段也采用replace策略
    basic_info_fields = [
        "家乡", "性别", "生日", "当前城市", "家庭情况",
        "职业信息", "联系方式"
    ]
    if key in basic_info_fields:
        return "replace"
    
    # 职业相关字段：如果新值与现有值不同，则覆盖
    if key in ["职业", "职位", "工作", "job", "position"]:
        if str(new_value).strip() != str(existing_value).strip():
            return "replace"
        else:
            return "keep"
    
    # 公司相关字段：如果新值与现有值不同，则覆盖
    elif key in ["公司", "工作单位", "company"]:
        if str(new_value).strip() != str(existing_value).strip():
            return "replace"
        else:
            return "keep"
    
    # 增量更新的字段
    elif key in ["兴趣", "期望", "关心话题"]:
        return "merge"

    elif key in ["餐饮偏好", "饮食偏好"]:
        return "merge"
    
    elif key in ["旅游历史", "旅行历史"]:
        return "merge"
    
    elif key in ["过往历史", "历史"]:
        return "merge"
    # 默认策略：如果值不同则覆盖
    if str(new_value).strip() != str(existing_value).strip():
        return "replace"
    else:
        return "keep"


def _merge_values(existing_value: any, new_value: any) -> any:
    """
    合并两个值，支持字符串和列表的合并
    
    Args:
        existing_value: 现有值
        new_value: 新值
        
    Returns:
        合并后的值
    """
    # 如果新值已经在现有值中，直接返回现有值
    if str(new_value).strip() in str(existing_value).strip():
        return existing_value
    
    # 处理列表类型
    if isinstance(existing_value, list) and isinstance(new_value, list):
        # 合并列表并去重
        combined = existing_value + new_value
        return list(dict.fromkeys(combined))
    
    elif isinstance(existing_value, list):
        # 现有值是列表，新值是单个值
        if new_value not in existing_value:
            return existing_value + [new_value]
        return existing_value
    
    elif isinstance(new_value, list):
        # 新值是列表，现有值是单个值
        if existing_value not in new_value:
            return [existing_value] + new_value
        return new_value
    
    else:
        # 都是字符串，用分号连接
        existing_str = str(existing_value).strip()
        new_str = str(new_value).strip()
        
        if existing_str and new_str:
            return f"{existing_str}；{new_str}"
        elif existing_str:
            return existing_str
        else:
            return new_str
            
def _smart_merge_nested_dict(existing_dict: dict, new_dict: dict, parent_key: str) -> dict:
    """
    智能合并嵌套字典
    
    Args:
        existing_dict: 现有嵌套字典
        new_dict: 新嵌套字典
        parent_key: 父级字段名
        
    Returns:
        dict: 合并后的嵌套字典
    """
    result = existing_dict.copy()
    
    for key, new_value in new_dict.items():
        if key not in existing_dict:
            # 新字段，直接添加
            result[key] = new_value
            logger.info(f"新增嵌套字段: {parent_key}.{key} = {new_value}")
            continue
        
        existing_value = existing_dict[key]
        
        # 如果父级是基本信息，所有子字段都采用replace策略
        if parent_key == "基本信息":
            if str(new_value).strip() != str(existing_value).strip():
                result[key] = new_value
                logger.info(f"基本信息覆盖更新: {parent_key}.{key} {existing_value} -> {new_value}")
            else:
                logger.info(f"基本信息保持现有值: {parent_key}.{key} = {existing_value}")
        else:
            # 其他嵌套字典的处理逻辑
            if isinstance(existing_value, dict) and isinstance(new_value, dict):
                # 递归处理更深层的嵌套
                result[key] = _smart_merge_nested_dict(existing_value, new_value, f"{parent_key}.{key}")
            else:
                # 判断更新策略
                update_strategy = _determine_update_strategy(key, existing_value, new_value)
                
                if update_strategy == "replace":
                    result[key] = new_value
                    logger.info(f"嵌套字段覆盖更新: {parent_key}.{key} {existing_value} -> {new_value}")
                elif update_strategy == "merge":
                    merged_value = _merge_values(existing_value, new_value)
                    result[key] = merged_value
                    logger.info(f"嵌套字段增量更新: {parent_key}.{key} {existing_value} + {new_value} -> {merged_value}")
                else:
                    logger.info(f"嵌套字段保持现有值: {parent_key}.{key} = {existing_value}")
    
    return result

def update_person(user_id: str, person_id: str, conn=None, **kwargs):  
    existing_person = get_person_by_id(user_id, person_id)
    if not existing_person:
        logger.error(f"人员不存在: {person_id}")
        return False
    # 解析现有的key_attributes
    existing_attrs = {}
    if existing_person.get("key_attributes"):
        try:
            if isinstance(existing_person["key_attributes"], str):
                existing_attrs = json.loads(existing_person["key_attributes"])
            else:
                existing_attrs = existing_person["key_attributes"]
        except (json.JSONDecodeError, TypeError):
            existing_attrs = {}# 更新人员信息
    # 处理新的key_attributes
    new_attrs = kwargs.get("key_attributes", {})
    if isinstance(new_attrs, str):
        try:
            new_attrs = json.loads(new_attrs)
        except (json.JSONDecodeError, TypeError):
            new_attrs = {}
    
    # 如果包含key_attributes，进行智能合并
    if new_attrs and isinstance(new_attrs, dict):
        merged_attrs = _smart_merge_attributes(existing_attrs, new_attrs)
        kwargs["key_attributes"] = merged_attrs
        logger.info(f"智能合并key_attributes完成: {existing_attrs} + {new_attrs} -> {merged_attrs}")
    
    update_values = {}
    for k, v in kwargs.items():
        if v is not None:
            if k in ["relationships", "key_attributes"] and isinstance(v, (dict, list)):
                update_values[k] = json.dumps(v, ensure_ascii=False)
            else:
                update_values[k] = v
    if not update_values:
        return False
    stmt = (
        person_memory.update()
        .where(and_(person_memory.c.person_id == person_id, person_memory.c.user_id == user_id))
        .values(**update_values)
    )
    client = conn if conn else sql_client
    try:
        result = client.update(stmt)
        logger.info(f"更新人员成功: {person_id} for user: {user_id}")
        return result.rowcount > 0
    except Exception as e:
        logger.error(f"更新人员失败: {e}")
        return False
####修改处

def search_persons_by_name(user_id: str, name: str, limit: int = 10):  # 按姓名、简介、属性等广泛搜索人员
    # 首先尝试精确匹配
    exact_stmt = (
        select(person_memory)
        .where(and_(person_memory.c.user_id == user_id, person_memory.c.canonical_name == name))
        .order_by(person_memory.c.updated_at.desc())
        .limit(limit)
    )

    try:
        exact_results = sql_client.select_many(exact_stmt)
        if exact_results:
            # 如果有精确匹配结果，直接返回
            persons = []
            for row in exact_results:
                person = dict(row._mapping)
                if person["relationships"]:
                    person["relationships"] = json.loads(person["relationships"])
                if person["key_attributes"]:
                    person["key_attributes"] = json.loads(person["key_attributes"])
                persons.append(person)
            return persons
    except Exception as e:
        logger.error(f"精确搜索失败: {e}")

    # 简单模糊匹配：直接使用传入的关键词
    # 上层AI已经生成了智能关键词，这里只需要进行模糊匹配
    pattern = f"%{name}%"

    # 构建模糊匹配查询
    conditions = [person_memory.c.canonical_name.like(pattern), person_memory.c.aliases.like(pattern)]

    fuzzy_stmt = (
        select(person_memory)
        .where(and_(person_memory.c.user_id == user_id, or_(*conditions)))
        .order_by((person_memory.c.canonical_name == name).desc(), person_memory.c.updated_at.desc())
        .limit(limit)
    )
    try:
        results = sql_client.select_many(fuzzy_stmt)
        persons = []
        for row in results:
            person = dict(row._mapping)
            if person["relationships"]:
                person["relationships"] = json.loads(person["relationships"])
            if person["key_attributes"]:
                person["key_attributes"] = json.loads(person["key_attributes"])
            persons.append(person)
        return persons
    except Exception as e:
        logger.error(f"搜索人员失败: {e}")
        return []


def delete_person(user_id: str, person_id: str, conn=None):  # 删除人员
    stmt = person_memory.delete().where(
        and_(person_memory.c.person_id == person_id, person_memory.c.user_id == user_id)
    )
    client = conn if conn else sql_client
    try:
        result = client.update(stmt)
        logger.info(f"删除人员成功: {person_id} for user: {user_id}")
        return result.rowcount > 0
    except Exception as e:
        logger.error(f"删除人员失败: {e}")
        return False
