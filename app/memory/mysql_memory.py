from __future__ import annotations

import asyncio
import json
import threading
import time
from datetime import datetime
from typing import Any, AsyncIterator, Dict, List, Optional, Tuple, Union

from configs.lion_config import get_value
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.base import BaseCheckpointSaver, Checkpoint, CheckpointTuple
from langgraph.checkpoint.serde.jsonplus import JsonPlusSerializer
from my_mysql.sql_client import CLIENT, execute, select_many, select_one
from service.ai_client import send_to_ai
from sqlalchemy import create_engine, inspect, text
from utils.logger import logger
from utils.thread_limiter import SafeAsyncExecutor


class MySQLCheckpointSerializer(JsonPlusSerializer):
    """序列化器，用于在JSON和对象之间转换，兼容MySQL。"""

    def dumps(self, obj: Any) -> str:
        """将对象序列化为JSON字符串。"""
        return super().dumps(obj)

    def loads(self, s: str) -> Any:
        """将JSON字符串反序列化为对象。"""
        if s is None:
            return None
        return super().loads(s)


class MySQLCheckpointSaver(BaseCheckpointSaver):
    """一个基于MySQL的LangGraph检查点保存器。"""

    serde = MySQLCheckpointSerializer()

    def __init__(self):
        super().__init__()
        self.create_table_if_not_exists()

    def create_table_if_not_exists(self):
        """如果表不存在，则创建chat_history表，并检查是否需要添加summary字段。"""
        with CLIENT.connect() as conn:
            # Use raw SQL query for compatibility, as inspect() fails with zebraproxy
            check_table_sql = text(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'chat_history'"
            )
            result = conn.execute(check_table_sql).fetchone()

            if not result:
                logger.info("表 'chat_history' 不存在，正在创建...")
                table_creation_sql = text(
                    """
                CREATE TABLE chat_history (
                    user_id VARCHAR(255) NOT NULL,
                    thread_id VARCHAR(255) NOT NULL,
                    thread_ts VARCHAR(255) NOT NULL,
                    parent_ts VARCHAR(255),
                    checkpoint LONGTEXT,
                    metadata LONGTEXT,
                    conversation_summary VARCHAR(500) DEFAULT NULL,
                    profile_updates JSON NULL COMMENT '本次对话中用户档案的更新内容',
                    intimacy_change INT DEFAULT 0 COMMENT '本次对话亲密度变化量',
                    events_added JSON NULL COMMENT '本次对话新增的事件ID列表',
                    PRIMARY KEY (user_id, thread_id, thread_ts)
                );
                """
                )
                conn.execute(table_creation_sql)
                conn.commit()
                logger.info("表 'chat_history' 创建成功。")
            else:
                # 检查是否已有conversation_summary字段
                check_column_sql = text(
                    """
                    SELECT COLUMN_NAME FROM information_schema.COLUMNS
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = 'chat_history'
                    AND COLUMN_NAME = 'conversation_summary'
                """
                )
                column_result = conn.execute(check_column_sql).fetchone()

                if not column_result:
                    logger.info("为 'chat_history' 表添加 'conversation_summary' 字段...")
                    add_column_sql = text(
                        """
                        ALTER TABLE chat_history
                        ADD COLUMN conversation_summary VARCHAR(500) DEFAULT NULL
                    """
                    )
                    conn.execute(add_column_sql)
                    conn.commit()
                    logger.info("字段 'conversation_summary' 添加成功。")

                # 跳过对话追踪字段检查（字段已手动创建）
                logger.info("跳过对话追踪字段检查，假设字段已存在")

            # 检查并创建性能优化索引
            self._create_performance_indexes(conn)



    def _create_performance_indexes(self, conn):
        """创建性能优化索引"""
        indexes_to_create = [
            {
                "name": "idx_user_thread",
                "sql": "CREATE INDEX idx_user_thread ON chat_history(user_id, thread_id)",
                "description": "用户和会话ID复合索引，优化摘要查询",
            },
            {
                "name": "idx_user_id",
                "sql": "CREATE INDEX idx_user_id ON chat_history(user_id)",
                "description": "用户ID索引，优化会话列表查询",
            },
        ]

        for index_info in indexes_to_create:
            try:
                # 检查索引是否已存在
                check_index_sql = text(
                    """
                    SELECT COUNT(*) FROM information_schema.statistics
                    WHERE table_schema = DATABASE()
                    AND table_name = 'chat_history'
                    AND index_name = :index_name
                """
                )
                result = conn.execute(check_index_sql.bindparams(index_name=index_info["name"])).fetchone()

                if result[0] == 0:
                    logger.info(f"创建索引: {index_info['name']} - {index_info['description']}")
                    conn.execute(text(index_info["sql"]))
                    conn.commit()
                    logger.info(f"索引 '{index_info['name']}' 创建成功")
                else:
                    logger.debug(f"索引 '{index_info['name']}' 已存在，跳过创建")

            except Exception as e:
                logger.warning(f"创建索引 '{index_info['name']}' 失败: {str(e)}")
                # 索引创建失败不应该影响应用启动，只记录警告

    def _ensure_checkpoint_compatibility(self, checkpoint_data):
        """确保 checkpoint 数据与新版本 LangGraph 兼容"""
        if checkpoint_data is None:
            return None

        # 确保 checkpoint_data 是字典类型
        if not isinstance(checkpoint_data, dict):
            logger.warning(f"检查点数据不是字典类型: {type(checkpoint_data)}")
            return checkpoint_data

        # 如果缺少 pending_sends 字段，添加空列表
        if "pending_sends" not in checkpoint_data:
            checkpoint_data["pending_sends"] = []
            logger.info("为旧版本 checkpoint 添加 pending_sends 字段")

        return checkpoint_data

    def get_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        thread_id = config["configurable"]["thread_id"]
        user_id = config["configurable"]["user_id"]
        thread_ts = config["configurable"].get("thread_ts")

        if thread_ts:
            sql = text(
                "SELECT checkpoint, parent_ts, metadata FROM chat_history WHERE user_id = :user_id AND thread_id = :thread_id AND thread_ts = :thread_ts"
            )
            result = select_one(sql.bindparams(user_id=user_id, thread_id=thread_id, thread_ts=thread_ts))
            if result:
                checkpoint_data = self.serde.loads(result[0])
                checkpoint_data = self._ensure_checkpoint_compatibility(checkpoint_data)
                return CheckpointTuple(
                    config={"configurable": {"thread_id": thread_id, "thread_ts": thread_ts, "user_id": user_id}},
                    checkpoint=checkpoint_data,
                    metadata=self.serde.loads(result[2]),
                    parent_config=(
                        {"configurable": {"thread_id": thread_id, "thread_ts": result[1], "user_id": user_id}}
                        if result[1]
                        else None
                    ),
                )
        else:
            sql = text(
                "SELECT checkpoint, thread_ts, parent_ts, metadata FROM chat_history WHERE user_id = :user_id AND thread_id = :thread_id ORDER BY thread_ts DESC LIMIT 1"
            )
            result = select_one(sql.bindparams(user_id=user_id, thread_id=thread_id))
            if result:
                checkpoint_data = self.serde.loads(result[0])
                checkpoint_data = self._ensure_checkpoint_compatibility(checkpoint_data)
                return CheckpointTuple(
                    config={"configurable": {"thread_id": thread_id, "thread_ts": result[1], "user_id": user_id}},
                    checkpoint=checkpoint_data,
                    metadata=self.serde.loads(result[3]),
                    parent_config=(
                        {"configurable": {"thread_id": thread_id, "thread_ts": result[2], "user_id": user_id}}
                        if result[2]
                        else None
                    ),
                )
        return None

    def list(self, config: RunnableConfig) -> List[RunnableConfig]:
        thread_id = config["configurable"]["thread_id"]
        user_id = config["configurable"]["user_id"]
        sql = text(
            "SELECT thread_id, thread_ts, user_id FROM chat_history WHERE user_id = :user_id AND thread_id = :thread_id ORDER BY thread_ts ASC"
        )
        results = select_many(sql.bindparams(user_id=user_id, thread_id=thread_id))
        return [{"configurable": {"thread_id": row[0], "thread_ts": row[1], "user_id": row[2]}} for row in results]

    def put(
        self, config: RunnableConfig, checkpoint: Union[Checkpoint, str], metadata: dict, *args, **kwargs
    ) -> RunnableConfig:
        """Saves a checkpoint, accepting either a dict or its serialized string representation."""
        if isinstance(checkpoint, str):
            if not checkpoint.strip():
                return config  # 忽略空的检查点字符串

            try:
                # 尝试将字符串解析为JSON，如果失败，则认为它是一个控制消息并忽略
                checkpoint_obj = self.serde.loads(checkpoint)
                # 确保检查点兼容性（添加 pending_sends 字段）
                checkpoint_obj = self._ensure_checkpoint_compatibility(checkpoint_obj)
                checkpoint_str = self.serde.dumps(checkpoint_obj)  # 重新序列化以包含兼容性修复
            except json.JSONDecodeError:
                logger.warning(f"忽略无法解析的检查点字符串（可能为LangGraph控制消息）: '{checkpoint}'")
                return config  # 返回原始配置，不做任何操作
        else:
            checkpoint_obj = checkpoint
            # 确保检查点兼容性（添加 pending_sends 字段）
            checkpoint_obj = self._ensure_checkpoint_compatibility(checkpoint_obj)
            checkpoint_str = self.serde.dumps(checkpoint_obj)

        thread_id = config["configurable"]["thread_id"]
        user_id = config["configurable"]["user_id"]
        # The 'ts' might not exist in control messages that we now ignore.
        # So we should only access it after we know we have a valid checkpoint object.
        if "ts" not in checkpoint_obj:
            logger.warning(f"检查点对象中缺少 'ts' 字段，无法保存: {checkpoint_obj}")
            return config

        thread_ts = checkpoint_obj["ts"]

        parent_tuple = self.get_tuple({"configurable": {"thread_id": thread_id, "user_id": user_id}})
        parent_ts = parent_tuple.config["configurable"]["thread_ts"] if parent_tuple else None

        sql = text(
            """
            INSERT INTO chat_history (user_id, thread_id, thread_ts, parent_ts, checkpoint, metadata)
            VALUES (:user_id, :thread_id, :thread_ts, :parent_ts, :checkpoint, :metadata)
            ON DUPLICATE KEY UPDATE parent_ts = :parent_ts, checkpoint = :checkpoint, metadata = :metadata;
        """
        )

        execute(
            sql.bindparams(
                user_id=user_id,
                thread_id=thread_id,
                thread_ts=thread_ts,
                parent_ts=parent_ts,
                checkpoint=checkpoint_str,
                metadata=self.serde.dumps(metadata),
            )
        )

        # 检查是否需要生成会话概括（当会话有足够的消息时）
        self._maybe_generate_summary(user_id, thread_id)

        # 检查是否需要保存档案快照
        try:
            # 从checkpoint的channel_values中获取state
            if hasattr(checkpoint, 'channel_values') and checkpoint.channel_values:
                state_data = checkpoint.channel_values
                if "memory_result" in state_data:
                    memory_result = state_data["memory_result"]
                    # 如果这是第一次保存checkpoint且有memory_result，说明对话刚开始
                    # 此时保存档案快照
                    if memory_result and not self._has_profile_snapshot(user_id, thread_id):
                        from utils.profile_diff import get_user_profile_snapshot
                        start_profile = get_user_profile_snapshot(user_id)
                        if start_profile:
                            self.save_conversation_snapshot(user_id, thread_id, start_profile)
                            logger.info(f"在checkpoint保存时补充档案快照: user_id={user_id}, thread_id={thread_id}")
        except Exception as e:
            logger.error(f"补充保存档案快照失败: {str(e)}")

        return {"configurable": {"thread_id": thread_id, "thread_ts": thread_ts, "user_id": user_id}}

    def put_writes(
        self,
        config: RunnableConfig,
        writes: List[Tuple[Checkpoint, dict]],
        *args,
        **kwargs,
    ) -> RunnableConfig:
        """保存一个检查点写入列表。"""
        for checkpoint, metadata in writes:
            config = self.put(config, checkpoint, metadata)
        return config

    def get_history(self, conversation_id: str, user_id: str) -> List[dict]:
        """获取指定对话的完整消息历史，过滤掉系统消息和重复的用户输入"""
        latest_checkpoint_tuple = self.get_tuple({"configurable": {"thread_id": conversation_id, "user_id": user_id}})

        history = []
        if latest_checkpoint_tuple and latest_checkpoint_tuple.checkpoint:
            messages = latest_checkpoint_tuple.checkpoint.get("channel_values", {}).get("messages", [])

            for msg in messages:
                # 只保留用户(human)和AI的消息
                if msg.type in ["human", "ai"]:
                    # 避免因内部循环导致重复记录用户输入
                    if not history or not (msg.type == history[-1]["type"] and msg.content == history[-1]["content"]):
                        history.append(
                            {"type": msg.type, "content": msg.content, "additional_kwargs": msg.additional_kwargs}
                        )
        return history

    def get_conversation_ids(self, user_id: str) -> List[str]:
        """获取指定用户的所有对话ID列表。"""
        sql = text("SELECT DISTINCT thread_id FROM chat_history WHERE user_id = :user_id ORDER BY thread_id DESC")
        results = select_many(sql.bindparams(user_id=user_id))
        return [row[0] for row in results]

    def _generate_chat_summary(self, history: List[dict]) -> str:
        """根据聊天历史生成概括（优化版：增加超时控制）"""
        try:
            if not history:
                return "空会话"

            # 构建聊天内容文本
            chat_text = ""
            for msg in history:
                role = "用户" if msg["type"] == "human" else "AI"
                content = msg["content"][:200]  # 限制每条消息长度
                chat_text += f"{role}: {content}\n"

            # 如果聊天内容太短，直接返回简单概括
            if len(chat_text.strip()) < 20:
                return "简短对话"

            # 使用AI生成概括（增加超时控制）
            prompt = "请为以下对话生成一个简洁的标题概括（不超过20个字），突出对话的主要内容或话题："

            query_input = {
                "model": get_value("humanrelation.memory_extraction_model", "gpt-4o-mini"),
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": chat_text[:1000]},  # 限制输入长度
                ],
                "stream": False,
                "temperature": 0.3,
                "max_tokens": 50,
            }

            # 增加超时控制：3秒快速失败
            start_time = time.time()
            try:
                response = send_to_ai(query_input)
                elapsed = time.time() - start_time

                if elapsed > 5:  # 如果超过5秒，记录慢查询
                    logger.warning(f"摘要生成耗时过长: {elapsed:.2f}秒")

                if response and response.status_code == 200:
                    response_data = json.loads(response.text)
                    summary = response_data["choices"][0]["message"]["content"].strip()
                    return summary if summary else "对话记录"
                else:
                    return "对话记录"
            except Exception as e:
                elapsed = time.time() - start_time
                logger.error(f"AI摘要生成失败 (耗时{elapsed:.2f}秒): {str(e)}")
                return "对话记录"

        except Exception as e:
            logger.error(f"生成聊天概括失败: {str(e)}")
            return "对话记录"

    def get_stored_summary(self, user_id: str, thread_id: str) -> Optional[str]:
        """从chat_history表获取已存储的会话概括"""
        try:
            sql = text(
                """
                SELECT conversation_summary FROM chat_history
                WHERE user_id = :user_id AND thread_id = :thread_id
                AND conversation_summary IS NOT NULL
                LIMIT 1
            """
            )
            result = select_one(sql.bindparams(user_id=user_id, thread_id=thread_id))
            return result[0] if result else None
        except Exception as e:
            logger.error(f"获取会话概括失败: {str(e)}")
            return None

    def save_summary(self, user_id: str, thread_id: str, summary: str):
        """保存会话概括到chat_history表"""
        try:
            sql = text(
                """
                UPDATE chat_history
                SET conversation_summary = :summary
                WHERE user_id = :user_id AND thread_id = :thread_id
            """
            )
            execute(sql.bindparams(user_id=user_id, thread_id=thread_id, summary=summary))
            logger.info(f"会话概括已保存: {thread_id} -> {summary}")
        except Exception as e:
            logger.error(f"保存会话概括失败: {str(e)}")

    def _maybe_generate_summary(self, user_id: str, thread_id: str):
        """检查是否需要为会话生成概括（异步优化版）"""
        try:
            # 检查是否已有概括
            existing_summary = self.get_stored_summary(user_id, thread_id)
            if existing_summary:
                return  # 已有概括，无需重新生成

            # 获取会话历史
            history = self.get_history(thread_id, user_id)

            # 只有当会话有至少2轮对话时才异步生成概括
            if len(history) >= 4:  # 至少2轮对话（用户+AI+用户+AI）
                # 异步生成摘要，不阻塞主流程
                def generate_summary_async():
                    try:
                        summary = self._generate_chat_summary(history)
                        self.save_summary(user_id, thread_id, summary)
                        logger.info(f"✅ 异步生成摘要完成: {thread_id} -> {summary}")
                    except Exception as e:
                        logger.error(f"❌ 异步摘要生成失败: {str(e)}")

                # 使用安全的异步执行器
                success = SafeAsyncExecutor.execute_async(generate_summary_async)
                if success:
                    logger.info(f"🚀 已启动异步摘要生成任务: {thread_id}")
                else:
                    logger.warning(f"⚠️ 系统负载过高，跳过摘要生成: {thread_id}")

        except Exception as e:
            logger.error(f"检查生成会话概括失败: {str(e)}")

    def get_conversations_with_summary(self, user_id: str) -> List[dict]:
        """获取指定用户的所有对话ID列表，包含聊天内容概括（优化版：快速失败）"""
        try:
            # 优化：使用单次查询获取所有会话及其摘要
            conversations_with_summaries = self._get_conversations_with_summaries_batch(user_id)
            conversations = []

            for conv_data in conversations_with_summaries:
                conversation_id = conv_data["conversation_id"]
                stored_summary = conv_data["summary"]

                try:
                    if stored_summary:
                        # 使用已存储的概括
                        summary = stored_summary
                    else:
                        # 快速生成概括，超时则使用默认值
                        history = self.get_history(conversation_id, user_id)

                        # 设置3秒超时限制
                        start_time = time.time()
                        try:
                            summary = self._generate_chat_summary(history)
                            elapsed = time.time() - start_time
                            if elapsed > 3:
                                logger.warning(f"摘要生成耗时 {elapsed:.2f}秒，建议优化")
                        except Exception:
                            # 生成失败时使用默认摘要
                            summary = f"对话记录 {len(history)}条消息"
                            logger.warning(f"摘要生成失败，使用默认值: {conversation_id}")

                        # 异步保存摘要
                        def save_async():
                            try:
                                self.save_summary(user_id, conversation_id, summary)
                            except Exception as e:
                                logger.error(f"异步保存摘要失败: {e}")

                        SafeAsyncExecutor.execute_async(save_async)

                    conversations.append({"conversation_id": conversation_id, "summary": summary})
                except Exception as e:
                    logger.error(f"处理会话 {conversation_id} 失败: {str(e)}")
                    conversations.append({"conversation_id": conversation_id, "summary": "对话记录"})

            return conversations

        except Exception as e:
            logger.error(f"批量获取会话摘要失败，回退到原方法: {str(e)}")
            # 回退到原来的方法
            return self._get_conversations_with_summary_fallback(user_id)

    def get_conversations_with_summary_paginated(self, user_id: str, limit: int = 20, offset: int = 0) -> dict:
        """获取指定用户的对话ID列表，包含聊天内容概括（支持分页）

        Args:
            user_id: 用户ID
            limit: 每页返回的会话数量，默认20
            offset: 偏移量，默认0

        Returns:
            dict: 包含会话列表、总数、分页信息的字典
        """
        try:
            # 优化：使用单次查询获取分页会话及其摘要
            conversations_with_summaries, total_count = self._get_conversations_with_summaries_batch_paginated(
                user_id, limit, offset
            )
            conversations = []

            for conv_data in conversations_with_summaries:
                conversation_id = conv_data["conversation_id"]
                stored_summary = conv_data["summary"]

                try:
                    if stored_summary:
                        # 使用已存储的概括
                        summary = stored_summary
                    else:
                        # 没有摘要时，先返回"生成中"，然后异步生成
                        summary = "生成中..."
                        # 异步生成摘要（不阻塞当前请求）
                        self._async_generate_summary(user_id, conversation_id)

                    conversations.append({"conversation_id": conversation_id, "summary": summary})
                except Exception as e:
                    logger.error(f"处理会话 {conversation_id} 失败: {str(e)}")
                    conversations.append({"conversation_id": conversation_id, "summary": "对话记录"})

            return {
                "conversations": conversations,
                "total_count": total_count,
                "current_page": offset // limit + 1,
                "page_size": limit,
                "has_more": offset + limit < total_count,
            }

        except Exception as e:
            logger.error(f"批量获取会话摘要失败: {str(e)}")
            return {"conversations": [], "total_count": 0, "current_page": 1, "page_size": limit, "has_more": False}

    def _get_conversations_with_summaries_batch(self, user_id: str) -> List[dict]:
        """批量获取用户的所有会话ID和摘要（优化版本）"""
        try:
            sql = text(
                """
                SELECT DISTINCT thread_id, conversation_summary
                FROM chat_history
                WHERE user_id = :user_id
                ORDER BY thread_id DESC
            """
            )
            results = select_many(sql.bindparams(user_id=user_id))

            conversations = []
            for row in results:
                conversations.append({"conversation_id": row[0], "summary": row[1] if row[1] else None})

            return conversations

        except Exception as e:
            logger.error(f"批量获取会话摘要失败: {str(e)}")
            raise

    def _get_conversations_with_summaries_batch_paginated(
        self, user_id: str, limit: int = 20, offset: int = 0
    ) -> tuple[List[dict], int]:
        """批量获取用户的会话ID和摘要（分页版本）

        Returns:
            tuple: (conversations_list, total_count)
        """
        try:
            # 先获取总数
            count_sql = text(
                """
                SELECT COUNT(DISTINCT thread_id)
                FROM chat_history
                WHERE user_id = :user_id
            """
            )
            count_result = select_one(count_sql.bindparams(user_id=user_id))
            total_count = count_result[0] if count_result else 0

            # 获取分页数据
            sql = text(
                """
                SELECT DISTINCT thread_id, conversation_summary
                FROM chat_history
                WHERE user_id = :user_id
                ORDER BY thread_id DESC
                LIMIT :limit OFFSET :offset
            """
            )
            results = select_many(sql.bindparams(user_id=user_id, limit=limit, offset=offset))

            conversations = []
            for row in results:
                conversations.append({"conversation_id": row[0], "summary": row[1] if row[1] else None})

            return conversations, total_count

        except Exception as e:
            logger.error(f"批量获取分页会话摘要失败: {str(e)}")
            raise

    def _get_conversations_with_summary_fallback(self, user_id: str) -> List[dict]:
        """回退方法：使用原来的逐个查询方式"""
        conversation_ids = self.get_conversation_ids(user_id)
        conversations = []

        for conversation_id in conversation_ids:
            try:
                # 首先尝试从数据库获取已存储的概括
                stored_summary = self.get_stored_summary(user_id, conversation_id)

                if stored_summary:
                    # 使用已存储的概括
                    summary = stored_summary
                else:
                    # 没有摘要时，先返回"生成中"，然后异步生成
                    summary = "生成中..."
                    # 异步生成摘要（不阻塞当前请求）
                    self._async_generate_summary(user_id, conversation_id)

                conversations.append({"conversation_id": conversation_id, "summary": summary})
            except Exception as e:
                logger.error(f"处理会话 {conversation_id} 失败: {str(e)}")
                conversations.append({"conversation_id": conversation_id, "summary": "对话记录"})

        return conversations

    def _async_generate_summary(self, user_id: str, conversation_id: str):
        """异步生成会话摘要（不阻塞当前请求）"""

        def generate_summary_task():
            try:
                # 检查是否已经有摘要了（避免重复生成）
                existing_summary = self.get_stored_summary(user_id, conversation_id)
                if existing_summary and existing_summary != "生成中...":
                    return  # 已有摘要，无需重新生成

                # 生成摘要
                history = self.get_history(conversation_id, user_id)
                if len(history) >= 2:  # 至少有一轮对话才生成摘要
                    summary = self._generate_chat_summary(history)
                    self.save_summary(user_id, conversation_id, summary)
                    logger.info(f"异步生成摘要完成: {conversation_id} -> {summary}")
                else:
                    # 对话太少，使用默认摘要
                    self.save_summary(user_id, conversation_id, "新对话")

            except Exception as e:
                logger.error(f"异步生成摘要失败: {conversation_id}, 错误: {str(e)}")
                # 生成失败时保存默认摘要
                try:
                    self.save_summary(user_id, conversation_id, "对话记录")
                except Exception as save_e:
                    logger.error(f"保存默认摘要失败: {save_e}")

        # 使用安全的异步执行器
        SafeAsyncExecutor.execute_async(generate_summary_task)

    async def aget_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        return await asyncio.get_running_loop().run_in_executor(None, self.get_tuple, config)

    async def alist(self, config: RunnableConfig) -> AsyncIterator[RunnableConfig]:
        results = await asyncio.get_running_loop().run_in_executor(None, self.list, config)
        for res in results:
            yield res

    async def aput(
        self, config: RunnableConfig, checkpoint: Checkpoint, metadata: dict, *args, **kwargs
    ) -> RunnableConfig:
        return await asyncio.get_running_loop().run_in_executor(
            None, self.put, config, checkpoint, metadata, *args, **kwargs
        )

    async def aput_writes(
        self,
        config: RunnableConfig,
        writes: List[Tuple[Checkpoint, dict]],
        *args,
        **kwargs,
    ) -> RunnableConfig:
        """异步接口：保存一个检查点写入列表。"""
        return await asyncio.get_running_loop().run_in_executor(None, self.put_writes, config, writes, *args, **kwargs)

    def save_conversation_snapshot(self, user_id: str, thread_id: str, profile_snapshot: dict):
        """保存对话开始时的档案快照到独立表"""
        try:
            logger.info(f"保存对话档案快照: user_id={user_id}, thread_id={thread_id}")

            import json

            # 使用独立表存储档案快照，避免与LangGraph冲突
            insert_sql = text("""
                INSERT INTO conversation_profile_snapshots (user_id, thread_id, profile_snapshot)
                VALUES (:user_id, :thread_id, :profile_snapshot)
                ON DUPLICATE KEY UPDATE
                profile_snapshot = VALUES(profile_snapshot),
                update_time = CURRENT_TIMESTAMP
            """)

            logger.info(f"准备执行SQL: user_id={user_id}, thread_id={thread_id}")

            execute(insert_sql.bindparams(
                user_id=user_id,
                thread_id=thread_id,
                profile_snapshot=json.dumps(profile_snapshot, ensure_ascii=False)
            ))

            logger.info(f"SQL执行完成")

            logger.info(f"档案快照保存成功: user_id={user_id}, thread_id={thread_id}")

        except Exception as e:
            logger.error(f"保存档案快照失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

    def get_conversation_snapshot(self, user_id: str, thread_id: str) -> dict:
        """获取对话开始时的档案快照"""
        try:
            logger.info(f"获取对话档案快照: user_id={user_id}, thread_id={thread_id}")

            # 从独立表查询档案快照
            query_sql = text("""
                SELECT profile_snapshot
                FROM conversation_profile_snapshots
                WHERE user_id = :user_id
                AND thread_id = :thread_id
            """)

            result = select_one(query_sql.bindparams(user_id=user_id, thread_id=thread_id))

            logger.info(f"查询结果: {result}")

            if result and result[0]:
                import json
                profile_snapshot_str = result[0]
                logger.info(f"原始profile_snapshot: {profile_snapshot_str}")

                profile_snapshot = json.loads(profile_snapshot_str) if profile_snapshot_str else {}
                logger.info(f"解析后的profile_snapshot: {profile_snapshot}")

                logger.info(f"获取到档案快照: {bool(profile_snapshot)}")
                return profile_snapshot
            else:
                logger.info("查询结果为空或profile_snapshot为空")
                return {}

        except Exception as e:
            logger.error(f"获取档案快照失败: {str(e)}")
            return {}

    def save_conversation_changes(self, user_id: str, thread_id: str, profile_updates: dict, intimacy_change: int, events_added: list):
        """保存对话变化信息"""
        try:
            logger.info(f"开始保存对话变化: user_id={user_id}, thread_id={thread_id}")
            logger.info(f"变化内容: profile_updates={profile_updates}, intimacy_change={intimacy_change}, events_added={events_added}")

            # 检查字段是否存在
            if not self._check_conversation_fields_exist():
                logger.warning("对话追踪字段不存在，跳过保存")
                return

            import json

            # 先查询最新的thread_ts
            max_ts_sql = text("""
                SELECT MAX(thread_ts)
                FROM chat_history
                WHERE user_id = :user_id AND thread_id = :thread_id
            """)

            max_ts_result = select_one(max_ts_sql.bindparams(user_id=user_id, thread_id=thread_id))

            if not max_ts_result or not max_ts_result[0]:
                logger.warning(f"未找到对话记录: user_id={user_id}, thread_id={thread_id}")
                return

            max_thread_ts = max_ts_result[0]
            logger.info(f"找到最新的thread_ts: {max_thread_ts}")

            # 更新指定的记录
            update_sql = text("""
                UPDATE chat_history
                SET profile_updates = :profile_updates,
                    intimacy_change = :intimacy_change,
                    events_added = :events_added
                WHERE user_id = :user_id
                AND thread_id = :thread_id
                AND thread_ts = :thread_ts
            """)

            logger.info(f"执行更新SQL: {update_sql}")

            result = execute(update_sql.bindparams(
                user_id=user_id,
                thread_id=thread_id,
                thread_ts=max_thread_ts,
                profile_updates=json.dumps(profile_updates, ensure_ascii=False) if profile_updates else None,
                intimacy_change=intimacy_change,
                events_added=json.dumps(events_added, ensure_ascii=False) if events_added else None
            ))

            logger.info(f"SQL执行结果: {result}")
            logger.info(f"保存对话变化成功: user_id={user_id}, thread_id={thread_id}")

        except Exception as e:
            logger.error(f"保存对话变化失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

    def get_conversation_changes(self, user_id: str, thread_id: str) -> dict:
        """获取对话变化信息"""
        try:
            logger.info(f"开始获取对话变化: user_id={user_id}, thread_id={thread_id}")

            # 首先检查字段是否存在
            if not self._check_conversation_fields_exist():
                logger.warning("对话追踪字段不存在，返回空结果")
                return {
                    "profile_updates": {},
                    "intimacy_change": 0,
                    "events_added": []
                }

            # 先查询最新的thread_ts
            max_ts_sql = text("""
                SELECT MAX(thread_ts)
                FROM chat_history
                WHERE user_id = :user_id AND thread_id = :thread_id
            """)

            max_ts_result = select_one(max_ts_sql.bindparams(user_id=user_id, thread_id=thread_id))

            if not max_ts_result or not max_ts_result[0]:
                logger.info(f"未找到对话记录: user_id={user_id}, thread_id={thread_id}")
                return {
                    "profile_updates": {},
                    "intimacy_change": 0,
                    "events_added": []
                }

            max_thread_ts = max_ts_result[0]
            logger.info(f"找到最新的thread_ts: {max_thread_ts}")

            # 查询指定记录的变化信息
            query_sql = text("""
                SELECT profile_updates, intimacy_change, events_added
                FROM chat_history
                WHERE user_id = :user_id
                AND thread_id = :thread_id
                AND thread_ts = :thread_ts
            """)

            logger.info(f"执行查询SQL: {query_sql}")
            result = select_one(query_sql.bindparams(user_id=user_id, thread_id=thread_id, thread_ts=max_thread_ts))
            logger.info(f"查询结果: {result}")

            if result:
                import json
                profile_updates = json.loads(result[0]) if result[0] else {}
                intimacy_change = result[1] or 0
                events_added = json.loads(result[2]) if result[2] else []

                logger.info(f"解析后的结果: profile_updates={profile_updates}, intimacy_change={intimacy_change}, events_added={events_added}")

                return {
                    "profile_updates": profile_updates,
                    "intimacy_change": intimacy_change,
                    "events_added": events_added
                }
            else:
                logger.info("未找到对话记录，返回空结果")
                return {
                    "profile_updates": {},
                    "intimacy_change": 0,
                    "events_added": []
                }

        except Exception as e:
            logger.error(f"获取对话变化失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {
                "profile_updates": {},
                "intimacy_change": 0,
                "events_added": []
            }

    def _check_conversation_fields_exist(self) -> bool:
        """检查对话追踪字段是否存在"""
        try:
            check_sql = text("""
                SELECT COLUMN_NAME FROM information_schema.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'chat_history'
                AND COLUMN_NAME IN ('profile_updates', 'intimacy_change', 'events_added')
            """)

            result = select_many(check_sql)
            existing_fields = [row[0] for row in result] if result else []

            required_fields = ['profile_updates', 'intimacy_change', 'events_added']
            missing_fields = [field for field in required_fields if field not in existing_fields]

            if missing_fields:
                logger.warning(f"缺少对话追踪字段: {missing_fields}")
                return False
            else:
                logger.info("所有对话追踪字段都存在")
                return True

        except Exception as e:
            logger.error(f"检查字段存在性失败: {str(e)}")
            return False

    def _has_profile_snapshot(self, user_id: str, thread_id: str) -> bool:
        """检查是否已经有档案快照"""
        try:
            check_sql = text("""
                SELECT id
                FROM conversation_profile_snapshots
                WHERE user_id = :user_id
                AND thread_id = :thread_id
                LIMIT 1
            """)

            result = select_one(check_sql.bindparams(user_id=user_id, thread_id=thread_id))
            return result is not None

        except Exception as e:
            logger.error(f"检查档案快照存在性失败: {str(e)}")
            return False
