"""
性能配置管理 - 集中管理超时、限制等配置
"""
from configs.lion_config import get_value

class PerformanceConfig:
    """性能相关配置"""
    
    # 数据库配置
    DB_POOL_SIZE = get_value("db.pool_size", 20)
    DB_MAX_OVERFLOW = get_value("db.max_overflow", 30)
    DB_CONNECTION_TIMEOUT = get_value("db.connection_timeout", 5)
    DB_READ_TIMEOUT = get_value("db.read_timeout", 10)
    DB_SLOW_QUERY_THRESHOLD = get_value("db.slow_query_threshold", 0.5)  # 秒
    
    # 网络请求配置
    HTTP_DEFAULT_TIMEOUT = get_value("http.default_timeout", 10)
    HTTP_CRAWL_TIMEOUT = get_value("http.crawl_timeout", 8)
    HTTP_AI_TIMEOUT = get_value("http.ai_timeout", 6)
    HTTP_RETRY_COUNT = get_value("http.retry_count", 3)
    
    # 线程池配置
    MAX_BACKGROUND_THREADS = get_value("threads.max_background", 30)
    MAX_CRAWL_WORKERS = get_value("threads.max_crawl_workers", 6)
    THREAD_POOL_TIMEOUT = get_value("threads.pool_timeout", 60)
    
    # 缓存配置
    SEARCH_CACHE_TTL = get_value("cache.search_ttl", 300)  # 5分钟
    SLOW_SITE_SKIP_DURATION = get_value("cache.slow_site_skip", 300)  # 5分钟
    
    # 熔断器配置
    CIRCUIT_BREAKER_THRESHOLD = get_value("circuit.failure_threshold", 3)
    CIRCUIT_BREAKER_TIMEOUT = get_value("circuit.timeout", 60)
    
    # 监控配置
    MONITOR_INTERVAL = get_value("monitor.interval", 30)  # 秒
    ALERT_DB_CONNECTION_THRESHOLD = get_value("monitor.db_connection_alert", 40)
    ALERT_THREAD_COUNT_THRESHOLD = get_value("monitor.thread_count_alert", 100)
    ALERT_MEMORY_THRESHOLD = get_value("monitor.memory_alert", 85)  # 百分比
    
    # AI请求配置
    AI_SUMMARY_TIMEOUT = get_value("ai.summary_timeout", 5)
    AI_MAX_TOKENS_SIMPLE = get_value("ai.max_tokens_simple", 100)
    AI_MAX_TOKENS_MEDIUM = get_value("ai.max_tokens_medium", 500)
    AI_MAX_TOKENS_COMPLEX = get_value("ai.max_tokens_complex", 1000)
    
    @classmethod
    def get_adaptive_timeout(cls, task_type: str = "default") -> int:
        """根据任务类型获取自适应超时时间"""
        timeout_map = {
            "db": cls.DB_CONNECTION_TIMEOUT,
            "http": cls.HTTP_DEFAULT_TIMEOUT,
            "crawl": cls.HTTP_CRAWL_TIMEOUT,
            "ai": cls.HTTP_AI_TIMEOUT,
            "summary": cls.AI_SUMMARY_TIMEOUT,
            "default": cls.HTTP_DEFAULT_TIMEOUT
        }
        return timeout_map.get(task_type, cls.HTTP_DEFAULT_TIMEOUT)
    
    @classmethod
    def is_high_load(cls) -> bool:
        """检查系统是否处于高负载状态"""
        import threading
        import psutil
        
        try:
            # 检查线程数
            if threading.active_count() > cls.ALERT_THREAD_COUNT_THRESHOLD:
                return True
            
            # 检查内存使用率
            if psutil.virtual_memory().percent > cls.ALERT_MEMORY_THRESHOLD:
                return True
                
            return False
        except Exception:
            return False  # 检查失败时假设不是高负载
    
    @classmethod
    def get_degraded_config(cls) -> dict:
        """获取降级配置（高负载时使用）"""
        return {
            "max_crawl_workers": max(cls.MAX_CRAWL_WORKERS // 2, 2),
            "http_timeout": max(cls.HTTP_CRAWL_TIMEOUT // 2, 3),
            "max_background_threads": max(cls.MAX_BACKGROUND_THREADS // 2, 10),
            "cache_ttl": cls.SEARCH_CACHE_TTL * 2,  # 延长缓存时间
        }

# 全局配置实例
perf_config = PerformanceConfig()
