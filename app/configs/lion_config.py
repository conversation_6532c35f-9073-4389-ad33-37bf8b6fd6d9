# -*- coding: utf-8 -*-
import hashlib
import hmac
import json
import threading
import traceback
from base64 import encodebytes
from datetime import datetime
from urllib import parse

import requests
from configs.config import APP_KEY, CURRENT_ENV
from utils.logger import logger


class LionClient(object):
    def __init__(self, client_id: str, client_secret: str, env: str, app_name: str):
        """
        Args:
            client_id:      从lion那边申请到的 ba id （虚拟账号）
            client_secret:  从lion那边申请到的 ba token （lion为虚拟账号生成的密码 ）
            app_name:       项目名，e.g. com.sankuai.dialogstudio.xiaomei.toolexecute
            env:            环境 [dev,test,ppe,staging,prod]

        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.app_name = app_name
        self.env = env
        self.domain = self.get_domain()  # 根据指定环境获取 url

    def post_auth(self, client_id: str, client_secret: str, url: str):
        """
        鉴权
        :return:
        """
        gmt_time = datetime.utcnow().strftime("%a, %d %b %Y %H:%M:%S GMT")
        string2sign = "GET %s\n%s" % (parse.urlparse(url).path, gmt_time)
        signature = encodebytes(hmac.new(str.encode(client_secret), str.encode(string2sign), hashlib.sha1).digest())
        return dict(Date=gmt_time, Authorization="MWS " + client_id + ":" + bytes.decode(signature).replace("\n", ""))

    def get_domain(self):
        """
        根据不同的环境获取 url
            线上：http://lion.vip.sankuai.com，包含环境 env = prod、staging；
            线下：http://lion-api.inf.test.sankuai.com，包含环境 env = dev、test；
        """
        if self.env in ["prod", "staging"]:
            return "http://lion.vip.sankuai.com"
        else:
            return "http://lion-api.inf.test.sankuai.com"

    def get_value_from_lion(self, lion_key: str, max_retries: int = 2) -> str:
        """
        根据 key 从 lion 中获取配置的 value
        Args:
            lion_key: 创建时的 key，e.g.galileo_prompt.summary"
            max_retries: 最大重试次数
        """

        url = self.domain + "/v3/configs/envs/{}/appkeys/{}/keys/{}".format(self.env, self.app_name, lion_key)

        for attempt in range(max_retries + 1):
            try:
                auth = self.post_auth(self.client_id, self.client_secret, url)
                # 增加超时时间
                response_json = requests.get(url, headers=auth, timeout=15).json()
                if response_json is None:
                    return None
                result = response_json.get("result", {})
                if not result:
                    return None
                return result.get("value", "")
            except requests.exceptions.Timeout:
                if attempt < max_retries:
                    logger.warning(
                        f"get_value_from_lion timeout (attempt {attempt + 1}/{max_retries + 1}), retrying... lion_key: {lion_key}"
                    )
                    continue
                else:
                    logger.error(f"get_value_from_lion timeout after {max_retries + 1} attempts, lion_key: {lion_key}")
                    return ""
            except requests.exceptions.RequestException as e:
                if attempt < max_retries:
                    logger.warning(
                        f"get_value_from_lion network error (attempt {attempt + 1}/{max_retries + 1}), retrying... lion_key: {lion_key}"
                    )
                    continue
                else:
                    logger.error(
                        f"get_value_from_lion network error after {max_retries + 1} attempts, lion_key: {lion_key}"
                    )
                    logger.error(str(e))
                    return ""
            except Exception as e:
                logger.error(f"get_value_from_lion failed., lion_key: {lion_key}")
                logger.error(str(e))
                return ""

        return ""

    def get_value(self, lion_key: str, default=None) -> str:
        """
        获取单个lion配置value
        （可以做个 cache，但是 yangwenqing 建议，现在这种「直接调用而不是批量跑全部」情况下可以实时从 lion 里拿）
        Args:
            lion_key: 创建时的 key，e.g.galileo_prompt.summary"
        :return:
        """
        try:
            res = self.get_value_from_lion(lion_key)
            if res:
                return res
            else:
                return default
        except Exception as e:
            logger.error(f"get_value failed., lion_key: {lion_key}")
            logger.exception(e)
            return default

    def set_value_to_lion(self, lion_key, value):
        try:
            # ... existing code ...
            pass
        except Exception as e:
            logger.error(f"set_value_to_lion failed., lion_key: {lion_key}")
            logger.exception(e)


LION_CLIENT_ID = "xiaomei_toolexecute_admin"
LION_SECRET = "XFDCTVU8SC"

lion_client = LionClient(client_id=LION_CLIENT_ID, client_secret=LION_SECRET, env=CURRENT_ENV, app_name=APP_KEY)


def get_value(key, default=None):
    # from utils.logger import logger
    try:
        return lion_client.get_value(key, default)
    except Exception as e:
        logger.error(f"Failed to get value from lion: {e}, return default instead: {default}")
        return default
