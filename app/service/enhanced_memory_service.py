########################################################
# 增强记忆管理服务 - 实现"以人物为中心"的完整记忆机制
########################################################

import json
import re
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from configs.lion_config import get_value
from service.ai_client import send_to_ai
from service.ESmemory.es_event_service import (
    add_event,
    get_recent_events,
    search_events_by_text,
    update_participant_in_events,
)
from service.mysql_person_service import (
    add_person,
    ensure_user_profile_exists,
    get_all_persons_mysql,
    get_person_by_id_mysql,
    get_user_person,
    search_persons_by_name_mysql,
    update_person_mysql,
)
from service.user_context_manager import UserContextManager
from service.user_profile_standardizer import user_profile_standardizer
from sqlalchemy import and_
from utils.logger import logger, safe_json_log
from utils.utils import timeit


class EnhancedMemoryService:
    def __init__(self):
        self.event_index = get_value("humanrelation.event_index_name", "memory_event_store")
        self.user_context_manager = UserContextManager()
        # 🚀 优化2: 添加意图分析缓存
        self._intent_cache = {}
        self._intent_cache_max_size = 100
        self._intent_cache_ttl = 300  # 5分钟缓存

    def extract_and_process_memory(self, conversation_text: str, user_id: str, conversation_id: str = None):  # 步骤一：输入处理与实体识别
        """从对话中提取记忆，并根据情况返回结构化指令"""
        try:
            extraction_result = self._extract_memory_from_conversation(conversation_text, user_id)
            if extraction_result.get("result") != "success":
                return extraction_result

            data = extraction_result.get("data", {})
            intent = data.get("intent", "add_memory")

            # 【修复】处理查询意图 - 跳转到查询处理流程
            if intent == "query":
                logger.info(f"识别到查询意图，跳转到查询处理流程: {data.get('query_text', conversation_text)}")
                query_text = data.get("query_text", conversation_text)
                # 提取用户输入部分（去掉Assistant:前缀）
                if "User: " in query_text:
                    query_text = query_text.split("User: ")[-1].strip()

                # 调用查询处理流程
                query_result = self.retrieve_memory_for_conversation(query_text, user_id, 5)
                return {"action_code": "QUERY_RESULT", "payload": {"query_text": query_text, "result": query_result}}

            # 处理不同类型的意图
            if intent == "merge_persons" and len(data.get("persons", [])) == 2:
                merge_result = self._handle_auto_merge_intent(data["persons"], user_id)
                if merge_result:
                    return merge_result
                # 如果 merge_result 为 None，则表示无法合并，将按默认流程处理

            elif intent == "alias_relation":
                return self._handle_alias_relation(data, user_id)
            elif intent == "rename_relation":
                return self._handle_rename_relation(data, user_id)

            # 正常的添加记忆流程 (也是合并失败时的后备流程)
            processed_persons, processed_events = [], []
            for person_data in data.get("persons", []):
                # --- 时间表达式转换 ---
                if person_data.get("profile_summary"):
                    person_data["profile_summary"] = self._replace_relative_time(person_data["profile_summary"])
                if person_data.get("key_attributes"):
                    for k, v in person_data["key_attributes"].items():
                        if isinstance(v, str):
                            person_data["key_attributes"][k] = self._replace_relative_time(v)
                # --- 结束 ---执行新增记忆
                processed_persons.append(
                    self._process_person_with_temporal_info(person_data, conversation_text, user_id)
                )

            # 处理双向关系：确保父女、夫妻等关系在双方档案中都有体现
            self._process_bidirectional_relationships(data.get("persons", []), user_id)

            for event_data in data.get("events", []):
                # --- 时间表达式转换 ---
                if event_data.get("description_text"):
                    event_data["description_text"] = self._replace_relative_time(event_data["description_text"])
                # --- 结束 ---执行新增事件
                processed_events.append(self._process_event(event_data, user_id, conversation_id))

            validations = [p.get("verification_message") for p in processed_persons if p.get("verification_message")]
            return {
                "action_code": "PROCESS_COMPLETE",
                "payload": {"persons": processed_persons, "events": processed_events, "validations": validations},
            }
        except Exception as e:
            logger.error(f"提取处理记忆失败: {str(e)}")
            return {"action_code": "ERROR", "payload": {"reason": str(e)}}

    def _handle_auto_merge_intent(self, persons_data: list, user_id: str):
        """处理自动合并意图，如果成功则返回结果，如果失败则返回None以进行后备处理"""
        try:
            name_a, name_b = persons_data[0].get("canonical_name"), persons_data[1].get("canonical_name")
            res_a = search_persons_by_name_mysql(user_id=user_id, name=name_a, limit=1)
            res_b = search_persons_by_name_mysql(user_id=user_id, name=name_b, limit=1)

            if res_a.get("persons") and res_b.get("persons"):
                person_a, person_b = res_a["persons"][0], res_b["persons"][0]

                # 规则：保留更新时间更近的档案作为主档案
                primary, secondary = (
                    (person_a, person_b) if person_a["updated_at"] >= person_b["updated_at"] else (person_b, person_a)
                )

                merge_result = self.execute_merge_persons(user_id, primary["person_id"], secondary["person_id"])

                if merge_result.get("result") == "success":
                    message = f"自动合并档案成功：已将'{secondary['canonical_name']}'的信息合并入'{primary['canonical_name']}'。"
                    logger.info(message)
                    return {"action_code": "MERGE_EXECUTED", "payload": {"message": message}}
                else:
                    return {"action_code": "ERROR", "payload": {"reason": merge_result.get("reason", "合并失败")}}
            else:
                logger.warning(f"无法找到用于合并的档案({name_a}, {name_b})，将按普通记忆添加流程处理。")
                return None  # 表示无法合并，应执行后备流程

        except Exception as e:
            logger.error(f"处理自动合并意图失败: {e}")
            return None  # 表示无法合并，应执行后备流程

    def execute_merge_persons(self, user_id: str, primary_person_id: str, secondary_person_id: str):
        """执行档案合并，使用手动事务管理"""
        conn = None
        try:
            # 安全检查：不能自己合并自己
            if primary_person_id == secondary_person_id:
                logger.info("无需合并，两个ID指向同一个人。")
                return {"result": "success", "message": "无需合并，指向同一个人。"}

            # 手动管理连接和事务
            from my_mysql.entity.person_table import person_memory
            from my_mysql.sql_client import CLIENT
            from sqlalchemy import delete as sql_delete
            from sqlalchemy import select
            from sqlalchemy import update as sql_update

            conn = CLIENT.connect()
            if not conn:
                raise Exception("无法建立数据库连接")
            trans = conn.begin()  # 开始事务

            # 1. 获取两份档案 - 直接在事务中查询，并带上user_id
            select_stmt_a = select(person_memory).where(
                and_(person_memory.c.person_id == primary_person_id, person_memory.c.user_id == user_id)
            )
            select_stmt_b = select(person_memory).where(
                and_(person_memory.c.person_id == secondary_person_id, person_memory.c.user_id == user_id)
            )

            result_a = conn.execute(select_stmt_a)
            result_b = conn.execute(select_stmt_b)

            if not result_a or not result_b:
                raise Exception("数据库查询执行失败")

            person_a_res = result_a.fetchone()
            person_b_res = result_b.fetchone()

            if not person_a_res or not person_b_res:
                raise Exception("无法找到档案")

            person_a = dict(person_a_res._mapping)
            person_b = dict(person_b_res._mapping)

            # 2. 合并信息 - 安全处理所有JSON字段
            try:
                aliases_a = json.loads(person_a.get("aliases") or "[]")
                if not isinstance(aliases_a, list):
                    aliases_a = []
            except (json.JSONDecodeError, TypeError):
                aliases_a = []

            try:
                aliases_b = json.loads(person_b.get("aliases") or "[]")
                if not isinstance(aliases_b, list):
                    aliases_b = []
            except (json.JSONDecodeError, TypeError):
                aliases_b = []

            merged_aliases = list(set(aliases_a + [person_b["canonical_name"]] + aliases_b))

            try:
                attrs_a = json.loads(person_a.get("key_attributes") or "{}")
                if not isinstance(attrs_a, dict):
                    attrs_a = {}
            except (json.JSONDecodeError, TypeError):
                attrs_a = {}

            try:
                attrs_b = json.loads(person_b.get("key_attributes") or "{}")
                if not isinstance(attrs_b, dict):
                    attrs_b = {}
            except (json.JSONDecodeError, TypeError):
                attrs_b = {}

            merged_attributes = {**attrs_b, **attrs_a}

            # 处理datetime对象，确保JSON序列化成功
            def convert_datetime_to_string(obj):
                """递归转换字典中的datetime对象为字符串"""
                if isinstance(obj, dict):
                    return {k: convert_datetime_to_string(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_datetime_to_string(item) for item in obj]
                elif isinstance(obj, datetime):
                    return obj.isoformat()
                else:
                    return obj

            # 清理合并后的数据，确保可以JSON序列化
            merged_attributes = convert_datetime_to_string(merged_attributes)

            # 安全处理relationships字段
            try:
                rels_a = json.loads(person_a.get("relationships") or "[]")
                if not isinstance(rels_a, list):
                    rels_a = []
            except (json.JSONDecodeError, TypeError):
                rels_a = []

            try:
                rels_b = json.loads(person_b.get("relationships") or "[]")
                if not isinstance(rels_b, list):
                    rels_b = []
            except (json.JSONDecodeError, TypeError):
                rels_b = []

            # 合并关系，确保每个元素都是字典
            merged_rels = []
            all_rels = rels_a + rels_b
            seen_ids = set()

            for rel in all_rels:
                if isinstance(rel, dict):
                    rel_id = rel.get("person_id", str(rel))
                    if rel_id not in seen_ids:
                        merged_rels.append(rel)
                        seen_ids.add(rel_id)
                elif isinstance(rel, str) and rel not in seen_ids:
                    # 如果是字符串，转换为简单的关系对象
                    merged_rels.append({"person_id": rel, "relation": "unknown"})
                    seen_ids.add(rel)

            # 最终清理，确保所有数据都可以JSON序列化
            merged_rels = convert_datetime_to_string(merged_rels)

            # 3. LLM生成新小传
            new_summary = self._summarize_merged_profile(person_a, person_b)

            # 4. 更新主档案
            update_stmt = (
                sql_update(person_memory)
                .where(person_memory.c.person_id == primary_person_id)
                .values(
                    aliases=json.dumps(merged_aliases, ensure_ascii=False),
                    key_attributes=json.dumps(merged_attributes, ensure_ascii=False),
                    relationships=json.dumps(merged_rels, ensure_ascii=False),
                    profile_summary=new_summary,
                )
            )
            conn.execute(update_stmt)

            # 5. 更新事件记录
            update_participant_in_events(self.event_index, user_id, secondary_person_id, primary_person_id)

            # 6. 删除次要档案
            delete_stmt = sql_delete(person_memory).where(person_memory.c.person_id == secondary_person_id)
            conn.execute(delete_stmt)

            # 7. 提交事务
            trans.commit()
            logger.info(f"档案合并成功: {secondary_person_id} -> {primary_person_id}")
            return {"result": "success"}
        except Exception as e:
            if conn:
                try:
                    trans.rollback()
                except Exception:
                    pass
            logger.error(f"档案合并失败: {e}")
            return {"result": "error", "reason": str(e)}
        finally:
            if conn:
                conn.close()

    def _summarize_merged_profile(self, person_a: dict, person_b: dict):
        """调用LLM为合并后的档案生成新的小传"""

        # 处理datetime对象，确保JSON序列化成功
        def convert_datetime_to_string(obj):
            """递归转换字典中的datetime对象为字符串"""
            if isinstance(obj, dict):
                return {k: convert_datetime_to_string(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_datetime_to_string(item) for item in obj]
            elif isinstance(obj, datetime):
                return obj.isoformat()
            else:
                return obj

        # 清理输入数据，确保可以JSON序列化
        clean_person_a = convert_datetime_to_string(person_a)
        clean_person_b = convert_datetime_to_string(person_b)

        prompt = get_value(
            "humanrelation.profile_merge_summarization_prompt",
            "请整合以下两份关于同一个人的资料，生成一段简洁、全面的新个人简介。",
        )
        context = f"档案A: {json.dumps(clean_person_a, ensure_ascii=False)}\n\n档案B: {json.dumps(clean_person_b, ensure_ascii=False)}"

        query_input = {
            "model": get_value("humanrelation.memory_extraction_model", "gpt-4o-mini"),
            "messages": [{"role": "system", "content": prompt}, {"role": "user", "content": context}],
            "stream": False,
            "temperature": 0.5,
            "max_tokens": 800,
        }
        response = send_to_ai(query_input)
        response_data = json.loads(response.text)
        return response_data["choices"][0]["message"]["content"].strip()

    def _extract_memory_from_conversation(self, conversation_text: str, user_id: str):  # LLM结构化提取
        """从对话历史中提取实体和事件，形成结构化记忆"""
        try:
            prompt = get_value("humanrelation.memory_extraction_prompt", "")
            if not prompt:
                logger.error("未配置记忆提取提示词")
                return {"result": "error", "reason": "未配置提示词", "data": {"persons": [], "events": []}}

            query_input = {
                "model": get_value("humanrelation.memory_extraction_model", "gpt-4o-mini"),
                "messages": [{"role": "system", "content": prompt}, {"role": "user", "content": conversation_text}],
                "stream": False,
                "temperature": 0.0,
                "max_tokens": 1000,
            }

            response = send_to_ai(query_input)
            if not response:
                logger.error("AI服务调用失败，返回None")
                return {"result": "error", "reason": "AI服务调用失败", "data": {"persons": [], "events": []}}

            logger.info(f"AI服务原始响应: {response.text}")

            try:
                response_data = json.loads(response.text)
            except json.JSONDecodeError as e:
                logger.error(f"解析AI服务响应失败: {e}, 原始响应: {response.text}")
                return {"result": "error", "reason": "AI服务响应格式错误", "data": {"persons": [], "events": []}}

            if "choices" not in response_data or not response_data["choices"]:
                logger.error(f"AI服务响应缺少choices字段: {response_data}")
                return {"result": "error", "reason": "AI服务响应格式异常", "data": {"persons": [], "events": []}}

            content = response_data["choices"][0]["message"]["content"].strip()
            logger.info(f"LLM返回的内容: {content}")

            if not content:
                logger.warning("LLM返回空内容")
                return {"result": "success", "data": {"intent": "add_memory", "persons": [], "events": []}}

            # 清理Markdown代码块标记
            cleaned_content = content
            if content.startswith("```json"):
                cleaned_content = content[7:]  # 移除开头的```json
            elif content.startswith("```"):
                cleaned_content = content[3:]  # 移除开头的```

            if cleaned_content.endswith("```"):
                cleaned_content = cleaned_content[:-3]  # 移除结尾的```

            cleaned_content = cleaned_content.strip()
            logger.info(f"清理后的内容: {cleaned_content}")

            try:
                result = json.loads(cleaned_content)
                logger.info(f"成功解析的记忆信息: {result}")
                return {"result": "success", "data": result}
            except json.JSONDecodeError as e:
                logger.error(f"解析LLM返回的JSON失败: {e}, 清理后内容: {cleaned_content}")

                # 尝试修复常见的JSON格式错误
                fixed_content = cleaned_content

                # 修复尾随逗号问题
                import re

                # 修复数组或对象末尾的多余逗号
                fixed_content = re.sub(r",(\s*[}\]])", r"\1", fixed_content)

                try:
                    result = json.loads(fixed_content)
                    logger.info(f"修复JSON格式后成功解析: {result}")

                    return {"result": "success", "data": result}
                except json.JSONDecodeError as e2:
                    logger.error(f"修复后仍无法解析JSON: {e2}, 修复后内容: {fixed_content}")

                    # 尝试提取可能的JSON部分
                    json_match = re.search(r"\{.*\}", fixed_content, re.DOTALL)
                    if json_match:
                        try:
                            result = json.loads(json_match.group())
                            logger.info(f"从混合内容中提取到JSON: {result}")
                            return {"result": "success", "data": result}
                        except:
                            pass

            # 如果完全无法解析，返回默认结构
            logger.warning("无法解析LLM输出，返回默认结构")
            return {"result": "success", "data": {"intent": "add_memory", "persons": [], "events": []}}

        except Exception as e:
            logger.error(f"提取记忆信息失败: {str(e)}")
            return {"result": "error", "reason": str(e), "data": {"persons": [], "events": []}}

    def _calculate_name_similarity_with_ai(
        self, name1: str, name2: str, person1_attrs: Dict = None, person2_attrs: Dict = None
    ) -> float:
        """
        使用AI计算两个人物的相似度
        """
        # 从Lion配置中心获取prompt模板
        from configs.lion_config import get_value

        prompt_template = get_value(
            "humanrelation.ai.similarity.prompt",
            """你是一个人际关系专家。请判断以下两个人物是否可能是同一个人：

人物1: {name1}
人物2: {name2}

人物1属性: {person1_attrs}
人物2属性: {person2_attrs}

请考虑以下因素：
1. 姓名的语义相似性（如"我爸爸"和"USER的父亲"指向同一人）
2. 属性的一致性（性别、年龄、职业、城市等）
3. 关系的一致性（与USER的关系类型）
4. **重名风险**：即使姓名相同，也要综合考虑其他属性来判断是否为同一人

**重要提醒**：
- 如果两人姓名相同但属性有明显冲突（如不同性别、不同关系、不同职业等），应判断为不同人
- **重名风险极高**：如果两人姓名相同但只有很少的属性信息（如只有性别），应保守判断为不同人，避免错误合并
- 只有在姓名相同且有足够多的关键属性高度一致时（如性别+城市+职业+关系等），才判断为同一人
- **宁可保守，不可激进**：当信息不足时，优先避免错误合并

请返回JSON格式：
{{
    "similarity_score": 0.0-1.0之间的相似度分数,
    "is_same_person": true/false,
    "reason": "判断理由"
}}

相似度评分标准：
- 1.0: 完全相同（姓名相同且所有关键属性一致）
- 0.9-0.99: 高度相似，很可能是同一人（如不同称呼但属性一致）
- 0.7-0.89: 中度相似，可能是同一人（部分属性一致）
- 0.5-0.69: 低度相似，需要更多信息确认
- 0.0-0.49: 不相似，不太可能是同一人（包括重名但属性冲突的情况）""",
        )

        # 构建AI提示
        prompt = prompt_template.format(
            name1=name1,
            name2=name2,
            person1_attrs=person1_attrs if person1_attrs else "无",
            person2_attrs=person2_attrs if person2_attrs else "无",
        )

        try:
            from service.ai_client import send_to_ai

            # 构建请求数据
            request_data = {
                "model": "gpt-4.1",
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": 500,
                "temperature": 0.1,
            }

            response = send_to_ai(request_data)

            if response and response.status_code == 200:
                response_data = response.json()
                content = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")

                # 解析AI响应
                import json
                import re

                # 提取JSON部分
                json_match = re.search(r"\{.*\}", content, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                    similarity_score = float(result.get("similarity_score", 0.0))
                    reason = result.get("reason", "AI判断")

                    logger.info(f"[AI相似度] {name1} vs {name2}: {similarity_score:.2f} - {reason}")
                    return similarity_score
                else:
                    logger.warning(f"[AI相似度] 无法解析AI响应: {content}")
                    return self._fallback_name_similarity(name1, name2)
            else:
                logger.warning(f"[AI相似度] AI请求失败")
                return self._fallback_name_similarity(name1, name2)

        except Exception as e:
            logger.error(f"[AI相似度] AI判断失败: {e}")
            return self._fallback_name_similarity(name1, name2)

    def _fallback_name_similarity(self, name1: str, name2: str) -> float:
        """
        AI失败时的备用相似度计算
        """
        # 简单的规则作为备用
        if name1 == name2:
            return 1.0

        # 检查包含关系
        if name1 in name2 or name2 in name1:
            return 0.7

        # 检查常见的家庭关系词
        family_words = ["父亲", "母亲", "爸爸", "妈妈", "儿子", "女儿"]
        name1_family = any(word in name1 for word in family_words)
        name2_family = any(word in name2 for word in family_words)

        if name1_family and name2_family:
            # 如果都包含家庭关系词，检查是否是同类关系
            for word in family_words:
                if word in name1 and word in name2:
                    return 0.8

        return 0.0

    def _generate_search_keywords_with_ai(self, canonical_name: str, new_person_attrs: Dict = None) -> List[str]:
        """
        使用AI生成搜索关键词，用于查找可能相似的人物
        """
        # 从Lion配置中心获取prompt模板
        from configs.lion_config import get_value

        prompt_template = get_value(
            "humanrelation.ai.keywords.prompt",
            """你是一个人际关系专家。给定一个人物姓名，请生成可能的搜索关键词来查找相似的人物档案。

目标人物: {canonical_name}
人物属性: {person_attrs}

请根据姓名类型生成合适的关键词：

1. **关系称呼类**（如"我爸爸"、"USER的父亲"）：
   - 生成语义相同的不同表达方式
   - 如："我爸爸" → ["父亲", "USER的父亲", "爸爸", "老爸"]

2. **正式姓名类**（如"张三"、"李四"）：
   - 主要生成昵称和常见变体
   - 如："张三" → ["张三", "老张", "小张", "张哥"]

3. **昵称类**（如"老王"、"小李"）：
   - 适度生成相关变体，避免过度扩展
   - 如："老王" → ["老王", "王哥", "王叔"]

请返回JSON格式的关键词列表：
{{
    "keywords": ["关键词1", "关键词2", "关键词3", ...],
    "reason": "生成这些关键词的理由"
}}

要求：
- 关键词数量控制在3-6个之间
- 必须包含原始姓名本身
- 优先考虑最可能的直接变体
- 避免过度职位化（如"老王"不要生成"王总"、"王经理"）
- 保持语义相近性""",
        )

        # 使用字符串替换而不是format，避免JSON示例中的大括号冲突
        prompt = prompt_template.replace("{canonical_name}", canonical_name)
        prompt = prompt.replace("{person_attrs}", str(new_person_attrs) if new_person_attrs else "无")

        try:
            from service.ai_client import send_to_ai

            # 构建请求数据
            request_data = {
                "model": "gpt-4.1",
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": 300,
                "temperature": 0.1,
            }

            response = send_to_ai(request_data)

            if response and response.status_code == 200:
                response_data = response.json()
                content = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")

                # 解析AI响应
                import json
                import re

                # 提取JSON部分
                json_match = re.search(r"\{.*\}", content, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                    keywords = result.get("keywords", [canonical_name])
                    reason = result.get("reason", "AI生成")

                    logger.info(f"[AI关键词] {canonical_name} -> {keywords} - {reason}")
                    return keywords
                else:
                    logger.warning(f"[AI关键词] 无法解析AI响应: {content}")
                    return self._fallback_search_keywords(canonical_name)
            else:
                logger.warning(f"[AI关键词] AI请求失败")
                return self._fallback_search_keywords(canonical_name)

        except Exception as e:
            logger.error(f"[AI关键词] AI生成失败: {e}")
            return self._fallback_search_keywords(canonical_name)

    def _fallback_search_keywords(self, canonical_name: str) -> List[str]:
        """
        AI失败时的备用关键词生成
        """
        keywords = [canonical_name]

        # 简单的规则作为备用
        if "父亲" in canonical_name or "爸爸" in canonical_name:
            keywords.extend(["父亲", "爸爸", "USER父亲", "USER的父亲"])
        elif "母亲" in canonical_name or "妈妈" in canonical_name:
            keywords.extend(["母亲", "妈妈", "USER母亲", "USER的母亲"])
        elif "儿子" in canonical_name:
            keywords.extend(["儿子", "孩子", "USER儿子"])
        elif "女儿" in canonical_name:
            keywords.extend(["女儿", "孩子", "USER女儿"])

        return list(set(keywords))  # 去重

    def _merge_attributes_with_ai_conflict_resolution(
        self, existing_attrs: Dict, new_attrs: Dict, existing_name: str, new_name: str
    ) -> Dict:
        """
        使用AI智能合并属性，处理冲突信息
        """
        if not existing_attrs and not new_attrs:
            return {}
        if not existing_attrs:
            return new_attrs
        if not new_attrs:
            return existing_attrs

        # 检测潜在冲突
        conflicts = self._detect_attribute_conflicts(existing_attrs, new_attrs)

        if not conflicts:
            # 没有冲突，直接合并（新信息优先）
            return self._simple_merge_attributes(existing_attrs, new_attrs)

        # 有冲突，使用AI解决
        logger.info(f"[冲突检测] 发现 {len(conflicts)} 个属性冲突，使用AI解决")
        return self._resolve_conflicts_with_ai(existing_attrs, new_attrs, conflicts, existing_name, new_name)

    def _detect_attribute_conflicts(self, existing_attrs: Dict, new_attrs: Dict) -> List[Dict]:
        """
        检测属性冲突
        """
        conflicts = []

        # 检查基本信息中的关键字段
        critical_fields = {
            "基本信息": ["性别", "当前城市", "家乡", "生日"],
            "职业信息": ["公司", "职位"],
            "家庭情况": ["配偶姓名", "婚育状况"],
        }

        for category, fields in critical_fields.items():
            existing_category = existing_attrs.get(category, {})
            new_category = new_attrs.get(category, {})

            for field in fields:
                existing_value = existing_category.get(field, "")
                new_value = new_category.get(field, "")

                # 如果两个值都存在且不同，则为冲突
                if existing_value and new_value and existing_value != new_value:
                    conflicts.append(
                        {"category": category, "field": field, "existing_value": existing_value, "new_value": new_value}
                    )

        return conflicts

    def _simple_merge_attributes(self, existing_attrs: Dict, new_attrs: Dict) -> Dict:
        """
        简单合并属性（新信息优先）
        """
        merged = existing_attrs.copy()

        for key, value in new_attrs.items():
            if isinstance(value, dict):
                if key not in merged:
                    merged[key] = {}
                for sub_key, sub_value in value.items():
                    if sub_value:  # 只有非空值才更新
                        merged[key][sub_key] = sub_value
            elif value:  # 只有非空值才更新
                merged[key] = value

        return merged

    def _resolve_conflicts_with_ai(
        self, existing_attrs: Dict, new_attrs: Dict, conflicts: List[Dict], existing_name: str, new_name: str
    ) -> Dict:
        """
        使用AI解决属性冲突
        """
        # 从Lion配置中心获取prompt模板
        from configs.lion_config import get_value

        prompt_template = get_value(
            "humanrelation.ai.conflict.prompt",
            """你是一个人际关系专家。现在需要合并两个人物档案的属性信息，但发现了一些冲突。请帮助解决这些冲突。

人物1: {existing_name}
现有属性: {existing_attrs}

人物2: {new_name}
新属性: {new_attrs}

发现的冲突:
{conflicts}

请考虑以下原则：
1. 时效性：更新的信息通常更准确
2. 具体性：更具体的信息通常更可靠
3. 一致性：保持逻辑一致性
4. 合理性：选择更合理的信息

对于每个冲突，请选择保留哪个值，或者提供一个合并后的值。

请返回JSON格式的合并结果：
{{
    "merged_attributes": {{
        "基本信息": {{
            "性别": "合并后的值",
            "当前城市": "合并后的值",
            ...
        }},
        ...
    }},
    "resolution_reasons": [
        {{
            "field": "字段名",
            "chosen_value": "选择的值",
            "reason": "选择理由"
        }}
    ]
}}""",
        )

        prompt = prompt_template.format(
            existing_name=existing_name,
            existing_attrs=existing_attrs,
            new_name=new_name,
            new_attrs=new_attrs,
            conflicts=conflicts,
        )

        try:
            from service.ai_client import send_to_ai

            # 构建请求数据
            request_data = {
                "model": "gpt-4.1",
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": 1000,
                "temperature": 0.1,
            }

            response = send_to_ai(request_data)

            if response and response.status_code == 200:
                response_data = response.json()
                content = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")

                # 解析AI响应
                import json
                import re

                # 提取JSON部分
                json_match = re.search(r"\{.*\}", content, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                    merged_attributes = result.get("merged_attributes", {})
                    reasons = result.get("resolution_reasons", [])

                    logger.info(f"[AI冲突解决] 成功解决 {len(conflicts)} 个冲突")
                    for reason in reasons:
                        logger.info(f"[AI冲突解决] {reason['field']}: {reason['chosen_value']} - {reason['reason']}")

                    return merged_attributes
                else:
                    logger.warning(f"[AI冲突解决] 无法解析AI响应: {content}")
                    return self._fallback_conflict_resolution(existing_attrs, new_attrs, conflicts)
            else:
                logger.warning(f"[AI冲突解决] AI请求失败")
                return self._fallback_conflict_resolution(existing_attrs, new_attrs, conflicts)

        except Exception as e:
            logger.error(f"[AI冲突解决] AI解决失败: {e}")
            return self._fallback_conflict_resolution(existing_attrs, new_attrs, conflicts)

    def _fallback_conflict_resolution(self, existing_attrs: Dict, new_attrs: Dict, conflicts: List[Dict]) -> Dict:
        """
        AI失败时的备用冲突解决策略
        """
        logger.info(f"[备用冲突解决] 使用备用策略解决 {len(conflicts)} 个冲突")

        # 备用策略：新信息优先，但记录冲突
        merged = self._simple_merge_attributes(existing_attrs, new_attrs)

        for conflict in conflicts:
            logger.warning(
                f"[备用冲突解决] {conflict['category']}.{conflict['field']}: "
                f"'{conflict['existing_value']}' -> '{conflict['new_value']}' (新信息优先)"
            )

        return merged

    def _find_similar_persons(self, user_id: str, canonical_name: str, new_person_attrs: Dict = None) -> List[Dict]:
        """
        查找语义相似的人物档案，用于去重合并
        结合姓名相似性和属性相似性进行智能匹配
        """
        # 1. 使用AI生成搜索关键词
        search_keywords = self._generate_search_keywords_with_ai(canonical_name, new_person_attrs)

        # 2. 基于AI生成的关键词搜索候选人物
        candidates = []
        for keyword in search_keywords:
            # 使用AI生成的关键词进行精确和模糊搜索
            result = search_persons_by_name_mysql(user_id, keyword, 20)
            persons = result.get("persons", [])
            for person in persons:
                # 避免重复添加
                if not any(p["person_id"] == person["person_id"] for p in candidates):
                    candidates.append(person)

        logger.info(f"[AI搜索] 使用关键词 {search_keywords} 找到 {len(candidates)} 个候选人物")

        # 2. 计算相似度并筛选
        similar_persons = []
        for candidate in candidates:
            # 使用AI计算综合相似度（包含姓名和属性）
            candidate_attrs = candidate.get("key_attributes", {})
            similarity_score = self._calculate_name_similarity_with_ai(
                canonical_name, candidate["canonical_name"], new_person_attrs, candidate_attrs
            )

            # 从Lion配置中心获取相似度阈值
            from configs.lion_config import get_value as lion_get_value

            similarity_threshold = float(lion_get_value("humanrelation.ai.similarity.threshold", "0.8"))

            # 设置相似度阈值（提高阈值以避免重名误合并）
            if similarity_score >= similarity_threshold:
                candidate["similarity_score"] = similarity_score
                similar_persons.append(candidate)
                logger.info(f"[AI相似度] {canonical_name} vs {candidate['canonical_name']}: {similarity_score:.2f}")

        # 按相似度排序
        similar_persons.sort(key=lambda x: x.get("similarity_score", 0), reverse=True)

        return similar_persons

    def _merge_similar_persons(self, user_id: str, canonical_name: str, new_person_data: Dict) -> Optional[str]:
        """
        合并相似的人物档案，返回合并后的person_id，如果没有合并则返回None
        """
        # 传递新人物的属性信息用于相似度计算
        new_person_attrs = new_person_data.get("key_attributes", {})
        similar_persons = self._find_similar_persons(user_id, canonical_name, new_person_attrs)

        if not similar_persons:
            return None

        # 选择最合适的目标档案进行合并（优先选择最新的）
        target_person = max(similar_persons, key=lambda p: p.get("updated_at", ""))
        target_id = target_person["person_id"]

        logger.info(f"[人物合并] 发现相似人物，准备合并到: {target_person['canonical_name']} (ID: {target_id})")

        # 使用AI智能合并属性和关系
        merged_attributes = self._merge_attributes_with_ai_conflict_resolution(
            target_person.get("key_attributes", {}),
            new_person_data.get("key_attributes", {}),
            target_person["canonical_name"],
            new_person_data["canonical_name"],
        )
        merged_relationships = target_person.get("relationships", [])

        # 合并关系（去重）
        new_relationships = new_person_data.get("relationships", [])
        for new_rel in new_relationships:
            if not any(
                r.get("target") == new_rel.get("target") and r.get("type") == new_rel.get("type")
                for r in merged_relationships
            ):
                merged_relationships.append(new_rel)

        # 更新档案
        update_data = {
            "key_attributes": merged_attributes,
            "relationships": merged_relationships,
            "profile_summary": new_person_data.get("profile_summary", target_person.get("profile_summary", "")),
        }

        update_person_mysql(user_id, target_id, **update_data)
        logger.info(f"[人物合并] 成功合并人物信息到: {target_person['canonical_name']} (ID: {target_id})")

        # 删除其他重复档案
        for person in similar_persons:
            if person["person_id"] != target_id:
                logger.info(f"[人物合并] 删除重复档案: {person['canonical_name']} (ID: {person['person_id']})")
                try:
                    # 实际删除重复档案
                    from service.mysql_person_service import delete_person_mysql

                    delete_person_mysql(user_id, person["person_id"])
                    logger.info(f"[人物合并] 成功删除重复档案: {person['canonical_name']} (ID: {person['person_id']})")
                except Exception as e:
                    logger.error(
                        f"[人物合并] 删除重复档案失败: {person['canonical_name']} (ID: {person['person_id']}) - {e}"
                    )

        return target_id

    def _process_person_with_temporal_info(
        self, person_data: dict, context: str, user_id: str
    ):  # 处理人物信息并进行时效性计算
        """处理单个实体，自动创建或更新"""
        canonical_name = person_data.get("canonical_name", "")
        if not canonical_name:
            return {"result": "error", "reason": "人物姓名为空"}

        # 特殊处理：如果是USER，直接处理用户本人档案
        if canonical_name == "USER":
            user_person = get_user_person(user_id)
            if not user_person:
                # 用户档案不存在，创建一个
                create_result = ensure_user_profile_exists(user_id)
                if create_result.get("result") == "success":
                    user_person = get_user_person(user_id)
                else:
                    return {"result": "error", "reason": "无法创建用户本人档案"}

            # 更新用户本人档案信息
            person_id = user_person["person_id"]
            processed_attributes = self._process_temporal_attributes(person_data.get("key_attributes", {}))
            verification_message = processed_attributes.pop("verification_message", "")

            # 合并属性（用户本人档案也可能需要更新信息）
            existing_attrs = user_person.get("key_attributes")
            if isinstance(existing_attrs, str):
                # 如果是字符串，尝试解析JSON
                try:
                    existing_attrs = json.loads(existing_attrs)
                except:
                    existing_attrs = {}
            elif isinstance(existing_attrs, dict):
                # 如果已经是字典，直接使用
                pass
            else:
                # 其他情况，初始化为空字典
                existing_attrs = {}

            # 使用标准化处理器确保用户信息结构一致
            logger.info(f"[标准化处理] 用户档案合并前 - 现有属性: {existing_attrs}")
            logger.info(f"[标准化处理] 用户档案合并前 - 新属性: {processed_attributes}")
            merged_attrs = user_profile_standardizer.merge_standard_attributes(existing_attrs, processed_attributes)
            logger.info(f"[标准化处理] 用户档案合并后 - 标准化属性: {merged_attrs}")

            # 更新用户档案
            update_payload = {}

            # 智能合并profile_summary
            if person_data.get("profile_summary"):
                new_summary = person_data.get("profile_summary")
                existing_summary = user_person.get("profile_summary", "")

                # 如果现有summary是默认的系统创建文案，直接替换
                if existing_summary == "这是您的个人档案，系统会在这里记录与您相关的信息。":
                    update_payload["profile_summary"] = new_summary
                else:
                    # 否则智能合并，避免重复信息
                    if existing_summary and new_summary not in existing_summary:
                        # 简单的合并策略：如果新信息不在旧信息中，则合并
                        merged_summary = f"{existing_summary.rstrip('。')}，{new_summary}"
                        update_payload["profile_summary"] = merged_summary
                    elif not existing_summary:
                        update_payload["profile_summary"] = new_summary
                    # 如果新信息已经包含在旧信息中，不更新

            if merged_attrs != existing_attrs:
                update_payload["key_attributes"] = json.dumps(merged_attrs, ensure_ascii=False)

            if update_payload:
                update_person_mysql(user_id=user_id, person_id=person_id, **update_payload)

            return {
                "result": "success",
                "data": {"action": "updated", "person_id": person_id},
                "verification_message": verification_message,
            }

        # 首先尝试合并相似人物
        merged_person_id = self._merge_similar_persons(user_id, canonical_name, person_data)
        if merged_person_id:
            logger.info(f"[人物合并] 人物已合并到现有档案: {canonical_name} (ID: {merged_person_id})")
            return {
                "result": "success",
                "action": "merged",
                "person_id": merged_person_id,
                "data": {"canonical_name": canonical_name},
            }

        search_result = search_persons_by_name_mysql(user_id=user_id, name=canonical_name, limit=1)  # 精确查找一个

        # --- 实体消歧 START ---
        if search_result.get("persons"):
            try:
                existing = search_result["persons"][0]
                # 安全获取 key_attributes
                existing_attrs = existing.get("key_attributes")
                if isinstance(existing_attrs, str):
                    existing_attrs = json.loads(existing_attrs or "{}")
                elif not isinstance(existing_attrs, dict):
                    existing_attrs = {}

                new_attrs = person_data.get("key_attributes", {})

                # 核心规则：如果"关系"属性存在且明确冲突，则判定为新人，强制创建新档案
                if (
                    "关系" in existing_attrs
                    and "关系" in new_attrs
                    and existing_attrs["关系"]
                    and new_attrs["关系"]
                    and existing_attrs["关系"] != new_attrs["关系"]
                ):
                    logger.info(
                        f"实体消歧：检测到'{canonical_name}'的'关系'属性冲突，强制创建新档案。已有关系: '{existing_attrs['关系']}', 新关系: '{new_attrs['关系']}'"
                    )
                    search_result = {"persons": []}  # 清空查找结果，以触发后续的"创建"逻辑
            except Exception as e:
                logger.error(f"实体消歧逻辑执行失败: {e}")
        # --- 实体消歧 END ---

        # 将profile_summary也传递给时效性处理，以便从中提取年龄信息
        attributes_with_summary = person_data.get("key_attributes", {}).copy()
        if person_data.get("profile_summary"):
            attributes_with_summary["profile_summary"] = person_data["profile_summary"]

        # 时效性处理
        logger.info(f"[数据生成] 开始时效性处理 - 人物: {canonical_name}")
        processed_attributes = self._process_temporal_attributes(attributes_with_summary)
        verification_message = processed_attributes.pop("verification_message", "")
        logger.info(
            f"[数据生成] 时效性处理完成 - 生日字段: {processed_attributes.get('基本信息', {}).get('生日', '无')}"
        )

        if search_result.get("persons"):  # 更新流程，合并而非覆盖
            existing = search_result["persons"][0]
            person_id = existing["person_id"]

            # 1. 合并别名
            try:
                existing_aliases = json.loads(existing.get("aliases") or "[]")
                if not isinstance(existing_aliases, list):
                    existing_aliases = []
            except:
                existing_aliases = []
            new_aliases = person_data.get("aliases", [])
            merged_aliases = list(dict.fromkeys(existing_aliases + new_aliases))

            # 2. 合并关键属性
            existing_attrs = existing.get("key_attributes")
            if isinstance(existing_attrs, str):
                # 如果是字符串，尝试解析JSON
                try:
                    existing_attrs = json.loads(existing_attrs)
                except:
                    existing_attrs = {}
            elif isinstance(existing_attrs, dict):
                # 如果已经是字典，直接使用
                pass
            else:
                # 其他情况，初始化为空字典
                existing_attrs = {}

            # 使用标准化处理器确保人物信息结构一致
            logger.info(f"[标准化处理] 人物档案合并前 - 现有属性: {existing_attrs}")
            logger.info(f"[标准化处理] 人物档案合并前 - 新属性: {processed_attributes}")
            merged_attrs = user_profile_standardizer.merge_standard_attributes(existing_attrs, processed_attributes)
            logger.info(f"[标准化处理] 人物档案合并后 - 标准化属性: {merged_attrs}")

            # 3. 合并关系信息
            try:
                existing_relationships = existing.get("relationships")
                if isinstance(existing_relationships, str):
                    existing_relationships = json.loads(existing_relationships or "[]")
                elif not isinstance(existing_relationships, list):
                    existing_relationships = []
            except:
                existing_relationships = []

            new_relationships = person_data.get("relationships", [])
            # 合并关系，避免重复和冲突
            merged_relationships = existing_relationships.copy()
            for new_rel in new_relationships:
                # 检查是否已存在相同的关系
                exists = any(
                    rel.get("target") == new_rel.get("target") and rel.get("type") == new_rel.get("type")
                    for rel in merged_relationships
                )
                if not exists:
                    # 检查是否存在冲突的关系
                    conflicting_rel = self._find_conflicting_relationship(merged_relationships, new_rel)
                    if conflicting_rel:
                        logger.warning(f"检测到关系冲突: 新关系 {new_rel} 与现有关系 {conflicting_rel} 冲突")
                        # 移除冲突的旧关系，添加新关系（新信息优先）
                        merged_relationships = [rel for rel in merged_relationships if rel != conflicting_rel]
                        merged_relationships.append(new_rel)
                        logger.info(f"已用新关系 {new_rel} 替换冲突的旧关系 {conflicting_rel}")
                    else:
                        merged_relationships.append(new_rel)

            # 4. 构建更新载荷
            update_payload = {}

            # 4a. 别名
            if merged_aliases != existing_aliases:
                update_payload["aliases"] = merged_aliases  # 传递列表，让update_person函数处理JSON编码

            # 4b. 关键属性
            if merged_attrs != existing_attrs:
                update_payload["key_attributes"] = merged_attrs  # 传递字典，让update_person函数处理JSON编码

            # 4c. 关系信息
            if merged_relationships != existing_relationships:
                update_payload["relationships"] = merged_relationships

            # 4d. profile_summary 处理逻辑：追加而非覆盖
            new_summary = person_data.get("profile_summary", "").strip()
            existing_summary = existing.get("profile_summary", "") or ""

            updated_summary = existing_summary
            if new_summary and new_summary not in existing_summary:
                updated_summary = f"{existing_summary}；{new_summary}" if existing_summary else new_summary

            if updated_summary != existing_summary:
                update_payload["profile_summary"] = updated_summary

            if update_payload:  # 只有有变动才更新
                update_person_mysql(user_id=user_id, person_id=person_id, **update_payload)
            action_result = {"action": "updated", "person_id": person_id}
        else:  # 创建新档案
            # 使用标准化处理器确保新档案的属性结构一致
            logger.info(f"[数据生成] 开始数据标准化 - 人物: {canonical_name}")
            standardized_attrs = user_profile_standardizer.ensure_standard_structure(processed_attributes)
            logger.info(f"[数据生成] 数据标准化完成")

            update_payload = {
                "aliases": person_data.get("aliases", []),  # 传递列表，让add_person函数处理JSON编码
                "profile_summary": person_data.get("profile_summary", ""),
                "key_attributes": standardized_attrs,  # 传递字典，让add_person函数处理JSON编码
                "relationships": person_data.get("relationships", []),
            }

            logger.info(f"[数据生成] 准备传递给add_person的参数:")
            logger.info(f"[数据生成] - aliases: {update_payload['aliases']} (type: {type(update_payload['aliases'])})")
            logger.info(
                f"[数据生成] - relationships: {update_payload['relationships']} (type: {type(update_payload['relationships'])})"
            )
            logger.info(f"[数据生成] - key_attributes type: {type(update_payload['key_attributes'])}")
            create_result = add_person(user_id=user_id, canonical_name=canonical_name, **update_payload)
            action_result = {"action": "created", "person_id": create_result.get("person_id")}

            # 新增逻辑：检查新建档案的名称是否像昵称，如果是，则生成追问全名的消息
            if re.match(r"^(老|小|阿).*", canonical_name) or len(canonical_name) <= 2:
                ask_for_name_msg = (
                    f"已为您创建了'{canonical_name}'的档案。如果方便的话，可以告诉我TA的正式姓名吗？这样我能记得更牢。"
                )
                if verification_message:
                    verification_message += f"；{ask_for_name_msg}"
                else:
                    verification_message = ask_for_name_msg

        return {"result": "success", "data": action_result, "verification_message": verification_message}

    def _process_temporal_attributes(self, attributes: dict):  # 时效性信息处理
        """处理可计算的时效性信息，如年龄转换为出生年份，并确保使用标准化结构"""
        logger.info(f"[时效性处理] 输入属性: {attributes}")

        # 首先确保输入属性符合标准结构
        standardized_input = user_profile_standardizer.ensure_standard_structure(attributes)
        logger.info(f"[时效性处理] 标准化后输入: {standardized_input}")

        processed = json.loads(json.dumps(standardized_input))  # 深拷贝
        verification_messages = []
        current_year = datetime.now().year

        # 处理年龄信息 - 从标准结构中提取
        basic_info = processed.get("基本信息", {})

        # 如果有年龄信息，转换为生日
        age_value = None

        # 方法1: 从attributes中直接提取年龄
        if "年龄" in attributes and attributes["年龄"]:
            try:
                age_value = int(attributes["年龄"])
            except:
                pass

        # 方法2: 如果没有直接的年龄字段，尝试从其他字段中提取年龄信息
        if not age_value:
            import re

            # 检查所有字段中是否包含年龄信息
            for key, value in attributes.items():
                if isinstance(value, str) and value:
                    # 匹配"XX岁"格式
                    age_match = re.search(r"(\d+)岁", value)
                    if age_match:
                        try:
                            age_value = int(age_match.group(1))
                            logger.info(f"从{key}字段中提取到年龄信息: {age_value}岁")
                            break
                        except:
                            pass

        # 转换年龄为出生年份
        if age_value:
            try:
                birth_year = current_year - age_value
                basic_info["生日"] = f"{birth_year}年"
                verification_messages.append(f"根据年龄{age_value}岁计算，出生年份应该是{birth_year}年左右，对吗？")
                logger.info(f"[时效性处理] 年龄{age_value}岁转换为出生年份{birth_year}年")
            except Exception as e:
                logger.error(f"[时效性处理] 年龄转换失败: {e}")
                pass

        # 处理孩子年龄信息 - 存储到家庭情况中
        if "孩子年龄" in attributes and attributes["孩子年龄"]:
            try:
                child_age = int(attributes["孩子年龄"])
                child_birth_year = current_year - child_age
                family_info = basic_info.get("家庭情况", {})
                if isinstance(family_info.get("子女信息"), list):
                    family_info["子女信息"].append(f"{child_birth_year}年出生")
                else:
                    family_info["子女信息"] = [f"{child_birth_year}年出生"]
                verification_messages.append(
                    f"孩子{child_age}岁，出生年份应该是{child_birth_year}年左右，对吗？如果能告诉我月份，以后提醒生日就更准了！"
                )
            except:
                pass

        # 添加时间戳用于状态信息的定期回顾 - 存储到职业信息中
        job_info = basic_info.get("职业信息", {})
        for key in ["职位", "公司"]:
            if key in attributes:
                job_info[f"{key}_更新时间"] = datetime.now().isoformat()

        if verification_messages:
            processed["verification_message"] = "; ".join(verification_messages)

        logger.info(f"[时效性处理] 处理后属性: {processed}")
        return processed

    def _process_bidirectional_relationships(self, persons_data: list, user_id: str):
        """处理双向关系：确保父女、夫妻等关系在双方档案中都有体现"""
        try:
            # 遍历所有人物，处理他们的关系
            for person_data in persons_data:
                canonical_name = person_data.get("canonical_name", "")
                relationships = person_data.get("relationships", [])

                for relationship in relationships:
                    target_name = relationship.get("target")
                    relation_type = relationship.get("type")

                    # 跳过与USER的关系，因为USER档案有特殊处理
                    if target_name == "USER":
                        continue

                    # 处理需要双向关系的类型
                    reverse_relation = self._get_reverse_relation(relation_type)
                    if reverse_relation:
                        self._add_reverse_relationship_and_attributes(
                            target_name, canonical_name, reverse_relation, user_id
                        )

        except Exception as e:
            logger.error(f"处理双向关系失败: {e}")

    def _get_reverse_relation(self, relation_type: str) -> str:
        """获取反向关系类型"""
        relation_map = {
            "父亲": "女儿",  # 如果A是B的父亲，那么B是A的女儿
            "母亲": "女儿",  # 如果A是B的母亲，那么B是A的女儿
            "儿子": "父亲",  # 如果A是B的儿子，那么B是A的父亲
            "女儿": "父亲",  # 如果A是B的女儿，那么B是A的父亲
            "丈夫": "妻子",  # 如果A是B的丈夫，那么B是A的妻子
            "妻子": "丈夫",  # 如果A是B的妻子，那么B是A的丈夫
            "配偶": "配偶",  # 配偶关系是对称的
        }
        return relation_map.get(relation_type, "")

    def _add_reverse_relationship_and_attributes(
        self, target_name: str, source_name: str, reverse_relation: str, user_id: str
    ):
        """为目标人物添加反向关系，并更新相关属性字段"""
        try:
            # 查找目标人物
            search_result = search_persons_by_name_mysql(user_id=user_id, name=target_name, limit=1)
            if not search_result.get("persons"):
                logger.info(f"未找到目标人物 {target_name}，跳过反向关系处理")
                return

            target_person = search_result["persons"][0]
            person_id = target_person["person_id"]

            # 1. 添加反向关系到relationships字段
            try:
                existing_relationships = target_person.get("relationships")
                if isinstance(existing_relationships, str):
                    existing_relationships = json.loads(existing_relationships or "[]")
                elif not isinstance(existing_relationships, list):
                    existing_relationships = []
            except:
                existing_relationships = []

            # 检查关系是否已存在
            new_relationship = {"target": source_name, "type": reverse_relation}
            relationship_exists = any(
                rel.get("target") == source_name and rel.get("type") == reverse_relation
                for rel in existing_relationships
            )

            if not relationship_exists:
                # 检查是否存在冲突的关系
                conflicting_rel = self._find_conflicting_relationship(existing_relationships, new_relationship)
                if conflicting_rel:
                    logger.warning(
                        f"双向关系处理中检测到关系冲突: 新关系 {new_relationship} 与现有关系 {conflicting_rel} 冲突"
                    )
                    # 移除冲突的旧关系，添加新关系（新信息优先）
                    existing_relationships = [rel for rel in existing_relationships if rel != conflicting_rel]
                    existing_relationships.append(new_relationship)
                    logger.info(f"已用新关系 {new_relationship} 替换冲突的旧关系 {conflicting_rel}")
                else:
                    existing_relationships.append(new_relationship)

                # 2. 更新key_attributes中的相关字段
                try:
                    key_attributes = target_person.get("key_attributes")
                    if isinstance(key_attributes, str):
                        key_attributes = json.loads(key_attributes or "{}")
                    elif not isinstance(key_attributes, dict):
                        key_attributes = {}
                except:
                    key_attributes = {}

                # 确保基本信息结构存在
                if "基本信息" not in key_attributes:
                    key_attributes["基本信息"] = {}
                if "家庭情况" not in key_attributes["基本信息"]:
                    key_attributes["基本信息"]["家庭情况"] = {}

                family_info = key_attributes["基本信息"]["家庭情况"]

                # 根据关系类型更新相应字段
                if reverse_relation in ["儿子", "女儿"]:
                    # 如果是子女关系，更新父亲的子女信息
                    if "子女信息" not in family_info:
                        family_info["子女信息"] = []
                    if isinstance(family_info["子女信息"], list) and source_name not in family_info["子女信息"]:
                        family_info["子女信息"].append(source_name)

                elif reverse_relation in ["父亲", "母亲"]:
                    # 如果是父母关系，可以考虑更新其他相关信息
                    pass

                elif reverse_relation in ["丈夫", "妻子", "配偶"]:
                    # 如果是配偶关系，更新配偶姓名
                    family_info["配偶姓名"] = source_name

                # 更新数据库
                update_result = update_person_mysql(
                    user_id=user_id,
                    person_id=person_id,
                    relationships=existing_relationships,
                    key_attributes=key_attributes,
                )

                if update_result.get("result") == "success":
                    logger.info(f"成功为 {target_name} 添加反向关系: {source_name} ({reverse_relation})")
                else:
                    logger.error(f"为 {target_name} 添加反向关系失败: {update_result}")

        except Exception as e:
            logger.error(f"添加反向关系失败 {target_name} -> {source_name}: {e}")

    def _find_conflicting_relationship(self, existing_relationships: list, new_relationship: dict) -> dict:
        """
        检查新关系是否与现有关系冲突
        使用混合方案：先用规则引擎快速检测，复杂情况使用AI判断

        Args:
            existing_relationships: 现有关系列表
            new_relationship: 新关系

        Returns:
            如果存在冲突关系，返回冲突的关系；否则返回None
        """
        new_target = new_relationship.get("target")
        new_type = new_relationship.get("type")

        if not new_target or not new_type:
            return None

        # 第一步：使用规则引擎进行快速检测
        rule_based_conflict = self._check_conflict_by_rules(existing_relationships, new_relationship)
        if rule_based_conflict:
            logger.info(f"规则引擎检测到关系冲突: {new_relationship} vs {rule_based_conflict}")
            return rule_based_conflict

        # 第二步：对于规则引擎无法处理的复杂情况，使用AI判断（可配置开关）
        enable_ai_conflict_detection = get_value("humanrelation.enable_ai_conflict_detection", False)
        if enable_ai_conflict_detection:
            ai_based_conflict = self._check_conflict_by_ai(existing_relationships, new_relationship)
            if ai_based_conflict:
                logger.info(f"AI检测到关系冲突: {new_relationship} vs {ai_based_conflict}")
                return ai_based_conflict

        return None

    def _check_conflict_by_rules(self, existing_relationships: list, new_relationship: dict) -> dict:
        """使用预定义规则检测关系冲突（快速检测）"""
        new_target = new_relationship.get("target")
        new_type = new_relationship.get("type")

        # 定义明确的冲突关系映射
        conflicting_relations = {
            "上司": ["下属", "员工", "部下"],
            "下属": ["上司", "老板", "领导", "主管"],
            "员工": ["上司", "老板", "领导", "主管"],
            "部下": ["上司", "老板", "领导", "主管"],
            "老板": ["下属", "员工", "部下"],
            "领导": ["下属", "员工", "部下"],
            "主管": ["下属", "员工", "部下"],
            "父亲": ["儿子", "女儿"],
            "母亲": ["儿子", "女儿"],
            "儿子": ["父亲", "母亲"],
            "女儿": ["父亲", "母亲"],
            "丈夫": ["妻子"],
            "妻子": ["丈夫"],
            "老师": ["学生"],
            "学生": ["老师"],
        }

        # 检查是否存在冲突关系
        conflicting_types = conflicting_relations.get(new_type, [])

        for existing_rel in existing_relationships:
            if existing_rel.get("target") == new_target and existing_rel.get("type") in conflicting_types:
                return existing_rel

        return None

    def _check_conflict_by_ai(self, existing_relationships: list, new_relationship: dict) -> dict:
        """使用AI检测复杂的关系冲突"""
        try:
            # 只对规则引擎无法处理的复杂情况使用AI
            new_target = new_relationship.get("target")
            new_type = new_relationship.get("type")

            # 找出与同一目标的所有现有关系
            same_target_relationships = [rel for rel in existing_relationships if rel.get("target") == new_target]

            if not same_target_relationships:
                return None

            # 构建AI判断提示词
            prompt = f"""你是一个人际关系专家。请判断以下关系是否存在逻辑冲突：

新关系：{new_type} -> {new_target}
现有关系：{[f"{rel.get('type')} -> {rel.get('target')}" for rel in same_target_relationships]}

请判断新关系是否与任何现有关系存在逻辑冲突。

判断标准：
1. 同一个人不能既是上司又是下属
2. 同一个人不能既是父亲又是儿子
3. 同一个人不能既是老师又是学生
4. 其他明显的逻辑矛盾

请返回JSON格式：
{{
    "has_conflict": true/false,
    "conflicting_relationship": {{"type": "关系类型", "target": "目标"}},
    "reason": "冲突原因说明"
}}

如果没有冲突，返回：
{{
    "has_conflict": false,
    "conflicting_relationship": null,
    "reason": "无冲突"
}}"""

            # 调用AI进行判断
            ai_input = {
                "model": get_value("humanrelation.memory_extraction_model", "gpt-4o-mini"),
                "messages": [{"role": "user", "content": prompt}],
                "stream": False,
                "temperature": 0.0,
            }

            response = send_to_ai(ai_input)
            if response and response.status_code == 200:
                response_data = response.json()
                # 处理OpenAI格式的响应
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    ai_content = response_data["choices"][0]["message"]["content"]
                    ai_result = self._parse_ai_response(ai_content)
                    if ai_result and ai_result.get("has_conflict"):
                        conflicting_rel = ai_result.get("conflicting_relationship")
                        if conflicting_rel:
                            # 找到对应的现有关系对象
                            for existing_rel in same_target_relationships:
                                if existing_rel.get("type") == conflicting_rel.get("type") and existing_rel.get(
                                    "target"
                                ) == conflicting_rel.get("target"):
                                    logger.info(f"AI检测原因: {ai_result.get('reason', '未知')}")
                                    return existing_rel

        except Exception as e:
            logger.warning(f"AI关系冲突检测失败，回退到规则引擎: {e}")

        return None

    def _parse_ai_response(self, response_text: str) -> dict:
        """解析AI返回的JSON响应"""
        try:
            # 尝试直接解析JSON
            if response_text.strip().startswith("{"):
                return json.loads(response_text)

            # 如果不是纯JSON，尝试提取JSON部分
            json_match = re.search(r"\{.*\}", response_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())

        except Exception as e:
            logger.warning(f"解析AI响应失败: {e}, 原始响应: {response_text}")

        return None

    def _process_event(self, event_data: dict, user_id: str, conversation_id: str = None):  # 处理事件信息（短期记忆）
        """处理单个事件信息并存入ES"""
        logger.info(f"_process_event 接收到的参数: user_id={user_id}, conversation_id={conversation_id}")
        logger.info(f"事件数据: {event_data}")

        # 提取参与者信息
        participant_names = event_data.get("participants", [])

        # 注释掉旧的用户档案更新逻辑，因为我们现在有更好的USER处理机制
        # 特殊处理：如果事件只涉及USER且包含个人属性信息，同时更新用户档案
        # if (len(participant_names) == 1 and participant_names[0] in ["我", "USER"] and
        #     event_data.get("topics") and any(topic in ["饮食偏好", "爱好", "特长", "性格", "习惯"] for topic in event_data.get("topics", []))):
        #     logger.info(f"[事件处理] 检测到用户个人属性事件，同时更新用户档案: {event_data}")
        #     self._update_user_profile_from_event(event_data, user_id)

        # 将参与者名称转换为 person_id
        participant_ids = []
        if participant_names:
            for participant_name in participant_names:
                if participant_name in ["我", "USER"]:
                    # 特殊处理：将"我"或"USER"转换为用户本人的person_id
                    from service.mysql_person_service import ensure_user_profile_exists, get_user_person

                    user_person = get_user_person(user_id)
                    if user_person:
                        participant_ids.append(user_person["person_id"])
                    else:
                        # 如果用户本人档案不存在，先创建再使用
                        logger.info(f"用户本人档案不存在，自动创建用户档案: {user_id}")
                        create_result = ensure_user_profile_exists(user_id)
                        if create_result.get("result") == "success":
                            participant_ids.append(create_result["person_id"])
                        else:
                            # 创建失败，保留原始名称
                            participant_ids.append(participant_name)
                else:
                    # 搜索人物档案获取 person_id
                    search_result = search_persons_by_name_mysql(user_id=user_id, name=participant_name, limit=1)
                    if search_result.get("result") == "success" and search_result.get("persons"):
                        person = search_result["persons"][0]
                        person_id = person["person_id"]
                        participant_ids.append(person_id)
                    else:
                        # 如果找不到对应的人物档案，保留原始名称
                        participant_ids.append(participant_name)

        result = add_event(
            index_name=self.event_index,
            user_id=user_id,
            description_text=event_data.get("description_text", ""),
            participants=participant_ids,  # 使用转换后的 person_id 列表
            location=event_data.get("location", ""),
            topics=event_data.get("topics", []),
            sentiment=event_data.get("sentiment", ""),
            conversation_id=conversation_id,
        )

        if result.get("result") == "success":
            return {"status": "created", "event_id": result.get("event_id"), "data": event_data}
        else:
            return {"status": "error", "reason": result.get("reason"), "data": event_data}

    def _update_user_profile_from_event(self, event_data: dict, user_id: str):
        """从事件信息中提取用户个人属性并更新用户档案"""
        try:
            from service.mysql_person_service import ensure_user_profile_exists, get_user_person, update_person_mysql

            # 确保用户档案存在
            user_person = get_user_person(user_id)
            if not user_person:
                create_result = ensure_user_profile_exists(user_id)
                if create_result.get("result") == "success":
                    user_person = get_user_person(user_id)
                else:
                    logger.error(f"无法创建用户档案: {create_result}")
                    return

            # 从事件描述中提取个人属性
            description = event_data.get("description_text", "")
            topics = event_data.get("topics", [])

            # 解析现有属性
            try:
                existing_attrs = json.loads(user_person.get("key_attributes") or "{}")
                if not isinstance(existing_attrs, dict):
                    existing_attrs = {}
            except:
                existing_attrs = {}

            # 根据topics和description更新属性
            updated = False
            if "饮食偏好" in topics and "喜欢" in description:
                # 提取喜欢的食物
                if "苹果" in description:
                    existing_attrs["饮食偏好"] = existing_attrs.get("饮食偏好", "") + "喜欢吃苹果; "
                    updated = True
                elif "水果" in description:
                    existing_attrs["饮食偏好"] = existing_attrs.get("饮食偏好", "") + "喜欢吃水果; "
                    updated = True

            # 更新档案
            if updated:
                # 清理重复内容
                for key, value in existing_attrs.items():
                    if isinstance(value, str):
                        existing_attrs[key] = value.strip().rstrip(";").strip()

                update_result = update_person_mysql(
                    user_id=user_id, person_id=user_person["person_id"], key_attributes=existing_attrs
                )
                if update_result.get("result") == "success":
                    logger.info(f"[用户档案更新] 从事件中提取并更新了用户个人属性: {existing_attrs}")
                else:
                    logger.error(f"[用户档案更新] 更新失败: {update_result}")

        except Exception as e:
            logger.error(f"从事件更新用户档案失败: {e}")

    def _start_async_memory_generation(
        self, persons: List[dict], events: List[dict], query_text: str, user_context: dict, user_id: str
    ):
        """启动异步记忆生成任务

        注意：此方法不应用于对话检索场景，仅适用于后台记忆处理
        对话检索必须同步执行以确保AI能获得完整上下文信息
        """
        import threading

        def async_generate_memory():
            try:
                logger.info("🔄 [异步任务] 开始后台生成详细记忆...")
                start_time = time.time()

                # 生成详细记忆内容
                detailed_suggestions = self._generate_conversation_suggestions(
                    persons, events, query_text, user_context
                )

                generation_time = time.time() - start_time
                logger.info(f"✅ [异步任务] 详细记忆生成完成，耗时: {generation_time:.3f}s")

                # 这里可以将生成的记忆存储到缓存或数据库中，供后续使用
                # 例如：self._cache_generated_memory(user_id, query_text, detailed_suggestions)

            except Exception as e:
                logger.error(f"❌ [异步任务] 记忆生成失败: {e}")

        # 启动后台线程
        thread = threading.Thread(target=async_generate_memory, daemon=True)
        thread.start()
        logger.info("🚀 [异步任务] 后台记忆生成任务已启动")

    def retrieve_memory_for_conversation(
        self, query_text: str, user_id: str, max_results: int = 5
    ):  # 步骤二：回忆与建议生成 (智能检索版)
        """为对话检索相关记忆并生成建议

        Args:
            query_text: 查询文本
            user_id: 用户ID
            max_results: 最大结果数
        """
        start_time = time.time()

        try:
            # 🚀 优化1: 并行处理意图分析和用户上下文获取
            from concurrent.futures import ThreadPoolExecutor

            with ThreadPoolExecutor(max_workers=2) as executor:
                # 并行执行意图分析和用户上下文判断
                intent_future = executor.submit(self._analyze_query_intent, query_text)

                # 先做简单的用户上下文预判断（不需要等待意图分析结果）
                need_user_context_simple = self._quick_user_context_check(query_text)
                user_context_future = executor.submit(
                    self.user_context_manager.get_user_context, user_id, need_user_context_simple
                )

                # 等待意图分析完成
                intent_result = intent_future.result()
                query_type = intent_result.get("query_type", "C")

                # 如果简单判断不准确，重新获取用户上下文
                need_user_context_accurate = self.user_context_manager.should_include_user_profile(
                    intent_result, query_text
                )
                if need_user_context_accurate != need_user_context_simple:
                    user_context = self.user_context_manager.get_user_context(user_id, need_user_context_accurate)
                else:
                    user_context = user_context_future.result()

            persons = []
            events = []

            # 3. 根据查询类型选择检索策略
            strategy_start = time.time()
            if query_type == "A":
                # A类：直接属性查询 - 精确结构化查询
                logger.info("🎯 [A类查询] 开始直接属性查询")
                persons = self._handle_direct_attribute_query(intent_result, user_id)
                logger.info(f"🎯 [A类查询] 找到 {len(persons)} 个人物")

            elif query_type == "B":
                # B类：反向属性查询 - MySQL属性检索 + ES语义检索
                logger.info("🔄 [B类查询] 开始反向属性查询")
                persons = self._handle_reverse_attribute_query(intent_result, user_id, max_results)
                logger.info(f"🔄 [B类查询] 找到 {len(persons)} 个人物")

            elif query_type == "C":
                # C类：开放式联想查询 - ES事件检索 + MySQL人物检索的综合分析
                logger.info("🌐 [C类查询] 开始开放式联想查询")
                persons, events = self._handle_open_associative_query(intent_result, user_id, max_results)
                logger.info(f"🌐 [C类查询] 找到 {len(persons)} 个人物, {len(events)} 个事件")

            strategy_time = time.time() - strategy_start
            logger.info(f"⚡ [检索策略] 耗时: {strategy_time:.3f}s")

            # 3. 智能整合与生成建议
            if persons or events:
                # 去重
                original_count = len(persons)
                persons = list({p["person_id"]: p for p in persons}.values())
                if original_count != len(persons):
                    logger.info(f"🔧 [去重处理] 从 {original_count} 个减少到 {len(persons)} 个人物")

                # 确保用户本人档案排在第一位（最终返回结果优化）
                user_person = None
                other_persons = []

                for person in persons:
                    if person.get("is_user", False):
                        user_person = person
                    else:
                        other_persons.append(person)

                # 重新排序：用户本人档案放在最前面
                final_persons = []
                if user_person:
                    final_persons.append(user_person)
                final_persons.extend(other_persons)

                # 生成对话建议
                suggestion_start = time.time()
                suggestions = self._generate_conversation_suggestions(final_persons, events, query_text, user_context)
                suggestion_time = time.time() - suggestion_start
                logger.info(f"💡 [建议生成] 耗时: {suggestion_time:.3f}s")

                total_time = time.time() - start_time
                logger.info(
                    f"✅ [查询完成] 总耗时: {total_time:.3f}s, 返回 {len(final_persons)} 个人物, {len(events)} 个事件"
                )

                return {"result": "success", "persons": final_persons, "events": events, "suggestions": suggestions}
            else:
                total_time = time.time() - start_time
                logger.info(f"❌ [查询完成] 总耗时: {total_time:.3f}s, 未找到相关记忆")
                return {"result": "success", "persons": [], "events": [], "suggestions": ""}

        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"💥 [查询失败] 总耗时: {total_time:.3f}s, 错误: {str(e)}")
            return {"result": "error", "reason": str(e)}

    def _quick_user_context_check(self, query_text: str) -> bool:
        """快速检查是否需要用户上下文（不依赖AI分析）"""
        # 简单的关键词检查，避免等待AI分析
        self_keywords = ["我", "我的", "我们", "我家", "我女儿", "我儿子", "我父亲", "我母亲", "我同事"]
        return any(keyword in query_text for keyword in self_keywords)

    def _analyze_query_intent(self, query_text: str) -> dict:
        """分析查询意图，返回查询类型和相关信息"""
        start_time = time.time()
        logger.info(f"🧠 [意图分析] 开始分析查询: '{query_text}'")

        # 🚀 优化2: 检查缓存
        import hashlib

        cache_key = hashlib.md5(query_text.encode()).hexdigest()
        current_time = time.time()

        if cache_key in self._intent_cache:
            cached_result, cache_time = self._intent_cache[cache_key]
            if current_time - cache_time < self._intent_cache_ttl:
                logger.info(f"🚀 [意图分析] 使用缓存结果，耗时: {time.time() - start_time:.3f}s")
                return cached_result

        try:
            # 1. 获取配置
            config_start = time.time()
            prompt = get_value("humanrelation.query_intent_analysis_prompt", "")
            if not prompt:
                logger.warning("⚠️ [意图分析] 未配置查询意图分析提示词，使用默认策略")
                return {
                    "query_type": "C",  # 默认为开放式查询
                    "target_person": "",
                    "target_attribute": "",
                    "search_attributes": [],
                    "keywords": [query_text],
                    "requires_events": True,
                    "confidence": 0.5,
                }
            config_time = time.time() - config_start
            logger.info(f"⚙️ [意图分析] 配置获取耗时: {config_time:.3f}s")

            # 2. 构建AI请求
            query_input = {
                "model": get_value("humanrelation.memory_extraction_model", "gpt-4o-mini"),
                "messages": [{"role": "system", "content": prompt}, {"role": "user", "content": query_text}],
                "stream": False,
                "temperature": 0.0,
                "max_tokens": 300,
            }
            logger.info(f"🤖 [意图分析] 使用模型: {query_input['model']}")

            # 3. 调用AI
            ai_start = time.time()
            response = send_to_ai(query_input)
            ai_time = time.time() - ai_start

            if not response:
                logger.error(f"❌ [意图分析] AI调用失败，耗时: {ai_time:.3f}s")
                return self._get_default_intent_result(query_text)

            logger.info(f"🤖 [意图分析] AI调用成功，耗时: {ai_time:.3f}s")

            # 4. 解析AI响应
            parse_start = time.time()
            try:
                # 正确解析OpenAI API响应格式
                response_data = json.loads(response.text)
                content = response_data["choices"][0]["message"]["content"].strip()
                logger.info(f"📝 [意图分析] AI原始返回: {safe_json_log(content[:200])}")

                # 处理可能的代码块格式
                if content.startswith("```json"):
                    content = content[7:-3].strip()
                elif content.startswith("```"):
                    content = content[3:-3].strip()

                logger.info(f"📝 [意图分析] 处理后内容: {safe_json_log(content[:200])}")

                intent_result = json.loads(content)
                parse_time = time.time() - parse_start
                logger.info(f"✅ [意图分析] JSON解析成功，耗时: {parse_time:.3f}s")

                # 5. 验证返回结果的格式
                validate_start = time.time()
                required_fields = ["query_type", "keywords", "requires_events"]
                missing_fields = []
                for field in required_fields:
                    if field not in intent_result:
                        missing_fields.append(field)

                if missing_fields:
                    logger.warning(f"⚠️ [意图分析] 结果缺少字段: {missing_fields}")
                    logger.warning(f"⚠️ [意图分析] 完整AI响应: {safe_json_log(intent_result)}")
                    return self._get_default_intent_result(query_text)

                # 确保query_type是有效值
                if intent_result["query_type"] not in ["A", "B", "C"]:
                    logger.warning(f"⚠️ [意图分析] 无效查询类型: {intent_result['query_type']}")
                    intent_result["query_type"] = "C"

                validate_time = time.time() - validate_start
                total_time = time.time() - start_time

                logger.info(f"🎯 [意图分析] 验证耗时: {validate_time:.3f}s")
                logger.info(f"✅ [意图分析] 总耗时: {total_time:.3f}s, 查询类型: {intent_result['query_type']}")
                logger.info(f"📊 [意图分析] 最终结果: {safe_json_log(intent_result)}")

                # 🚀 优化2: 存储到缓存
                self._store_intent_cache(cache_key, intent_result, current_time)

                return intent_result

            except json.JSONDecodeError as e:
                parse_time = time.time() - parse_start
                total_time = time.time() - start_time
                logger.error(f"❌ [意图分析] JSON解析失败，解析耗时: {parse_time:.3f}s, 总耗时: {total_time:.3f}s")
                logger.error(f"❌ [意图分析] 解析错误: {e}")
                logger.error(f"❌ [意图分析] 原始响应: {safe_json_log(response.text[:500])}")
                return self._get_default_intent_result(query_text)

        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"💥 [意图分析] 分析失败，总耗时: {total_time:.3f}s, 错误: {e}")
            return self._get_default_intent_result(query_text)

    def _store_intent_cache(self, cache_key: str, result: dict, timestamp: float):
        """存储意图分析结果到缓存"""
        # 清理过期缓存
        if len(self._intent_cache) >= self._intent_cache_max_size:
            current_time = time.time()
            expired_keys = [
                key
                for key, (_, cache_time) in self._intent_cache.items()
                if current_time - cache_time >= self._intent_cache_ttl
            ]
            for key in expired_keys:
                del self._intent_cache[key]

            # 如果还是太多，删除最旧的
            if len(self._intent_cache) >= self._intent_cache_max_size:
                oldest_key = min(self._intent_cache.keys(), key=lambda k: self._intent_cache[k][1])
                del self._intent_cache[oldest_key]

        self._intent_cache[cache_key] = (result, timestamp)
        logger.debug(f"🚀 [缓存] 存储意图分析结果，缓存大小: {len(self._intent_cache)}")

    def _get_default_intent_result(self, query_text: str) -> dict:
        """获取默认的意图分析结果"""
        return {
            "query_type": "C",
            "target_person": "",
            "target_attribute": "",
            "search_attributes": [],
            "keywords": [query_text],
            "requires_events": True,
            "confidence": 0.5,
        }

    def _extract_names_from_query(self, query_text: str, user_id: str) -> List[str]:
        """使用LLM从查询中提取人名，若失败则使用正则+SQL兜底"""
        try:
            prompt = get_value(
                "humanrelation.retrieval_entity_extraction_prompt", "Extract person names. Return JSON list."
            )

            query_input = {
                "model": get_value("humanrelation.memory_extraction_model", "gpt-4o-mini"),
                "messages": [{"role": "system", "content": prompt}, {"role": "user", "content": query_text}],
                "stream": False,
                "temperature": 0.0,
                "max_tokens": 100,
            }

            response = send_to_ai(query_input)
            names: List[str] = []
            if response:
                response_text = response.text
                if response_text.startswith("```json"):
                    response_text = response_text[7:-3].strip()
                try:
                    parsed = json.loads(response_text)
                    if isinstance(parsed, list):
                        names = [n for n in parsed if isinstance(n, str)]
                except Exception as e:
                    logger.warning(f"LLM人名解析失败:{e}, 原始响应:{response_text}")

            # 新增：如果输入包含“我”或“自己”，自动加入当前用户的名字和别名
            if "我" in query_text or "自己" in query_text:
                try:
                    from service.mysql_person_service import get_user_person

                    my_profile = get_user_person(user_id)
                    if my_profile:
                        canonical_name = my_profile.get("canonical_name", "")
                        if canonical_name:
                            names.append(canonical_name)
                        aliases = my_profile.get("aliases", "")
                        if aliases:
                            # 支持逗号、分号、空格分隔
                            import re

                            alias_list = re.split(r"[;,，\s]+", aliases)
                            for alias in alias_list:
                                if alias:
                                    names.append(alias)
                except Exception as e:
                    logger.warning(f"自动映射'我'为用户本人失败: {e}")

            # ---------- Fallback: 新的、更智能的兜底策略 ----------
            if not names:
                # 新的后备策略：从数据库加载已知人名进行匹配，而不是用正则猜测
                logger.info("LLM提取人名失败，启用数据库已知人名匹配策略...")
                try:
                    from my_mysql.entity.person_table import person_memory
                    from my_mysql.sql_client import CLIENT
                    from sqlalchemy import select

                    conn = CLIENT.connect()
                    try:
                        # 1. 从数据库获取当前用户的所有人物的姓名和别名
                        select_stmt = select(person_memory.c.canonical_name, person_memory.c.aliases).where(
                            person_memory.c.user_id == user_id
                        )
                        all_persons_res = conn.execute(select_stmt).fetchall()

                        known_names = set()
                        for person in all_persons_res:
                            known_names.add(person.canonical_name)
                            if person.aliases:
                                try:
                                    aliases = json.loads(person.aliases)
                                    if isinstance(aliases, list):
                                        for alias in aliases:
                                            known_names.add(alias)
                                except (json.JSONDecodeError, TypeError):
                                    pass  # 忽略无法解析的别名

                        # 2. 检查查询文本中是否包含任何已知人名
                        for name in known_names:
                            if name and name in query_text:  # 确保名字不为空
                                names.append(name)
                    finally:
                        conn.close()

                except Exception as db_e:
                    logger.error(f"数据库人名匹配后备方案失败: {db_e}")

            # 去重
            names = list(dict.fromkeys(names))
            logger.info(f"最终提取人名: {names}")
            return names
        except Exception as e:
            logger.error(f"从查询中提取人名失败: {e}")
            return []

    def _generate_conversation_suggestions(
        self, persons: List[dict], events: List[dict], query_context: str, user_context: dict = None
    ):  # 生成对话建议
        try:
            prompt = get_value("humanrelation.memory_generation_prompt", "")
            if not prompt:
                return ""

            # 递归把 datetime 转字符串
            def dt2str(obj):
                from datetime import datetime

                if isinstance(obj, dict):
                    return {k: dt2str(v) for k, v in obj.items()}
                if isinstance(obj, list):
                    return [dt2str(v) for v in obj]
                if isinstance(obj, datetime):
                    return obj.isoformat()
                return obj

            # 清理事件数据，移除不必要的 vector 字段
            cleaned_events = []
            for event in events:
                clean_event = event.copy()
                clean_event.pop("vector", None)  # 安全地移除 vector
                cleaned_events.append(clean_event)

            # 优化：确保用户本人档案优先处理
            user_person = None
            other_persons = []

            for person in persons:
                if person.get("is_user", False):
                    user_person = person
                else:
                    other_persons.append(person)

            # 重新排序：用户本人档案放在最前面
            ordered_persons = []
            if user_person:
                ordered_persons.append(user_person)
            ordered_persons.extend(other_persons)

            context_data = {
                "persons": dt2str(ordered_persons),
                "events": dt2str(cleaned_events),
                "query_context": query_context,
            }

            # 优化：使用用户上下文管理器动态增强提示词
            enhanced_prompt = prompt

            # 如果有用户上下文，添加用户档案重要性说明
            if user_context and user_context.get("has_user_context", False):
                user_summary = user_context.get("user_summary", "")
                enhanced_prompt += f"""

🎯 重要提示：
1. 在提供的人物档案中，标记为 is_user=true 的是当前用户本人的档案，这是最重要的信息源
2. 当用户询问关于自己的信息时（如"我的..."、"我女儿..."等），应该优先从用户本人档案中查找答案
3. 用户本人档案的 key_attributes 包含了用户的详细个人信息，包括家庭关系、职业信息等
4. 请基于用户本人档案的实际内容生成准确的对话建议，而不是基于其他人物的信息进行推测

当前用户档案摘要：
{user_summary}
- 档案标识：is_user=true（用户本人）
- 主要信息：请重点关注此档案的 key_attributes 字段"""

            query_input = {
                "model": get_value("humanrelation.memory_extraction_model", "gpt-4o-mini"),
                "messages": [
                    {"role": "system", "content": enhanced_prompt},
                    {"role": "user", "content": json.dumps(context_data, ensure_ascii=False)},
                ],
                "stream": False,
                "temperature": 0.7,
                "max_tokens": 500,
            }

            response = send_to_ai(query_input)
            response_data = json.loads(response.text)
            suggestions = response_data["choices"][0]["message"]["content"].strip()

            return suggestions
        except Exception as e:
            logger.error(f"生成对话建议失败: {str(e)}")
            return ""

    def _get_all_user_ids(self) -> List[str]:
        """获取所有唯一的用户ID"""
        from my_mysql.entity.person_table import person_memory
        from my_mysql.sql_client import CLIENT
        from sqlalchemy import distinct, select

        conn = None
        try:
            conn = CLIENT.connect()
            stmt = select(distinct(person_memory.c.user_id))
            results = conn.execute(stmt).fetchall()
            return [row[0] for row in results]
        except Exception as e:
            logger.error(f"获取所有用户ID失败: {e}")
            return []
        finally:
            if conn:
                conn.close()

    def _long_term_memory_worker_loop(self):
        """后台工作线程循环，定期执行长期记忆更新任务。"""
        INITIAL_DELAY = 10 * 60  # 10分钟
        WEEKLY_INTERVAL = 7 * 24 * 60 * 60  # 7天

        logger.info(f"后台长期记忆更新工作线程已启动。首次任务将在 {INITIAL_DELAY // 60} 分钟后执行。")

        # 首次启动时的初始延迟
        if self._stop_event.wait(INITIAL_DELAY):
            logger.info("后台长期记忆更新工作线程在初始等待期间被停止。")
            return  # 线程直接退出

        while not self._stop_event.is_set():
            try:
                logger.info("开始执行定期长期记忆更新任务...")

                user_ids = self._get_all_user_ids()
                if not user_ids:
                    logger.info("未找到任何用户，跳过本轮长期记忆更新。")
                else:
                    logger.info(f"将为 {len(user_ids)} 位用户更新长期记忆。")
                    for user_id in user_ids:
                        # 在处理每个用户前再次检查，以便能快速响应停止信号
                        if self._stop_event.is_set():
                            logger.info("在处理用户列表时检测到停止信号，中断更新。")
                            break
                        try:
                            logger.info(f"正在为用户 {user_id} 更新长期记忆...")
                            self.update_long_term_memory(user_id)
                        except Exception as e:
                            logger.error(f"为用户 {user_id} 更新长期记忆失败: {e}")

                logger.info("本轮长期记忆更新任务完成。")

            except Exception as e:
                logger.error(f"后台长期记忆更新工作线程出现严重错误: {e}")

            # 等待下一次执行（每周）
            logger.info(f"下一次长期记忆更新将在 {WEEKLY_INTERVAL // (24 * 3600)} 天后执行。")
            if self._stop_event.wait(WEEKLY_INTERVAL):
                break  # 如果在等待期间被停止，则跳出循环

        logger.info("后台长期记忆更新工作线程已停止。")

    def start_long_term_memory_update_thread(self):
        """启动长期记忆更新的后台线程"""
        if not hasattr(self, "_worker_thread") or not self._worker_thread.is_alive():
            self._stop_event = threading.Event()
            self._worker_thread = threading.Thread(target=self._long_term_memory_worker_loop, daemon=True)
            self._worker_thread.start()
            logger.info("成功启动后台长期记忆更新工作线程。")
            return True
        logger.info("后台长期记忆更新工作线程已在运行中。")
        return False

    def update_long_term_memory(self, user_id: str):  # 定期更新长期记忆
        """定期将短期记忆总结为长期记忆（人物小传更新）"""
        try:
            # 获取最近的事件记忆，增加数量以适应周更频率
            recent_events_res = get_recent_events(index_name=self.event_index, user_id=user_id, size=200)
            if recent_events_res.get("result") != "success" or not recent_events_res.get("events"):
                return {"result": "success", "message": "没有新的事件记忆需要处理"}

            recent_events = recent_events_res["events"]

            # 按人物分组事件
            person_events = self._group_events_by_person(recent_events)

            updated_persons = []
            for person_identifier, events in person_events.items():
                # person_identifier 可能是 person_id 或者人名
                # 先尝试按 person_id 查找，如果失败再按名称查找
                person = None

                # 尝试按 person_id 查找
                person_result = get_person_by_id_mysql(user_id=user_id, person_id=person_identifier)
                if person_result.get("result") == "success" and person_result.get("person"):
                    person = person_result["person"]
                else:
                    # 按名称查找（兜底逻辑，处理旧数据或未找到档案的情况）
                    search_result = search_persons_by_name_mysql(user_id=user_id, name=person_identifier, limit=1)
                    if search_result.get("result") == "success" and search_result.get("persons"):
                        person = search_result["persons"][0]

                if person:
                    # 总结事件为人物小传
                    updated_summary = self._summarize_events_to_profile(person, events)
                    if updated_summary:
                        # 更新人物档案
                        update_result = update_person_mysql(
                            user_id=user_id, person_id=person["person_id"], profile_summary=updated_summary
                        )
                        if update_result["result"] == "success":
                            updated_persons.append(person.get("canonical_name", person_identifier))

            return {"result": "success", "updated_persons": updated_persons}
        except Exception as e:
            logger.error(f"更新长期记忆失败: {str(e)}")
            return {"result": "error", "reason": str(e)}

    def _group_events_by_person(self, events: List[dict]):  # 按人物分组事件
        person_events = {}
        for event in events:
            participants = event.get("participants", [])
            for participant_id in participants:
                # 检查是否为用户本人
                from service.mysql_person_service import is_current_user

                if not is_current_user(event.get("user_id"), participant_id):
                    if participant_id not in person_events:
                        person_events[participant_id] = []
                    person_events[participant_id].append(event)
        return person_events

    def _summarize_events_to_profile(self, person: dict, events: List[dict]):  # 将事件总结为人物小传
        """调用LLM，根据人物现有信息和新事件，生成更新后的人物小传"""
        try:
            prompt_template = get_value("humanrelation.long_memory_summarization_prompt")
            if not prompt_template:
                logger.error("长期记忆总结提示词 'humanrelation.long_memory_summarization_prompt' 未在配置中心配置。")
                return None

            current_date_str = datetime.now().strftime("%Y-%m-%d")
            prompt = prompt_template.format(current_date=current_date_str)

            # 递归把 datetime 转字符串以防json序列化失败
            def dt2str(obj):
                from datetime import datetime

                if isinstance(obj, dict):
                    return {k: dt2str(v) for k, v in obj.items()}
                if isinstance(obj, list):
                    return [dt2str(v) for v in obj]
                if isinstance(obj, datetime):
                    return obj.isoformat()
                return obj

            # 清理数据，避免JSON序列化问题
            def clean_data(obj):
                if isinstance(obj, dict):
                    cleaned = {}
                    for k, v in obj.items():
                        cleaned_v = clean_data(v)
                        if cleaned_v is not None:
                            cleaned[k] = cleaned_v
                    return cleaned
                elif isinstance(obj, list):
                    return [clean_data(v) for v in obj if clean_data(v) is not None]
                elif isinstance(obj, str):
                    # 修复常见的JSON问题
                    if obj == "[object Object]":
                        return ""
                    return obj
                else:
                    return obj

            context_data = {"person": clean_data(dt2str(person)), "events": clean_data(dt2str(events))}

            # 生成用户内容并检查长度
            user_content = json.dumps(context_data, ensure_ascii=False)
            logger.info(f"生成人物小传请求内容长度: {len(user_content)} 字符")

            query_input = {
                "model": get_value("humanrelation.memory_extraction_model", "gpt-4o-mini"),
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": user_content},
                ],
                "stream": False,
                "temperature": 0.5,
                "max_tokens": 800,
            }

            # 添加重试机制处理间歇性失败
            max_retries = 10
            for attempt in range(max_retries):
                response = send_to_ai(query_input)
                if response and response.status_code == 200:
                    break

                if not response:
                    logger.warning(f"AI调用失败，尝试 {attempt + 1}/{max_retries}")
                elif response.status_code != 200:
                    logger.warning(
                        f"AI API返回错误状态码: {response.status_code}, 尝试 {attempt + 1}/{max_retries}, 响应: {response.text}"
                    )

                if attempt < max_retries - 1:
                    import time

                    time.sleep(1)  # 等待1秒后重试

            if not response:
                logger.error(f"AI调用失败，重试{max_retries}次后仍然失败。请求参数: {query_input}")
                return None

            # 检查HTTP状态码
            if response.status_code != 200:
                logger.error(f"AI API返回错误状态码: {response.status_code}, 响应内容: {response.text}")
                return None

            try:
                response_data = json.loads(response.text)
                if "choices" not in response_data:
                    logger.error(f"AI响应格式错误，缺少choices字段: {response_data}")
                    return None

                summary = response_data["choices"][0]["message"]["content"].strip()
                return summary
            except (json.JSONDecodeError, KeyError, IndexError) as e:
                logger.error(f"解析AI响应失败: {str(e)}, 响应内容: {response.text if response else 'None'}")
                return None
        except Exception as e:
            logger.error(f"总结人物小传失败: {str(e)}")
            return None

    def check_outdated_information(self, user_id: str, days_threshold: int = 180):  # 检查过期信息
        """检查并标记可能过期的信息点"""
        try:
            all_persons_res = get_all_persons_mysql(user_id=user_id, limit=1000)
            if all_persons_res.get("result") != "success":
                return

            outdated_persons = []
            cutoff_date = datetime.now() - timedelta(days=days_threshold)

            for person in all_persons_res["persons"]:
                key_attributes = person.get("key_attributes", {})
                for key, value in key_attributes.items():
                    if key.endswith("_更新时间"):
                        try:
                            update_time = datetime.fromisoformat(value)
                            if update_time < cutoff_date:
                                field_name = key.replace("_更新时间", "")
                                outdated_persons.append(
                                    {
                                        "person_id": person["person_id"],
                                        "person_name": person["canonical_name"],
                                        "field": field_name,
                                        "value": key_attributes.get(field_name),
                                        "last_update": value,
                                    }
                                )
                        except:
                            pass

            return {"result": "success", "outdated_info": outdated_persons}
        except Exception as e:
            logger.error(f"检查过期信息失败: {e}")
            return {"result": "error", "reason": str(e)}

    def _handle_alias_relation(self, data: dict, user_id: str):
        """处理别名关系：A是B，将B作为A的别名"""
        try:
            original_name = data.get("original_name", "")
            alias_name = data.get("alias_name", "")

            if not original_name or not alias_name:
                return {"action_code": "ERROR", "payload": {"reason": "别名关系信息不完整"}}

            # 查找原始人物
            search_result = search_persons_by_name_mysql(user_id=user_id, name=original_name, limit=1)
            if not search_result.get("persons"):
                return {"action_code": "ERROR", "payload": {"reason": f"找不到人物: {original_name}"}}

            person = search_result["persons"][0]
            try:
                current_aliases = json.loads(person.get("aliases", "[]")) if person.get("aliases") else []
                if not isinstance(current_aliases, list):
                    current_aliases = []
            except (json.JSONDecodeError, TypeError):
                current_aliases = []

            # 检查别名是否已存在于其他档案中
            alias_search = search_persons_by_name_mysql(user_id=user_id, name=alias_name, limit=5)
            if alias_search.get("persons"):
                # 如果别名已作为其他人的正式姓名存在，需要合并
                for existing_person in alias_search["persons"]:
                    if existing_person["canonical_name"] == alias_name:
                        return self._merge_two_persons(user_id, person["person_id"], existing_person["person_id"])

            # 添加别名
            if alias_name not in current_aliases:
                current_aliases.append(alias_name)
                update_result = update_person_mysql(
                    user_id=user_id,
                    person_id=person["person_id"],
                    aliases=json.dumps(current_aliases, ensure_ascii=False),
                )
                if update_result["result"] == "success":
                    logger.info(f"为{original_name}添加别名{alias_name}")
                    return {
                        "action_code": "PROCESS_COMPLETE",
                        "payload": {"message": f"已将'{alias_name}'添加为'{original_name}'的别名"},
                    }

            return {"action_code": "PROCESS_COMPLETE", "payload": {"message": "别名已存在"}}
        except Exception as e:
            logger.error(f"处理别名关系失败: {e}")
            return {"action_code": "ERROR", "payload": {"reason": str(e)}}

    def _handle_rename_relation(self, data: dict, user_id: str):
        """处理正名关系：A的大名叫B，将A的正式姓名改为B"""
        try:
            current_name = data.get("current_name", "")
            formal_name = data.get("formal_name", "")

            if not current_name or not formal_name:
                return {"action_code": "ERROR", "payload": {"reason": "正名关系信息不完整"}}

            # 查找当前人物
            search_result = search_persons_by_name_mysql(user_id=user_id, name=current_name, limit=1)
            if not search_result.get("persons"):
                return {"action_code": "ERROR", "payload": {"reason": f"找不到人物: {current_name}"}}

            person = search_result["persons"][0]

            # 检查正式姓名是否已被其他人使用
            formal_search = search_persons_by_name_mysql(user_id=user_id, name=formal_name, limit=5)
            if formal_search.get("persons"):
                for existing_person in formal_search["persons"]:
                    if (
                        existing_person["canonical_name"] == formal_name
                        and existing_person["person_id"] != person["person_id"]
                    ):
                        # 需要合并两个档案
                        return self._merge_two_persons(user_id, existing_person["person_id"], person["person_id"])

            # 更新正式姓名，将原名加入别名
            try:
                current_aliases = json.loads(person.get("aliases", "[]")) if person.get("aliases") else []
                if not isinstance(current_aliases, list):
                    current_aliases = []
            except (json.JSONDecodeError, TypeError):
                current_aliases = []

            if person["canonical_name"] not in current_aliases:
                current_aliases.append(person["canonical_name"])

            update_result = update_person_mysql(
                user_id=user_id,
                person_id=person["person_id"],
                canonical_name=formal_name,
                aliases=json.dumps(current_aliases, ensure_ascii=False),
            )
            if update_result["result"] == "success":
                logger.info(f"将{current_name}的正式姓名更新为{formal_name}")
                return {
                    "action_code": "PROCESS_COMPLETE",
                    "payload": {"message": f"已将'{current_name}'的正式姓名更新为'{formal_name}'"},
                }

            return {"action_code": "ERROR", "payload": {"reason": "更新失败"}}
        except Exception as e:
            logger.error(f"处理正名关系失败: {e}")
            return {"action_code": "ERROR", "payload": {"reason": str(e)}}

    def _merge_two_persons(self, user_id: str, primary_person_id: str, secondary_person_id: str):
        """内部合并两个人物档案的辅助方法"""
        try:
            result = self.execute_merge_persons(user_id, primary_person_id, secondary_person_id)
            if result["result"] == "success":
                return {"action_code": "PROCESS_COMPLETE", "payload": {"message": "档案已自动合并"}}
            else:
                return {"action_code": "ERROR", "payload": {"reason": result.get("reason", "合并失败")}}
        except Exception as e:
            logger.error(f"合并档案失败: {e}")
            return {"action_code": "ERROR", "payload": {"reason": str(e)}}

    def _replace_relative_time(self, text: str) -> str:
        """将文本中的相对时间（如"昨天"）替换为绝对日期（YYYY-MM-DD）"""
        if not isinstance(text, str):
            return text

        today = datetime.now().date()
        time_map = {
            "前天": today - timedelta(days=2),
            "昨天": today - timedelta(days=1),
            "今天": today,
            "明天": today + timedelta(days=1),
            "后天": today + timedelta(days=2),
        }

        original_text = text
        for word, date_obj in time_map.items():
            if word in text:
                # 只保留具体日期，不包含相对时间词汇
                text = text.replace(word, date_obj.strftime("%Y-%m-%d"))

        if text != original_text:
            logger.info(f"时间表达式转换: '{original_text}' -> '{text}'")

        return text

    def _handle_direct_attribute_query(self, intent_result: dict, user_id: str) -> List[dict]:
        """处理A类查询：直接属性查询"""
        start_time = time.time()
        target_person = intent_result.get("target_person", "")
        target_attribute = intent_result.get("target_attribute", "")

        logger.info(f"🎯 [A类查询] 开始处理，目标人物: '{target_person}', 目标属性: '{target_attribute}'")

        try:
            if not target_person:
                logger.warning("⚠️ [A类查询] 目标人物为空，返回空结果")
                return []

            # 精确查找指定人物
            search_start = time.time()
            persons_result = search_persons_by_name_mysql(user_id=user_id, name=target_person, limit=1)
            persons = persons_result.get("persons", [])
            search_time = time.time() - search_start

            total_time = time.time() - start_time
            logger.info(f"🎯 [A类查询] MySQL查询耗时: {search_time:.3f}s, 总耗时: {total_time:.3f}s")
            logger.info(f"🎯 [A类查询] 查找'{target_person}'的'{target_attribute}'，找到{len(persons)}个结果")

            if persons:
                logger.info(f"🎯 [A类查询] 找到人物: {[p.get('canonical_name') for p in persons]}")

            return persons

        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"💥 [A类查询] 处理失败，总耗时: {total_time:.3f}s, 错误: {e}")
            return []

    def _handle_reverse_attribute_query(self, intent_result: dict, user_id: str, max_results: int) -> List[dict]:
        """处理B类查询：反向属性查询"""
        start_time = time.time()
        search_attributes = intent_result.get("search_attributes", [])
        keywords = intent_result.get("keywords", [])

        logger.info(f"🔄 [B类查询] 开始处理，搜索属性: {search_attributes}, 关键词: {keywords}")

        try:
            persons = []

            # 【增强】特殊处理关系查询（如"我女儿是谁"）
            relationship_start = time.time()
            relationship_queries = ["女儿", "儿子", "妻子", "丈夫", "父亲", "母亲", "同事", "朋友"]
            relationship_found = False

            for attr in search_attributes:
                if attr in relationship_queries:
                    relationship_found = True
                    logger.info(f"🔗 [B类查询] 检测到关系查询: '{attr}', 使用关系检索策略")

                    rel_search_start = time.time()
                    relationship_persons = self._search_by_relationship(user_id, attr, max_results)
                    rel_search_time = time.time() - rel_search_start

                    persons.extend(relationship_persons)
                    logger.info(
                        f"🔗 [B类查询] 关系查询 '{attr}' 耗时: {rel_search_time:.3f}s, 找到 {len(relationship_persons)} 个结果"
                    )

            relationship_time = time.time() - relationship_start
            if relationship_found:
                logger.info(f"🔗 [B类查询] 关系查询总耗时: {relationship_time:.3f}s")

            # 1. MySQL属性检索
            mysql_start = time.time()
            if search_attributes:
                from service.mysql_person_service import search_persons_by_attributes_mysql

                logger.info(f"🗄️ [B类查询] 开始MySQL属性检索，属性: {search_attributes}")
                mysql_result = search_persons_by_attributes_mysql(user_id, search_attributes, max_results)
                mysql_time = time.time() - mysql_start

                if mysql_result.get("result") == "success":
                    mysql_persons = mysql_result.get("persons", [])
                    persons.extend(mysql_persons)
                    logger.info(f"🗄️ [B类查询] MySQL属性检索耗时: {mysql_time:.3f}s, 找到 {len(mysql_persons)} 个结果")
                else:
                    logger.warning(f"⚠️ [B类查询] MySQL属性检索失败: {mysql_result.get('reason', '未知错误')}")
            else:
                mysql_time = time.time() - mysql_start
                logger.info(f"🗄️ [B类查询] 无搜索属性，跳过MySQL检索，耗时: {mysql_time:.3f}s")

            # 2. ES文本检索人物档案（不使用向量搜索）
            es_start = time.time()
            es_persons_count = 0
            try:
                from service.ESmemory.es_memory_client import client

                logger.info(f"🔍 [B类查询] 开始ES人物档案检索，关键词: {keywords}")

                # 搜索人物档案类型的记忆
                for keyword in keywords:
                    if keyword:
                        keyword_start = time.time()
                        search_body = {
                            "size": max_results,
                            "query": {
                                "bool": {
                                    "must": {
                                        "multi_match": {
                                            "query": keyword,
                                            "fields": ["memory_content", "canonical_name", "profile_summary"],
                                            "type": "best_fields",
                                        }
                                    },
                                    "filter": [
                                        {"term": {"user_id": user_id}},
                                        {"term": {"memory_type": "person_profile"}},
                                    ],
                                }
                            },
                        }

                        es_result = client.search(index=self.event_index, body=search_body)
                        keyword_time = time.time() - keyword_start

                        # 从ES结果中提取人物信息
                        keyword_persons = 0
                        for hit in es_result.get("hits", {}).get("hits", []):
                            source = hit.get("_source", {})
                            if source.get("memory_type") == "person_profile":
                                person_data = {
                                    "person_id": source.get("person_id"),
                                    "canonical_name": source.get("canonical_name"),
                                    "profile_summary": source.get("profile_summary"),
                                    "key_attributes": source.get("key_attributes", {}),
                                    "aliases": source.get("aliases", ""),
                                    "relationships": source.get("relationships", []),
                                    "is_user": source.get("is_user", False),
                                }
                                persons.append(person_data)
                                keyword_persons += 1

                        es_persons_count += keyword_persons
                        logger.info(
                            f"🔍 [B类查询] 关键词 '{keyword}' ES检索耗时: {keyword_time:.3f}s, 找到 {keyword_persons} 个人物"
                        )

            except Exception as es_e:
                logger.warning(f"⚠️ [B类查询] ES人物档案检索失败: {es_e}")

            es_time = time.time() - es_start
            total_time = time.time() - start_time

            logger.info(f"🔍 [B类查询] ES检索总耗时: {es_time:.3f}s, 找到 {es_persons_count} 个人物")
            logger.info(f"✅ [B类查询] 处理完成，总耗时: {total_time:.3f}s, 总共找到 {len(persons)} 个结果")

            if persons:
                person_names = [p.get("canonical_name", "Unknown") for p in persons[:5]]  # 只显示前5个
                logger.info(f"🔄 [B类查询] 找到的人物: {person_names}{'...' if len(persons) > 5 else ''}")

            return persons

        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"💥 [B类查询] 处理失败，总耗时: {total_time:.3f}s, 错误: {e}")
            return []

    def _search_by_relationship(self, user_id: str, relationship_type: str, max_results: int) -> List[dict]:
        """根据关系类型查找人物"""
        start_time = time.time()
        logger.info(f"🔗 [关系查询] 开始查找关系类型: '{relationship_type}', 最大结果数: {max_results}")

        try:
            from service.mysql_person_service import get_all_persons_mysql

            # 获取所有人物
            fetch_start = time.time()
            all_persons_result = get_all_persons_mysql(user_id, limit=100)
            fetch_time = time.time() - fetch_start

            if all_persons_result.get("result") != "success":
                logger.error(f"❌ [关系查询] 获取人物列表失败: {all_persons_result}")
                return []

            all_persons = all_persons_result.get("persons", [])
            logger.info(f"🗄️ [关系查询] 获取人物列表耗时: {fetch_time:.3f}s, 总人数: {len(all_persons)}")

            # 查找与USER有指定关系的人物
            search_start = time.time()
            matching_persons = []
            processed_count = 0

            for person in all_persons:
                processed_count += 1
                relationships = person.get("relationships", [])
                if isinstance(relationships, str):
                    try:
                        relationships = json.loads(relationships)
                    except json.JSONDecodeError:
                        relationships = []
                elif relationships is None:
                    relationships = []

                # 确保relationships是列表
                if not isinstance(relationships, list):
                    relationships = []

                # 检查是否与USER有指定关系
                for rel in relationships:
                    if rel.get("type") == relationship_type and rel.get("target") in ["USER", "我"]:
                        matching_persons.append(person)
                        logger.info(
                            f"✅ [关系查询] 找到匹配: {person.get('canonical_name')} -> {relationship_type} -> USER"
                        )
                        break

                # 限制结果数量
                if len(matching_persons) >= max_results:
                    logger.info(f"🔢 [关系查询] 达到最大结果数限制: {max_results}")
                    break

            search_time = time.time() - search_start
            total_time = time.time() - start_time

            logger.info(
                f"🔍 [关系查询] 关系匹配耗时: {search_time:.3f}s, 处理了 {processed_count}/{len(all_persons)} 个人物"
            )
            logger.info(f"✅ [关系查询] 查询完成，总耗时: {total_time:.3f}s, 找到 {len(matching_persons)} 个匹配结果")

            if matching_persons:
                match_names = [p.get("canonical_name", "Unknown") for p in matching_persons]
                logger.info(f"🔗 [关系查询] 匹配的人物: {match_names}")

            return matching_persons

        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"💥 [关系查询] 查询失败，总耗时: {total_time:.3f}s, 错误: {e}")
            return []

    def _handle_open_associative_query(self, intent_result: dict, user_id: str, max_results: int) -> tuple:
        """处理C类查询：开放式联想查询"""
        start_time = time.time()
        keywords = intent_result.get("keywords", [])
        requires_events = intent_result.get("requires_events", True)
        target_person = intent_result.get("target_person", "")

        logger.info(
            f"🌐 [C类查询] 开始处理，关键词: {keywords}, 目标人物: '{target_person}', 需要事件: {requires_events}"
        )

        try:
            persons = []
            events = []

            # 1. 如果有明确的目标人物，先查找该人物
            if target_person:
                person_start = time.time()
                logger.info(f"👤 [C类查询] 查找目标人物: '{target_person}'")

                persons_result = search_persons_by_name_mysql(user_id=user_id, name=target_person, limit=1)
                person_time = time.time() - person_start

                if persons_result.get("persons"):
                    persons.extend(persons_result["persons"])
                    logger.info(
                        f"👤 [C类查询] 目标人物查找耗时: {person_time:.3f}s, 找到 {len(persons_result['persons'])} 个人物"
                    )
                else:
                    logger.info(f"👤 [C类查询] 目标人物查找耗时: {person_time:.3f}s, 未找到人物")

            # 2. 事件记忆检索
            if requires_events:
                event_start = time.time()
                query_text = " ".join(keywords) if keywords else intent_result.get("query_text", "")
                logger.info(f"📅 [C类查询] 开始事件检索，查询文本: '{query_text}'")

                events_result = search_events_by_text(
                    index_name=self.event_index, user_id=user_id, query_text=query_text, max_results=max_results
                )
                event_time = time.time() - event_start

                if events_result.get("result") == "success":
                    events = events_result.get("events", [])
                    logger.info(f"📅 [C类查询] 事件检索耗时: {event_time:.3f}s, 找到 {len(events)} 个事件")
                else:
                    logger.warning(
                        f"⚠️ [C类查询] 事件检索失败，耗时: {event_time:.3f}s, 原因: {events_result.get('reason', '未知')}"
                    )
            else:
                logger.info("📅 [C类查询] 跳过事件检索")

            # 3. 从事件中提取相关人物
            if events and not target_person:
                extract_start = time.time()
                logger.info(f"🔍 [C类查询] 从 {len(events)} 个事件中提取参与者")

                # 提取事件参与者并查找对应的人物档案
                participant_names = set()
                for event in events:
                    participants = event.get("participants", [])
                    if isinstance(participants, list):
                        participant_names.update(participants)

                logger.info(
                    f"🔍 [C类查询] 提取到 {len(participant_names)} 个唯一参与者: {list(participant_names)[:10]}{'...' if len(participant_names) > 10 else ''}"
                )

                # 查找参与者的详细档案
                participant_search_start = time.time()
                found_participants = 0
                for name in participant_names:
                    if name and name != "USER":  # 排除USER标识
                        persons_result = search_persons_by_name_mysql(user_id=user_id, name=name, limit=1)
                        if persons_result.get("persons"):
                            persons.extend(persons_result["persons"])
                            found_participants += 1

                participant_search_time = time.time() - participant_search_start
                extract_time = time.time() - extract_start

                logger.info(
                    f"🔍 [C类查询] 参与者档案查找耗时: {participant_search_time:.3f}s, 找到 {found_participants} 个人物档案"
                )
                logger.info(f"🔍 [C类查询] 参与者提取总耗时: {extract_time:.3f}s")

            total_time = time.time() - start_time
            logger.info(
                f"✅ [C类查询] 处理完成，总耗时: {total_time:.3f}s, 找到 {len(persons)} 个人物, {len(events)} 个事件"
            )

            if persons:
                person_names = [p.get("canonical_name", "Unknown") for p in persons[:5]]
                logger.info(f"🌐 [C类查询] 找到的人物: {person_names}{'...' if len(persons) > 5 else ''}")

            if events:
                logger.info(f"🌐 [C类查询] 找到的事件数量: {len(events)}")

            return persons, events

        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"💥 [C类查询] 处理失败，总耗时: {total_time:.3f}s, 错误: {e}")
            return [], []
