"""
用户上下文管理器
负责智能判断何时需要用户档案，并提供相应的用户上下文信息
"""

import logging
from typing import Any, Dict, Optional

from service.mysql_person_service import get_all_persons_mysql

logger = logging.getLogger(__name__)


class UserContextManager:
    """用户上下文管理器"""

    def __init__(self):
        self._user_cache = {}  # 简单的内存缓存

    def should_include_user_profile(self, query_intent: dict, query_text: str) -> bool:
        """
        智能判断是否需要用户档案

        Args:
            query_intent: 查询意图分析结果
            query_text: 原始查询文本

        Returns:
            bool: 是否需要用户档案
        """
        query_type = query_intent.get("query_type", "")

        # 1. B类查询强制需要用户档案
        if query_type == "B":
            logger.info("🎯 [用户上下文] B类查询，强制包含用户档案")
            return True

        # 2. 包含自指代词的查询需要用户档案
        self_keywords = ["我", "我的", "我们", "我家", "我女儿", "我儿子", "我父亲", "我母亲", "我同事"]
        if any(keyword in query_text for keyword in self_keywords):
            logger.info(f"🎯 [用户上下文] 检测到自指代词，包含用户档案")
            return True

        # 3. C类查询根据具体内容判断
        if query_type == "C":
            # 如果是个性化建议类查询，需要用户档案
            personalized_keywords = ["建议", "推荐", "应该", "怎么办", "最近", "重要"]
            if any(keyword in query_text for keyword in personalized_keywords):
                logger.info("🎯 [用户上下文] C类个性化查询，包含用户档案")
                return True

        # 4. 其他情况通常不需要
        logger.info("🎯 [用户上下文] 常规查询，不包含用户档案")
        return False

    def get_user_context(self, user_id: str, include_profile: bool = True) -> dict:
        """
        获取用户上下文信息

        Args:
            user_id: 用户ID
            include_profile: 是否包含完整用户档案

        Returns:
            dict: 用户上下文信息
        """
        if not include_profile:
            return {"has_user_context": False, "user_profile": None, "user_summary": None}

        # 检查缓存
        cache_key = f"user_profile_{user_id}"
        if cache_key in self._user_cache:
            user_profile = self._user_cache[cache_key]
            logger.info("🎯 [用户上下文] 使用缓存的用户档案")
        else:
            # 获取用户档案
            try:
                user_profile = self._get_user_profile(user_id)
                if user_profile:
                    self._user_cache[cache_key] = user_profile
                    logger.info("🎯 [用户上下文] 获取用户档案成功，已缓存")
                else:
                    logger.warning(f"🎯 [用户上下文] 未找到用户档案: {user_id}")
            except Exception as e:
                logger.error(f"🎯 [用户上下文] 获取用户档案失败: {e}")
                user_profile = None

        if not user_profile:
            return {"has_user_context": False, "user_profile": None, "user_summary": None}

        # 生成用户摘要
        user_summary = self._generate_user_summary(user_profile)

        return {"has_user_context": True, "user_profile": user_profile, "user_summary": user_summary}

    def _get_user_profile(self, user_id: str) -> Optional[dict]:
        """获取用户本人档案"""
        try:
            # 获取所有人物，找到用户本人
            result = get_all_persons_mysql(user_id)

            if result.get("result") != "success":
                logger.warning(f"🎯 [用户上下文] 获取人物列表失败: {result.get('reason', '未知错误')}")
                return None

            all_persons = result.get("persons", [])

            for person in all_persons:
                if person.get("is_user", False):
                    return person

            logger.warning(f"🎯 [用户上下文] 未找到用户本人档案: {user_id}")
            return None

        except Exception as e:
            logger.error(f"🎯 [用户上下文] 获取用户档案异常: {e}")
            return None

    def _generate_user_summary(self, user_profile: dict) -> str:
        """生成用户摘要信息"""
        if not user_profile:
            return ""

        name = user_profile.get("canonical_name", "用户")
        key_attrs = user_profile.get("key_attributes", {})

        summary_parts = [f"用户姓名：{name}"]

        # 提取基本信息
        if isinstance(key_attrs, dict):
            basic_info = key_attrs.get("基本信息", {})
            if isinstance(basic_info, dict):
                city = basic_info.get("当前城市", "")
                if city:
                    summary_parts.append(f"当前城市：{city}")

                family = basic_info.get("家庭情况", {})
                if isinstance(family, dict):
                    children = family.get("子女信息", [])
                    if children:
                        summary_parts.append(f"子女：{', '.join(children)}")

                    spouse = family.get("配偶姓名", "")
                    if spouse:
                        summary_parts.append(f"配偶：{spouse}")

        return "；".join(summary_parts)

    def clear_cache(self, user_id: str = None):
        """清除缓存"""
        if user_id:
            cache_key = f"user_profile_{user_id}"
            self._user_cache.pop(cache_key, None)
            logger.info(f"🎯 [用户上下文] 清除用户缓存: {user_id}")
        else:
            self._user_cache.clear()
            logger.info("🎯 [用户上下文] 清除所有用户缓存")
