"""
综合天气服务
提供基于用户社交关系和事件安排的全面天气信息和个性化提醒
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from configs.lion_config import get_value
from service.ai_client import send_to_ai
from service.ESmemory.es_event_service import get_recent_events
from service.mysql_person_service import get_person_by_id_mysql, search_persons_by_attributes_mysql
from tools.weather import get_adcode, get_weather_with_fallback
from utils.logger import logger


class ComprehensiveWeatherService:
    """综合天气服务类"""

    def __init__(self):
        self.event_index = get_value("humanrelation.event_index_name", "memory_event_store")

    def get_comprehensive_weather_info(self, user_id: str) -> Dict:
        """
        获取用户综合天气信息

        Args:
            user_id: 用户ID

        Returns:
            包含多地点天气信息和AI提醒的字典
        """
        try:
            logger.info(f"🌤️ 开始获取用户 {user_id} 的综合天气信息")

            # 1. 获取用户基本信息
            user_info = self._get_user_basic_info(user_id)
            if not user_info:
                logger.warning(f"⚠️ 用户 {user_id} 基本信息获取失败，将使用默认配置继续执行")
                user_info = {"canonical_name": "用户", "key_attributes": {}}

            # 2. 收集所有需要查询天气的地点
            locations = self._collect_all_locations(user_id, user_info)
            logger.info(f"📍 收集到 {len(locations)} 个地点需要查询天气")

            # 3. 获取所有地点的天气信息
            weather_data = self._get_weather_for_locations(locations)

            # 4. 生成AI个性化提醒
            ai_reminder = self._generate_ai_weather_reminder(user_id, user_info, weather_data, locations)

            # 5. 构建返回结果
            result = {
                "result": "success",
                "user_id": user_id,
                "timestamp": datetime.now().isoformat(),
                "weather_summary": {
                    "total_locations": len(locations),
                    "successful_queries": len([w for w in weather_data if w.get("success")]),
                    "failed_queries": len([w for w in weather_data if not w.get("success")]),
                },
                "locations": locations,
                "weather_data": weather_data,
                "ai_reminder": ai_reminder,
            }

            logger.info(f"✅ 用户 {user_id} 综合天气信息获取完成")
            return result

        except Exception as e:
            logger.error(f"❌ 获取综合天气信息失败: {str(e)}")
            return {"result": "error", "reason": str(e)}

    def _generate_personalized_reminder_for_location(self, user_id: str, location: str, weather_data: Dict) -> str:
        """
        为指定地点生成个性化天气提醒

        Args:
            user_id: 用户ID
            location: 地点名称
            weather_data: 天气数据

        Returns:
            str: 个性化天气提醒
        """
        try:
            logger.info(f"🤖 为用户 {user_id} 生成 {location} 的个性化天气提醒")

            # 获取用户信息
            user_info = self._get_user_basic_info(user_id)

            if not user_info:
                return f"{location}天气信息已获取，建议根据天气情况合理安排出行。"

            # 获取用户近期事件和提醒
            recent_events = self._get_user_recent_events_and_reminders(user_id)

            # 生成AI提醒（使用修改后的方法）
            ai_reminder = self._generate_ai_reminder_for_single_location(
                user_info, location, weather_data, recent_events
            )

            return ai_reminder

        except Exception as e:
            logger.error(f"为{location}生成个性化提醒失败: {e}")
            return f"{location}天气信息已获取，建议根据天气情况合理安排出行。"

    def _build_weather_comparison_for_location(self, location: str, weather_data: Dict) -> str:
        """为单个地点构建天气对比数据"""
        try:
            if not weather_data.get("success"):
                return f"{location}：天气数据获取失败"

            data = weather_data.get("data", {})
            temperature = data.get("temperature", "未知")
            feels_like = data.get("feelsLike", "未知")
            weather = data.get("weather", "未知")
            humidity = data.get("humidity", "未知")

            # 获取明天预报
            forecast = data.get("forecast", [])
            tomorrow_forecast = ""
            if forecast and len(forecast) > 1:
                tomorrow = forecast[1]
                temp_max = tomorrow.get("tempMax", "未知")
                temp_min = tomorrow.get("tempMin", "未知")
                text_day = tomorrow.get("textDay", "未知")
                tomorrow_forecast = f"明天预报：{temp_min}-{temp_max}°C，{text_day}"

            return f"""{location}：
- 今天：{temperature}°C（体感{feels_like}°C），{weather}，湿度{humidity}
- 昨天对比：与昨天相近
- {tomorrow_forecast}
- 空气质量：未知
- 紫外线：未知"""

        except Exception as e:
            logger.error(f"构建{location}天气对比数据失败: {e}")
            return f"{location}：天气数据处理失败"

    def _generate_ai_reminder_for_single_location(
        self, user_info: Dict, location: str, weather_data: Dict, recent_events: str
    ) -> str:
        """为单个地点生成AI天气提醒"""
        try:
            # 构建天气对比数据
            weather_comparison = self._build_weather_comparison_for_location(location, weather_data)

            # 构建用户档案
            user_profile = self._build_user_profile(user_info)

                # 获取System和User的prompt模板
            system_prompt = get_value("humanrelation.weather_system_prompt", self._get_default_weather_system_prompt())
            user_prompt_template = get_value(
                "humanrelation.weather_user_prompt", self._get_default_weather_user_prompt()
            )

            # 构建数据
            current_time = datetime.now().strftime("%Y年%m月%d日 %H:%M")

            # 构建User prompt（只包含数据）
            user_prompt = user_prompt_template.format(
                current_time=current_time,
                weather_comparison=weather_comparison,
                user_profile=user_profile,
                user_events=recent_events,
            )

            # 调用AI生成提醒
            ai_request_data = {
                "model": get_value("humanrelation.weather.ai.model", "gpt-4.1"),
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                "stream": False,
                "temperature": 0.8,
                "max_tokens": 800,
            }

            response = send_to_ai(ai_request_data)

            if response and response.status_code == 200:
                response_data = response.json()
                if response_data.get("choices"):
                    ai_reminder = response_data["choices"][0]["message"]["content"].strip()
                    logger.info(f"✅ {location}个性化天气提醒生成成功")
                    return ai_reminder
            else:
                logger.warning(f"AI生成{location}天气提醒失败")
                return f"{location}天气信息已获取，建议根据天气情况合理安排出行。"

        except Exception as e:
            logger.error(f"生成{location}AI天气提醒失败: {e}")
            return f"{location}天气信息已获取，建议根据天气情况合理安排出行。"

    def _build_user_profile(self, user_info: Dict) -> str:
        """构建用户档案字符串"""
        try:
            return self._prepare_full_user_profile_for_ai(user_info)
        except Exception as e:
            logger.error(f"构建用户档案失败: {e}")
            return "用户档案信息获取失败"

    def _get_user_basic_info(self, user_id: str) -> Optional[Dict]:
        """获取用户基本信息"""
        try:
            # 获取用户自己的档案信息（查找is_user=True的记录）
            from service.mysql_person_service import get_user_person

            logger.info(f"🔍 开始获取用户 {user_id} 的基本信息")
            user_result = get_user_person(user_id)

            if user_result:
                logger.info(f"✅ 成功获取用户 {user_id} 的基本信息: {user_result.get('canonical_name', '未知姓名')}")
                return user_result
            else:
                logger.warning(f"⚠️ 用户 {user_id} 的档案不存在 (is_user=True)")

                # 尝试确保用户档案存在
                from service.mysql_person_service import ensure_user_profile_exists

                logger.info(f"🔧 尝试为用户 {user_id} 创建档案")
                create_result = ensure_user_profile_exists(user_id)

                if create_result.get("result") == "success":
                    logger.info(f"✅ 成功创建用户 {user_id} 的档案")
                    # 重新获取用户信息
                    user_result = get_user_person(user_id)
                    if user_result:
                        logger.info(f"✅ 重新获取用户 {user_id} 信息成功")
                        return user_result
                else:
                    logger.error(f"❌ 创建用户 {user_id} 档案失败: {create_result}")

            return None
        except Exception as e:
            logger.error(f"❌ 获取用户基本信息失败: {e}")
            import traceback

            logger.error(f"详细错误: {traceback.format_exc()}")
            return None

    def _collect_all_locations(self, user_id: str, user_info: Dict) -> List[Dict]:
        """收集所有需要查询天气的地点"""
        locations = []

        # 1. 用户常用地址
        user_locations = self._extract_user_locations(user_info)
        locations.extend(user_locations)

        # 2. 用户即将发生的事件地址
        event_locations = self._get_upcoming_event_locations(user_id)
        locations.extend(event_locations)

        # 3. 直属亲属地址
        family_locations = self._get_family_locations(user_id)
        locations.extend(family_locations)

        # 4. 老板地址
        boss_locations = self._get_boss_locations(user_id)
        locations.extend(boss_locations)

        # 5. 如果没有找到任何地点，添加默认测试地点
        if not locations:
            logger.info("📍 未找到用户相关地点，添加默认测试地点")
            locations.extend(
                [
                    {
                        "name": "北京",
                        "type": "default_location",
                        "source": "默认测试地点",
                        "person_name": "系统",
                        "relationship": "测试",
                    },
                    {
                        "name": "上海",
                        "type": "default_location",
                        "source": "默认测试地点",
                        "person_name": "系统",
                        "relationship": "测试",
                    },
                ]
            )

        # 去重处理
        unique_locations = self._deduplicate_locations(locations)

        return unique_locations

    def _extract_user_locations(self, user_info: Dict) -> List[Dict]:
        """从用户信息中提取地址"""
        locations = []

        try:
            # 从标准化的用户档案中提取地址信息
            key_attributes = user_info.get("key_attributes", {})

            # 查找各种地址相关的字段
            location_fields = [
                "地点",
                "工作地点",
                "生活地点",
                "居住地点",
                "常住地址",
                "工作地址",
                "家庭地址",
                "现居地",
                "所在地",
            ]

            for field in location_fields:
                if field in key_attributes and key_attributes[field]:
                    location_name = key_attributes[field].strip()
                    if location_name and location_name not in [loc["name"] for loc in locations]:
                        locations.append(
                            {
                                "name": location_name,
                                "type": "user_location",
                                "source": f"用户{field}",
                                "person_name": user_info.get("canonical_name", "用户"),
                                "relationship": "本人",
                            }
                        )

            logger.info(f"从用户信息中提取到 {len(locations)} 个地址")

        except Exception as e:
            logger.error(f"提取用户地址失败: {e}")

        return locations

    def _get_upcoming_event_locations(self, user_id: str, days_ahead: int = 7) -> List[Dict]:
        """获取即将发生的事件地址"""
        locations = []

        try:
            # 获取未来一周的事件
            events_result = get_recent_events(self.event_index, user_id, size=50)

            if events_result.get("result") == "success":
                events = events_result.get("events", [])

                # 过滤出未来的事件（基于描述文本中的日期）
                future_events = self._filter_future_events(events, days_ahead)

                for event in future_events:
                    location = event.get("location", "").strip()
                    if location:
                        event_time = self._extract_event_time(event.get("description_text", ""))
                        locations.append(
                            {
                                "name": location,
                                "type": "event_location",
                                "source": "即将发生的事件",
                                "event_description": event.get("description_text", ""),
                                "event_time": event_time,
                                "participants": event.get("participants", []),
                            }
                        )

            logger.info(f"从即将发生的事件中提取到 {len(locations)} 个地址")

        except Exception as e:
            logger.error(f"获取事件地址失败: {e}")

        return locations

    def _get_family_locations(self, user_id: str) -> List[Dict]:
        """获取直属亲属地址"""
        locations = []

        try:
            # 从数据库中查询家庭成员信息
            from service.mysql_person_service import get_all_persons_mysql

            persons_result = get_all_persons_mysql(user_id)
            if persons_result.get("result") == "success":
                persons = persons_result.get("persons", [])

                # 筛选出直属亲属（配偶、子女、父母）
                family_relationships = [
                    "配偶",
                    "妻子",
                    "丈夫",
                    "老公",
                    "老婆",
                    "儿子",
                    "女儿",
                    "父亲",
                    "母亲",
                    "爸爸",
                    "妈妈",
                ]

                for person in persons:
                    relationship = person.get("relationship", "").strip()
                    if relationship in family_relationships:
                        # 从person的key_attributes中提取地址信息
                        key_attributes = person.get("key_attributes", {})
                        if isinstance(key_attributes, str):
                            try:
                                import json

                                key_attributes = json.loads(key_attributes)
                            except:
                                key_attributes = {}

                        # 查找地址相关字段
                        location_fields = [
                            "地点",
                            "工作地点",
                            "生活地点",
                            "居住地点",
                            "常住地址",
                            "工作地址",
                            "家庭地址",
                            "现居地",
                            "所在地",
                        ]

                        person_locations = []
                        for field in location_fields:
                            if field in key_attributes and key_attributes[field]:
                                location_name = key_attributes[field].strip()
                                if location_name and location_name not in person_locations:
                                    person_locations.append(location_name)

                        # 如果找到了地址，添加到结果中
                        for location_name in person_locations:
                            locations.append(
                                {
                                    "name": location_name,
                                    "type": "family_location",
                                    "source": "家庭成员档案",
                                    "person_name": person.get("canonical_name", ""),
                                    "relationship": relationship,
                                }
                            )

                        # 如果没有找到具体地址，但有家庭成员信息，使用关系描述
                        if not person_locations and person.get("canonical_name"):
                            locations.append(
                                {
                                    "name": f"{relationship}居住地",
                                    "type": "family_location",
                                    "source": "家庭成员档案",
                                    "person_name": person.get("canonical_name", ""),
                                    "relationship": relationship,
                                }
                            )

            logger.info(f"从数据库中查询到 {len(locations)} 个家庭成员地址")

        except Exception as e:
            logger.error(f"获取亲属地址失败: {e}")

        return locations

    def _get_boss_locations(self, user_id: str) -> List[Dict]:
        """获取老板地址"""
        locations = []

        try:
            # 从数据库中查询老板信息
            from service.mysql_person_service import get_all_persons_mysql

            persons_result = get_all_persons_mysql(user_id)
            if persons_result.get("result") == "success":
                persons = persons_result.get("persons", [])

                # 筛选出老板相关的关系
                boss_relationships = ["老板", "上司", "领导", "主管", "经理", "总监", "CEO", "CTO", "VP"]

                for person in persons:
                    relationship = person.get("relationship", "").strip()
                    if relationship in boss_relationships:
                        # 从person的key_attributes中提取地址信息
                        key_attributes = person.get("key_attributes", {})
                        if isinstance(key_attributes, str):
                            try:
                                import json

                                key_attributes = json.loads(key_attributes)
                            except json.JSONDecodeError:
                                key_attributes = {}

                        # 查找地址相关字段
                        location_fields = [
                            "地点",
                            "工作地点",
                            "生活地点",
                            "居住地点",
                            "常住地址",
                            "工作地址",
                            "家庭地址",
                            "现居地",
                            "所在地",
                            "办公地点",
                        ]

                        person_locations = []
                        for field in location_fields:
                            if field in key_attributes and key_attributes[field]:
                                location_name = key_attributes[field].strip()
                                if location_name and location_name not in person_locations:
                                    person_locations.append(location_name)

                        # 如果找到了地址，添加到结果中
                        for location_name in person_locations:
                            locations.append(
                                {
                                    "name": location_name,
                                    "type": "boss_location",
                                    "source": "老板档案",
                                    "person_name": person.get("canonical_name", ""),
                                    "relationship": relationship,
                                }
                            )

                        # 如果没有找到具体地址，但有老板信息，使用关系描述
                        if not person_locations and person.get("canonical_name"):
                            locations.append(
                                {
                                    "name": f"{relationship}办公地点",
                                    "type": "boss_location",
                                    "source": "老板档案",
                                    "person_name": person.get("canonical_name", ""),
                                    "relationship": relationship,
                                }
                            )

            logger.info(f"从数据库中查询到 {len(locations)} 个老板地址")

        except Exception as e:
            logger.error(f"获取老板地址失败: {e}")

        return locations

    def _extract_person_locations(self, person: Dict, relationship: str) -> List[Dict]:
        """从人员信息中提取地址"""
        locations = []

        try:
            key_attributes = person.get("key_attributes", {})
            person_name = person.get("canonical_name", "未知")

            # 查找地址相关字段
            location_fields = ["地点", "工作地点", "生活地点", "居住地点", "所在地"]

            for field in location_fields:
                if field in key_attributes and key_attributes[field]:
                    location_name = key_attributes[field].strip()
                    if location_name:
                        locations.append(
                            {
                                "name": location_name,
                                "type": "person_location",
                                "source": f"{relationship}地址",
                                "person_name": person_name,
                                "relationship": relationship,
                            }
                        )

        except Exception as e:
            logger.error(f"提取人员地址失败: {e}")

        return locations

    def _filter_future_events(self, events: List[Dict], days_ahead: int) -> List[Dict]:
        """过滤出未来的事件"""
        future_events = []
        today = datetime.now().date()
        future_date = today + timedelta(days=days_ahead)

        for event in events:
            description = event.get("description_text", "")
            event_date = self._extract_date_from_description(description)

            if event_date and today <= event_date <= future_date:
                future_events.append(event)

        return future_events

    def _extract_date_from_description(self, description: str) -> Optional[datetime.date]:
        """从事件描述中提取日期"""
        try:
            import re

            # 匹配 YYYY-MM-DD 格式的日期
            date_pattern = r"(\d{4}-\d{2}-\d{2})"
            match = re.search(date_pattern, description)

            if match:
                date_str = match.group(1)
                return datetime.strptime(date_str, "%Y-%m-%d").date()

        except Exception as e:
            logger.debug(f"提取日期失败: {e}")

        return None

    def _extract_event_time(self, description: str) -> str:
        """从事件描述中提取时间信息"""
        try:
            import re

            # 匹配日期时间模式
            patterns = [
                r"(\d{4}-\d{2}-\d{2})",  # YYYY-MM-DD
                r"(\d{1,2}:\d{2})",  # HH:MM
                r"(明天|后天|下周|本周|下个月)",  # 相对时间
            ]

            for pattern in patterns:
                match = re.search(pattern, description)
                if match:
                    return match.group(1)

        except Exception as e:
            logger.debug(f"提取事件时间失败: {e}")

        return ""

    def _deduplicate_locations(self, locations: List[Dict]) -> List[Dict]:
        """去重地点列表"""
        seen_names = set()
        unique_locations = []

        for location in locations:
            name = location["name"]
            if name not in seen_names:
                seen_names.add(name)
                unique_locations.append(location)

        return unique_locations

    def _get_weather_for_locations(self, locations: List[Dict]) -> List[Dict]:
        """获取所有地点的天气信息"""
        weather_data = []

        for location in locations:
            try:
                location_name = location["name"]
                logger.info(f"🌤️ 正在获取 {location_name} 的天气信息")

                # 获取地点的adcode
                adcode = get_adcode(location_name)

                if adcode:
                    # 使用备选方案获取天气信息
                    weather_result = self._get_weather_with_fallback(adcode, location_name)

                    if weather_result.get("success"):
                        weather_info = weather_result.get("weather_info", {})
                        logger.info(f"🌤️ {location_name} 天气数据结构: {weather_info}")
                        weather_data.append(
                            {
                                "location": location,
                                "weather": weather_info,
                                "success": True,
                                "adcode": adcode,
                                "source": weather_result.get("source", "unknown"),
                            }
                        )

                        logger.info(f"✅ {location_name} 天气获取成功 (来源: {weather_result.get('source')})")
                    else:
                        weather_data.append(
                            {
                                "location": location,
                                "error": weather_result.get("error", "天气获取失败"),
                                "success": False,
                            }
                        )
                        logger.warning(f"⚠️ {location_name} 天气获取失败: {weather_result.get('error')}")
                else:
                    weather_data.append({"location": location, "error": "地点解析失败", "success": False})
                    logger.warning(f"⚠️ {location_name} 地点解析失败")

            except Exception as e:
                logger.error(f"❌ 获取 {location.get('name', '未知地点')} 天气失败: {e}")
                weather_data.append({"location": location, "error": str(e), "success": False})

        return weather_data

    def _get_weather_with_fallback(self, adcode: str, location_name: str) -> Dict:
        """
        使用备选方案获取天气信息（调用统一接口）
        优先使用和风天气API，失败时使用原有API

        Args:
            adcode: 行政区划代码
            location_name: 地点名称

        Returns:
            天气信息字典
        """
        # 使用统一的天气获取接口
        result = get_weather_with_fallback(adcode, location_name, return_format="dict")

        if result.get("success"):
            return {"success": True, "weather_info": result["data"], "source": result["source"]}
        else:
            return {"success": False, "error": result.get("error", f"获取{location_name}天气信息失败")}

    def _generate_ai_weather_reminder(
        self, user_id: str, user_info: Dict, weather_data: List[Dict], locations: List[Dict]
    ) -> str:
        """生成AI个性化天气提醒"""
        try:
            logger.info(f"🤖 开始为用户 {user_id} 生成AI天气提醒")
            logger.info(f"📥 输入参数 - user_info: {user_info}")
            logger.info(f"📥 输入参数 - weather_data数量: {len(weather_data) if weather_data else 0}")
            logger.info(f"📥 输入参数 - locations数量: {len(locations) if locations else 0}")

            # 获取用户近期事件和提醒信息
            user_events = self._get_user_recent_events_and_reminders(user_id)
            logger.info(f"📅 获取到用户近期事件: {user_events}")

            # 获取System和User的prompt模板
            system_prompt = get_value("humanrelation.weather_system_prompt", self._get_default_weather_system_prompt())
            user_prompt_template = get_value(
                "humanrelation.weather_user_prompt", self._get_default_weather_user_prompt()
            )
            logger.info(f"📝 System prompt长度: {len(system_prompt)}")
            logger.info(f"📝 User prompt模板长度: {len(user_prompt_template)}")

            # 构建多城市天气对比数据
            weather_comparison = self._build_comprehensive_weather_comparison(weather_data, locations)
            logger.info(f"🌤️ 天气对比数据: {weather_comparison}")

            # 准备完整的用户档案信息
            full_user_profile = self._prepare_full_user_profile_for_ai(user_info)
            logger.info(f"👤 完整用户档案: {full_user_profile}")

            # 获取当前时间
            from datetime import datetime

            current_time = datetime.now().strftime("%Y年%m月%d日 %H:%M")
            logger.info(f"⏰ 当前时间: {current_time}")

            # 构建User prompt（只包含数据）
            user_prompt = user_prompt_template.format(
                current_time=current_time,
                weather_comparison=weather_comparison,
                user_profile=full_user_profile,
                user_events=user_events or "用户暂无近期事件和提醒记录",
            )
            logger.info(f"🤖 最终User prompt长度: {len(user_prompt)}")
            logger.info(f"🤖 User prompt内容: {user_prompt[:500]}...")  # 只显示前500字符

            logger.info(f"生成综合天气对比提醒，用户: {user_info.get('canonical_name', '未知')}")

            # 调用AI生成提醒
            query_input = {
                "model": get_value("humanrelation.weather.ai.model", "gpt-4.1"),
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                "stream": False,
                "temperature": 0.8,
                "max_tokens": 800,
            }

            logger.info(f"🚀 发送AI请求: {query_input}")
            response = send_to_ai(query_input)
            logger.info(f"📨 AI原始响应: {response}")

            if response and response.text:
                logger.info(f"📨 AI响应文本: {response.text}")
                try:
                    response_data = json.loads(response.text)
                    logger.info(f"📨 AI响应数据: {response_data}")

                    if "choices" in response_data and response_data["choices"]:
                        ai_reminder = response_data["choices"][0]["message"]["content"].strip()
                        logger.info(f"🎯 提取的AI提醒: {ai_reminder}")

                        logger.info("✅ AI综合天气提醒生成成功")
                        return ai_reminder
                    else:
                        logger.error(f"❌ AI响应格式错误，缺少choices字段: {response_data}")
                        return self._generate_basic_weather_reminder(weather_data)
                except json.JSONDecodeError as e:
                    logger.error(f"❌ AI响应JSON解析失败: {e}, 原始响应: {response.text}")
                    return self._generate_basic_weather_reminder(weather_data)
            else:
                logger.warning("⚠️ AI服务调用失败，使用基础提醒")
                return self._generate_basic_weather_reminder(weather_data)

        except Exception as e:
            logger.error(f"❌ 生成AI天气提醒失败: {e}")
            return self._generate_basic_weather_reminder(weather_data)

    def _get_user_recent_events_and_reminders(self, user_id: str) -> str:
        """
        获取用户近期事件和提醒，直接返回原始数据给AI分析
        """
        try:
            from my_mysql.entity.reminders import query_reminders_by_user

            events_info = []

            # 1. 获取用户提醒事项
            try:
                reminders = query_reminders_by_user(user_id)
                if reminders:
                    events_info.append("=== 用户提醒事项 ===")
                    for reminder in reminders:
                        try:
                            # 处理不同的数据格式
                            if hasattr(reminder, "_asdict"):
                                reminder_dict = reminder._asdict()
                            elif hasattr(reminder, "__dict__"):
                                reminder_dict = reminder.__dict__
                            elif isinstance(reminder, dict):
                                reminder_dict = reminder
                            else:
                                continue

                            status = reminder_dict.get("status", "")
                            next_trigger_time = reminder_dict.get("next_trigger_time")
                            content = reminder_dict.get("reminder_text_template", "")

                            if next_trigger_time and content:
                                # 格式化时间
                                if isinstance(next_trigger_time, str):
                                    time_str = next_trigger_time
                                else:
                                    time_str = next_trigger_time.strftime("%Y-%m-%d %H:%M")

                                events_info.append(f"- [{status}] {time_str}: {content}")
                        except Exception as e:
                            logger.error(f"处理提醒记录失败: {e}")
                            continue
            except Exception as e:
                logger.error(f"获取用户提醒失败: {e}")

            # 2. 获取用户近期事件记忆
            try:
                recent_events_result = get_recent_events(self.event_index, user_id, size=10)  # 获取最近10条事件

                if recent_events_result.get("result") == "success" and recent_events_result.get("events"):
                    events_info.append("\n=== 用户近期事件 ===")
                    for event in recent_events_result["events"]:
                        timestamp = event.get("timestamp", "")
                        description = event.get("description_text", "")
                        location = event.get("location", "")
                        participants = event.get("participants", [])

                        if description:
                            event_text = f"- {timestamp}: {description}"
                            if location:
                                event_text += f" (地点: {location})"
                            if participants:
                                event_text += f" (参与者: {', '.join(participants)})"
                            events_info.append(event_text)
            except Exception as e:
                logger.error(f"获取用户近期事件失败: {e}")

            # 3. 返回原始数据给AI分析
            if events_info:
                result = "\n".join(events_info)
                logger.info(f"获取用户{user_id}近期事件和提醒，共{len(events_info)}条信息")
                return result
            else:
                return "用户暂无近期事件和提醒记录"

        except Exception as e:
            logger.error(f"获取用户近期事件和提醒失败: {e}")
            return "无法获取用户近期事件信息"

    def _get_default_weather_system_prompt(self) -> str:
        """获取默认的天气System Prompt"""
        return """你是一个顶级的气象分析师，同时也是一个充满关怀的生活伙伴。你的任务是根据朋友的个人情况（备忘录、健康状况、出行计划）和详细的天气数据，为他们提供一份内容精干、数据翔实、充满人文关怀的个性化天气解读。

【核心原则】：
1. **深度个性化**: 你的每一条提醒都必须与用户的备忘录紧密结合。明确指出天气对"谁"（父母、孩子、本人）、"什么事"（出差、旅游、关节炎）的具体影响。
2. **数据驱动**: 当天气出现异常时（如高温、暴雨、大风、降温），必须提供具体数据支撑，但要用通俗易懂的方式表达。
3. **主动关怀**: 你的语气应像一个体贴的朋友，温暖、主动、有同理心。
4. **要事优先**: 先说最重要的城市，例如用户当前所在地，根据备忘录信息判断用户在哪里，并且结合其他人所在地有无紧急情况。例如大家所在地都正常，就先说自己。如果自己所在地正常，父母所在地要大暴雨了，那就先说父母所在地，再说自己所在地。以上是举例子，你自己思考一下顺序。
5. **交通方式**: 注意用户是否开车、骑车、步行，对于天气的感受不一样，例如开车怕冰雹不怕普通的雨水；以及航班、高铁出行，对待天气的敏感度也不一样。

【通俗化表达指南】：
- **降雨强度**: 例如不要说"50毫米/小时"，改为"下雨强度超过99%的情况"、"暴雨级别"、"像倒水一样的大雨"这种类型的说法
- **风力**: 例如不要只说"8级风"，改为"能吹倒树枝的强风"、"走路都困难的大风"、"相当于台风边缘的风力"这种类型的说法
- **温度**: 例如不要只说数字，加上感受描述，如"热得像蒸桑拿"、"冷得需要穿羽绒服"、"体感像发烧一样难受"这种类型的说法
- **湿度**: 例如不要只说"98%"，改为"空气湿得像在浴室里"、"衣服晾不干的潮湿"这种类型的说法
- **能见度**: 例如不要说"200米"，改为"只能看清一个路口的距离"、"雾大得看不清对面楼"这种类型的说法
- **空气质量**: 例如不要只说AQI数值，改为"空气脏得像吸烟"、"出门必须戴口罩的污染程度"这种类型的说法

【输出要求】：
- **内容加倍**: 每个城市可以输出100字以内，要选最重要的事情说，先说一句话最关键的信息再展开。
- **关键数据**: 可以主动识别并透露异常或关键天气数据，没有异常就可以略过，但必须用通俗易懂的方式表达：
    - **预警**: 例如暴雨、山洪、台风、高温、低温、大风、沙尘、雷电、冰雹、寒潮、霜冻、大雾、霾、干旱、森林火灾、地质灾害预警等，如果有要优先解读。
    - **气温**: 异常高/低温、24小时内剧烈温差，用生活化比喻描述。
    - **降水**: 明确预警等级、开始和结束时间，用通俗比喻描述强度。
    - **风力**: 用生活场景描述风力影响，而不是只说等级。
    - **空气质量**: 用健康影响和生活感受描述，而不是只说数值。
- **精准时间**: 所有预警和天气变化，必须明确时间点（如"今天傍晚到夜间"、"明天凌晨"），严格杜绝"未来几天"等模糊表述。
- **语气自然**: 适当使用emoji表情符号，让解读更生动、更亲切，但是不要使用markdown符号。
- **安全第一**: 对于恶劣天气（如暴雨、雷电、地质灾害预警），语气应更严肃，明确提醒风险和规避措施。"""

    def _get_default_weather_user_prompt(self) -> str:
        """获取默认的天气User Prompt模板"""
        return """当前时间：{current_time}

天气数据：
{weather_comparison}

用户档案：
{user_profile}

近期安排：
{user_events}"""

    def _build_comprehensive_weather_comparison(self, weather_data: List[Dict], locations: List[Dict]) -> str:
        """
        构建多城市天气对比数据
        """
        try:
            comparison_data = []

            for i, location in enumerate(locations):
                if i < len(weather_data) and weather_data[i].get("success"):
                    weather_info = weather_data[i]["weather"]  # 修复：使用正确的键名
                    city = location.get("name", "未知城市")  # 修复：使用正确的键名
                    location_type = location.get("type", "未知")
                    relationship = location.get("relationship", "")

                    # 提取当前天气信息
                    current_temp = weather_info.get("temperature", "未知")
                    current_feels_like = weather_info.get("feelsLike", current_temp)
                    current_weather = weather_info.get("weather", "未知")
                    current_humidity = weather_info.get("humidity", "未知")

                    # 提取预报数据进行对比分析
                    forecast = weather_info.get("forecast", [])
                    yesterday_analysis = self._analyze_yesterday_comparison(forecast, current_temp)
                    tomorrow_forecast = self._analyze_tomorrow_forecast(forecast)

                    # 构建该城市的天气变化描述
                    city_comparison = f"""{city}（{location_type}）：
- 今天：{current_temp}°C（体感{current_feels_like}°C），{current_weather}，湿度{current_humidity}%
- 昨天对比：{yesterday_analysis}
- 明天预报：{tomorrow_forecast}
- 空气质量：{weather_info.get('air_quality', '未知')}
- 紫外线：{weather_info.get('uv_index', '未知')}"""

                    if relationship:
                        city_comparison += f"\n- 关系：{relationship}"

                    comparison_data.append(city_comparison)

            return "\n\n".join(comparison_data)

        except Exception as e:
            logger.error(f"构建天气对比数据失败: {e}")
            return "天气对比数据构建失败"

    def _analyze_yesterday_comparison(self, forecast: list, current_temp: str) -> str:
        """
        分析昨天与今天的温度对比
        """
        try:
            if not forecast or len(forecast) < 2:
                return "无昨日对比数据"

            # 简化的温度变化分析
            if isinstance(current_temp, str) and current_temp.isdigit():
                current_temp_int = int(current_temp)
                # 简化处理，实际应该从历史数据获取
                if current_temp_int > 30:
                    return "比昨天更热"
                elif current_temp_int < 15:
                    return "比昨天更冷"
                else:
                    return "与昨天相近"

            return "温度变化待分析"

        except Exception as e:
            logger.error(f"分析昨天温度对比失败: {e}")
            return "昨日对比分析失败"

    def _analyze_tomorrow_forecast(self, forecast: list) -> str:
        """
        分析明天的天气预报
        """
        try:
            if not forecast or len(forecast) < 2:
                return "无明日预报数据"

            # 获取明天的预报数据
            tomorrow = forecast[1] if len(forecast) > 1 else forecast[0]

            tomorrow_temp_high = tomorrow.get("tempMax", "未知")
            tomorrow_temp_low = tomorrow.get("tempMin", "未知")
            tomorrow_weather = tomorrow.get("textDay", "未知")

            return f"{tomorrow_temp_low}-{tomorrow_temp_high}°C，{tomorrow_weather}"

        except Exception as e:
            logger.error(f"分析明天预报失败: {e}")
            return "明日预报分析失败"

    def _prepare_full_user_profile_for_ai(self, user_info: Dict) -> str:
        """
        准备完整的用户档案信息给AI分析
        """
        try:
            import json

            profile = {}

            # 基本信息
            profile["姓名"] = user_info.get("canonical_name", "未知")
            profile["别名"] = user_info.get("aliases", [])

            # 解析key_attributes
            key_attributes = user_info.get("key_attributes", {})
            if isinstance(key_attributes, str):
                try:
                    key_attributes = json.loads(key_attributes)
                except:
                    key_attributes = {}

            # 提取基本信息
            basic_info = key_attributes.get("基本信息", {})
            if basic_info:
                profile["性别"] = basic_info.get("性别", "未知")
                profile["生日"] = basic_info.get("生日", "未知")
                profile["当前城市"] = basic_info.get("当前城市", "未知")
                profile["家乡"] = basic_info.get("家乡", "未知")

                # 家庭情况
                family_info = basic_info.get("家庭情况", {})
                if family_info:
                    profile["婚育状况"] = family_info.get("婚育状况", "")
                    profile["配偶姓名"] = family_info.get("配偶姓名", "")
                    profile["子女信息"] = family_info.get("子女信息", [])

                # 职业信息
                job_info = basic_info.get("职业信息", {})
                if job_info:
                    profile["公司"] = job_info.get("公司", "")
                    profile["职位"] = job_info.get("职位", "")
                    profile["行业"] = job_info.get("行业", "")

            # 兴趣爱好和关心话题
            profile["兴趣"] = key_attributes.get("兴趣", "")
            profile["关心话题"] = key_attributes.get("关心话题", "")
            profile["餐饮偏好"] = key_attributes.get("餐饮偏好", "")
            profile["旅游历史"] = key_attributes.get("旅游历史", "")
            profile["过往历史"] = key_attributes.get("过往历史", "")

            # 计算年龄
            birth_year_str = profile.get("生日", "")
            if birth_year_str and "年" in birth_year_str:
                try:
                    birth_year = int(birth_year_str.replace("年", ""))
                    current_year = datetime.now().year
                    profile["age"] = str(current_year - birth_year)
                except:
                    pass

            # 关系信息
            relationships = user_info.get("relationships", [])
            if relationships:
                profile["社交关系"] = relationships

            # 档案摘要
            profile_summary = user_info.get("profile_summary", "")
            if profile_summary:
                profile["档案摘要"] = profile_summary

            # 格式化输出
            profile_text = []
            for key, value in profile.items():
                if value and value != "未知" and value != "":
                    if isinstance(value, list) and len(value) > 0:
                        profile_text.append(f"{key}: {', '.join(map(str, value))}")
                    elif not isinstance(value, list):
                        profile_text.append(f"{key}: {value}")

            return "\n".join(profile_text) if profile_text else "用户档案信息不完整"

        except Exception as e:
            logger.error(f"准备用户档案失败: {e}")
            return "用户档案信息获取失败"

    def _prepare_user_profile_for_ai(self, user_info: Dict) -> Dict:
        """为AI准备用户档案信息"""
        try:
            key_attributes = user_info.get("key_attributes", {})

            return {
                "name": user_info.get("canonical_name", ""),
                "age": key_attributes.get("年龄", ""),
                "occupation": key_attributes.get("职业", ""),
                "company": key_attributes.get("公司", ""),
                "interests": key_attributes.get("兴趣爱好", ""),
                "health": key_attributes.get("健康状况", ""),
            }
        except Exception as e:
            logger.error(f"准备用户档案失败: {e}")
            return {}

    def _prepare_weather_summary_for_ai(self, weather_data: List[Dict]) -> List[Dict]:
        """为AI准备天气数据摘要"""
        summary = []

        for data in weather_data:
            if data.get("success"):
                location = data["location"]
                weather = data["weather"]

                # 提取关键天气信息
                weather_info = {
                    "location_name": location["name"],
                    "location_type": location["type"],
                    "temperature": weather.get("temperature", ""),
                    "feels_like": weather.get("feelsLike", ""),  # 体感温度
                    "weather_desc": weather.get("weather", ""),
                    "humidity": weather.get("humidity", ""),
                    "wind": weather.get("windDirection", "") + weather.get("windPower", ""),
                    "aqi": weather.get("aqi", ""),
                    "uv_index": weather.get("uvIndex", ""),
                }

                summary.append(weather_info)

        return summary

    def _prepare_location_relationships_for_ai(self, locations: List[Dict], weather_data: List[Dict]) -> List[Dict]:
        """为AI准备地点关系信息"""
        relationships = []

        # 创建地点名称到天气数据的映射
        weather_map = {}
        for data in weather_data:
            if data.get("success"):
                location_name = data["location"]["name"]
                weather_map[location_name] = data["weather"]

        for location in locations:
            location_name = location["name"]
            relationship_info = {
                "location": location_name,
                "type": location["type"],
                "source": location["source"],
                "person_name": location.get("person_name", ""),
                "relationship": location.get("relationship", ""),
                "has_weather": location_name in weather_map,
            }

            # 添加事件相关信息
            if location["type"] == "event_location":
                relationship_info["event_description"] = location.get("event_description", "")
                relationship_info["event_time"] = location.get("event_time", "")

            relationships.append(relationship_info)

        return relationships

    def _generate_basic_weather_reminder(self, weather_data: List[Dict]) -> str:
        """生成基础天气提醒（AI失败时的备选方案）"""
        try:
            successful_weather = [data for data in weather_data if data.get("success")]

            if not successful_weather:
                return "抱歉，暂时无法获取天气信息，请稍后再试。"

            # 简单的基础提醒
            location_count = len(successful_weather)
            first_location = successful_weather[0]
            weather = first_location["weather"]
            location_name = first_location["location"]["name"]

            feels_like = weather.get("feelsLike", weather.get("temperature", ""))
            weather_desc = weather.get("weather", "")

            reminder = f"今天{location_name}{weather_desc}，体感温度{feels_like}°C"

            if location_count > 1:
                reminder += f"。已为您查询了{location_count}个相关地点的天气，"

            reminder += "。请根据天气情况合理安排出行，注意保暖或防晒。"

            return reminder

        except Exception as e:
            logger.error(f"生成基础天气提醒失败: {e}")
            return "天气信息获取完成，请注意天气变化，合理安排出行。"

    def _parse_weather_string(self, weather_text: str) -> Dict:
        """解析天气信息字符串，提取关键信息"""
        try:
            import re

            weather_info = {}

            # 提取温度信息
            temp_match = re.search(r"实际温度：(\d+)°C", weather_text)
            if temp_match:
                weather_info["temperature"] = temp_match.group(1)

            # 提取体感温度
            feels_like_match = re.search(r"体感温度：(\d+)°C", weather_text)
            if feels_like_match:
                weather_info["feelsLike"] = feels_like_match.group(1)

            # 提取天气状况
            weather_match = re.search(r"天气状况：(.+)", weather_text)
            if weather_match:
                weather_info["weather"] = weather_match.group(1)

            # 提取湿度
            humidity_match = re.search(r"湿度：(\d+)%", weather_text)
            if humidity_match:
                weather_info["humidity"] = humidity_match.group(1) + "%"

            # 提取风向风速
            wind_match = re.search(r"风向风速：(.+?) (\d+\.?\d*)m/s \((\d+)级\)", weather_text)
            if wind_match:
                weather_info["windDirection"] = wind_match.group(1)
                weather_info["windSpeed"] = wind_match.group(2) + "m/s"
                weather_info["windPower"] = wind_match.group(3) + "级"

            # 提取紫外线指数
            uv_match = re.search(r"紫外线指数：(.+)", weather_text)
            if uv_match:
                weather_info["uvIndex"] = uv_match.group(1)

            # 提取气压
            pressure_match = re.search(r"气压：(\d+)hPa", weather_text)
            if pressure_match:
                weather_info["pressure"] = pressure_match.group(1) + "hPa"

            # 如果没有提取到关键信息，至少保留原始文本
            if not weather_info:
                weather_info["description"] = weather_text

            return weather_info

        except Exception as e:
            logger.error(f"解析天气字符串失败: {e}")
            return {"description": weather_text}
