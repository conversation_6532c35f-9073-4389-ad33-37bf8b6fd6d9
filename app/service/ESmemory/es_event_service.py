from datetime import datetime
from uuid import uuid4

from service.ESmemory.es_memory_client import client
from utils.logger import logger


def add_event(
    index_name: str,
    user_id: str,
    description_text: str = "",
    participants: list = [],
    location: str = "",
    topics: list = [],
    sentiment: str = "",
    conversation_id: str = None,
    **kwargs,
):  # 添加事件
    event_id = str(uuid4())
    document = {
        "memory_type": "event",  # 添加记忆类型标识
        "event_id": event_id,
        "user_id": user_id,
        "description_text": description_text,
        "timestamp": datetime.now(),
        "participants": participants,
        "location": location,
        "topics": topics,
        "sentiment": sentiment,
        "conversation_id": conversation_id,
        "created_in_conversation": conversation_id is not None,
    }
    try:
        # 使用OpenSearch的index方法
        client.index(index=index_name, id=event_id, body=document)
        logger.info(f"添加事件成功: {event_id}")
        logger.info(f"事件详细内容: {document}")
        return {"result": "success", "event_id": event_id}
    except Exception as e:
        logger.error(f"添加事件到ES失败: {e}")
        return {"result": "error", "reason": str(e)}


def get_events_by_conversation(index_name: str, user_id: str, conversation_id: str):
    """获取指定对话中新增的事件"""
    try:
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"user_id": user_id}},
                        {"term": {"conversation_id": conversation_id}},
                        {"term": {"created_in_conversation": True}}
                    ]
                }
            },
            "sort": [{"timestamp": {"order": "desc"}}]
        }

        logger.info(f"查询事件的条件: user_id={user_id}, conversation_id={conversation_id}")
        logger.info(f"查询语句: {query}")

        response = client.search(index=index_name, body=query)
        logger.info(f"ES查询响应: {response}")

        events = []

        for hit in response["hits"]["hits"]:
            event_data = hit["_source"]
            events.append({
                "event_id": event_data.get("event_id"),
                "description_text": event_data.get("description_text", ""),
                "participants": event_data.get("participants", []),
                "location": event_data.get("location", ""),
                "topics": event_data.get("topics", []),
                "sentiment": event_data.get("sentiment", ""),
                "timestamp": event_data.get("timestamp")
            })

        logger.info(f"获取对话 {conversation_id} 的事件成功，共 {len(events)} 个")
        return events

    except Exception as e:
        logger.error(f"获取对话事件失败: {str(e)}")
        return []


def search_events_by_text(index_name: str, user_id: str, query_text: str, max_results: int = 5):  # 按文本搜索事件
    """使用 bool 查询结合 simple_query_string 和 filter 实现用户隔离的模糊搜索"""
    try:
        query = {
            "bool": {
                "must": {
                    "simple_query_string": {
                        "query": query_text,
                        "fields": ["description_text", "participants", "location", "topics"],
                        "default_operator": "OR",
                    }
                },
                "filter": [
                    {"term": {"user_id": user_id}},
                    # 只搜索事件类型的记忆，兼容没有memory_type字段的旧数据
                    {
                        "bool": {
                            "should": [
                                {"term": {"memory_type": "event"}},
                                {"bool": {"must_not": {"exists": {"field": "memory_type"}}}},
                            ]
                        }
                    },
                ],
            }
        }
        logger.info(f"ES事件搜索查询: {query}")
        response = client.search(index=index_name, body={"query": query, "size": max_results})

        hits = response.get("hits", {}).get("hits", [])
        logger.info(f"ES事件搜索原始结果: {hits}")  # 临时启用调试

        events = []
        for hit in hits:
            event_data = hit.get("_source", {})
            event_data["score"] = hit.get("_score")
            events.append(event_data)

        return {"result": "success", "events": events}
    except Exception as e:
        logger.error(f"从ES搜索事件失败: {e}")
        return {"result": "error", "reason": str(e), "events": []}


def search_events_by_participant(
    index_name: str, user_id: str, participant_id: str, size: int = 10
):  # 按参与者搜索事件
    try:
        search_body = {
            "size": size,
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"user_id": user_id}},
                        {"term": {"participants": participant_id}},
                        # 只搜索事件类型的记忆
                        {
                            "bool": {
                                "should": [
                                    {"term": {"memory_type": "event"}},
                                    {"bool": {"must_not": {"exists": {"field": "memory_type"}}}},
                                ]
                            }
                        },
                    ]
                }
            },
        }
        response = client.search(index=index_name, body=search_body)
        events = [hit["_source"] for hit in response["hits"]["hits"]]
        return {"result": "success", "events": events}
    except Exception as e:
        logger.error(f"按参与者搜索事件失败: {str(e)}")
        return {"result": "error", "reason": str(e), "events": []}


def get_recent_events(index_name: str, user_id: str, size: int = 50):  # 获取最近的事件
    """获取指定用户最近的事件，按时间戳降序排序"""
    try:
        search_body = {
            "size": size,
            "sort": [{"timestamp": {"order": "desc"}}],
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"user_id": user_id}},
                        # 只搜索事件类型的记忆
                        {
                            "bool": {
                                "should": [
                                    {"term": {"memory_type": "event"}},
                                    {"bool": {"must_not": {"exists": {"field": "memory_type"}}}},
                                ]
                            }
                        },
                    ]
                }
            },
        }
        response = client.search(index=index_name, body=search_body)
        events = [hit["_source"] for hit in response["hits"]["hits"]]
        return {"result": "success", "events": events}
    except Exception as e:
        logger.error(f"获取最近事件失败: {str(e)}")
        return {"result": "error", "reason": str(e), "events": []}


def get_event_by_id(index_name: str, user_id: str, event_id: str):  # 根据ID获取事件详情
    try:
        query = {
            "bool": {
                "filter": [
                    {"term": {"user_id": user_id}},
                    {"term": {"event_id": event_id}},
                    # 只搜索事件类型的记忆
                    {
                        "bool": {
                            "should": [
                                {"term": {"memory_type": "event"}},
                                {"bool": {"must_not": {"exists": {"field": "memory_type"}}}},
                            ]
                        }
                    },
                ]
            }
        }
        response = client.search(index=index_name, body={"query": query, "size": 1})
        hits = response.get("hits", {}).get("hits", [])
        if hits:
            return {"result": "success", "event": hits[0]["_source"]}
        return {"result": "error", "reason": "事件不存在或无权限访问", "event": None}
    except Exception as e:
        logger.error(f"获取事件详情失败: {str(e)}")
        return {"result": "error", "reason": str(e), "event": None}


def update_participant_in_events(
    index_name: str, user_id: str, old_participant_id: str, new_participant_id: str
):  # 更新事件中的参与者ID
    """将一个参与者ID更新为另一个，用于档案合并，确保在用户范围内操作"""
    try:
        script = {
            "source": "if(ctx._source.participants.indexOf(params.old_id)!=-1){ctx._source.participants.remove(ctx._source.participants.indexOf(params.old_id));if(ctx._source.participants.indexOf(params.new_id)==-1){ctx._source.participants.add(params.new_id)}}",
            "lang": "painless",
            "params": {"old_id": old_participant_id, "new_id": new_participant_id},
        }
        query = {"bool": {"filter": [{"term": {"user_id": user_id}}, {"term": {"participants": old_participant_id}}]}}
        response = client.update_by_query(index=index_name, body={"script": script, "query": query})
        logger.info(f"更新事件参与者成功: {old_participant_id} -> {new_participant_id}, 响应: {response}")
        return {"result": "success", "updated_count": response.get("updated")}
    except Exception as e:
        logger.error(f"更新事件参与者失败: {str(e)}")
        return {"result": "error", "reason": str(e)}


def update_event_content(index_name: str, user_id: str, event_id: str, **kwargs):
    """更新事件内容，支持更新描述、参与者、地点、主题、情感等字段"""
    try:
        # 构建更新文档
        update_doc = {}
        allowed_fields = ["description_text", "participants", "location", "topics", "sentiment"]

        for field, value in kwargs.items():
            if field in allowed_fields:
                update_doc[field] = value

        if not update_doc:
            return {"result": "error", "reason": "没有提供有效的更新字段"}

        # 添加更新时间戳
        update_doc["updated_at"] = datetime.now()

        # 构建更新请求
        update_body = {"doc": update_doc, "doc_as_upsert": False}  # 如果文档不存在，不创建新文档

        # 先检查事件是否存在且属于该用户
        check_result = get_event_by_id(index_name, user_id, event_id)
        if check_result.get("result") != "success":
            return {"result": "error", "reason": "事件不存在或无权限访问"}

        # 执行更新
        client.update(index=index_name, id=event_id, body=update_body)

        logger.info(f"更新事件成功: {event_id}, 更新字段: {list(update_doc.keys())}")
        return {"result": "success", "event_id": event_id, "updated_fields": list(update_doc.keys())}

    except Exception as e:
        logger.error(f"更新事件内容失败: {str(e)}")
        return {"result": "error", "reason": str(e)}


def delete_event(index_name: str, user_id: str, event_id: str):
    """删除指定用户的事件，确保用户只能删除自己的事件"""
    try:
        # 先检查事件是否存在且属于该用户
        check_result = get_event_by_id(index_name, user_id, event_id)
        if check_result.get("result") != "success":
            return {"result": "error", "reason": "事件不存在或无权限删除"}

        # 执行删除操作
        client.delete(index=index_name, id=event_id)

        logger.info(f"用户 {user_id} 成功删除事件: {event_id}")
        return {"result": "success", "event_id": event_id}

    except Exception as e:
        logger.error(f"删除事件失败: {str(e)}")
        return {"result": "error", "reason": str(e)}


def get_weather(location: str):
    """获取指定地点的当前天气信息"""
    try:
        from datetime import datetime

        from tools.weather import get_adcode, get_time_interval, get_weather_info_by_adcode

        # 获取当前日期，格式为 YYYY-MM-DD
        current_date = datetime.now().strftime("%Y-%m-%d")

        # 直接调用底层函数，避免LangChain工具调用问题
        adcode = get_adcode(location)
        if adcode is not None:
            time_interval = get_time_interval(current_date)
            weather_info = get_weather_info_by_adcode(adcode, time_interval, current_date, location)

            # 如果天气服务不可用，提供友好的回复
            if "天气服务暂时不可用" in weather_info or "HTTP状态码" in weather_info:
                return {
                    "result": "error",
                    "reason": f"抱歉，天气服务暂时不可用，无法获取{location}的天气信息。请稍后再试。",
                }

            return {"result": "success", "weather_info": weather_info}
        else:
            return {"result": "error", "reason": f"无法获取{location}的地理位置信息，请检查地点名称是否正确"}

    except Exception as e:
        logger.error(f"获取天气信息失败: {str(e)}")
        return {"result": "error", "reason": f"获取天气信息失败: {str(e)}"}


def get_weather_by_person(user_id: str, person_id: str):
    """根据指定人员信息中的地点获取天气"""
    try:
        import json
        from datetime import datetime

        from service.mysql_person_service import get_person_by_id_mysql
        from tools.weather import get_adcode, get_weather_with_fallback

        # 获取指定人员档案（包含用户权限验证）
        person_result = get_person_by_id_mysql(user_id, person_id)
        if person_result.get("result") != "success" or not person_result.get("person"):
            return {"result": "error", "reason": "人员档案不存在或无权限访问"}

        person_data = person_result["person"]

        # 从用户档案中提取地点信息
        locations = {}  # 使用字典存储多个地点，键为地点类型，值为城市名

        # 尝试从key_attributes中获取地点信息
        if person_data.get("key_attributes"):
            try:
                logger.info(
                    f"原始key_attributes数据: {person_data['key_attributes']}, 类型: {type(person_data['key_attributes'])}"
                )
                key_attributes = (
                    json.loads(person_data["key_attributes"])
                    if isinstance(person_data["key_attributes"], str)
                    else person_data["key_attributes"]
                )
                logger.info(f"解析后key_attributes: {key_attributes}")

                # 方法1: 优先从新的标准化结构中提取地点信息
                if isinstance(key_attributes, dict) and "基本信息" in key_attributes:
                    basic_info = key_attributes.get("基本信息", {})
                    logger.info(f"检测到新的标准化结构，基本信息: {basic_info}")

                    # 从基本信息中提取当前城市
                    if basic_info.get("当前城市") and basic_info["当前城市"].strip():
                        city = basic_info["当前城市"].strip()
                        locations["当前城市"] = city
                        logger.info(f"从标准化结构中找到当前城市: {city}")

                    # 从基本信息中提取家乡
                    if basic_info.get("家乡") and basic_info["家乡"].strip():
                        hometown = basic_info["家乡"].strip()
                        if hometown not in locations.values():
                            locations["家乡"] = hometown
                            logger.info(f"从标准化结构中找到家乡: {hometown}")

                # 方法2: 兼容旧的扁平结构，定义地点相关的关键词
                location_keywords = [
                    "地点",
                    "城市",
                    "居住地",
                    "所在地",
                    "工作地点",
                    "生活地点",
                    "常住地",
                    "家庭住址",
                    "办公地点",
                    "工作城市",
                    "生活城市",
                    "所在城市",
                    "现居地",
                    "location",
                    "city",
                    "address",
                    "workplace",
                    "residence",
                ]

                # 递归遍历key_attributes的所有键值对，查找包含地点相关关键词的键
                def extract_locations_recursive(data, prefix=""):
                    if isinstance(data, dict):
                        for key, value in data.items():
                            current_key = f"{prefix}.{key}" if prefix else key

                            # 如果值是字符串且不为空，检查是否为地点信息
                            if isinstance(value, str) and value.strip():
                                key_lower = key.lower()
                                # 检查键名是否包含地点相关关键词
                                for keyword in location_keywords:
                                    if keyword.lower() in key_lower or keyword in key:
                                        city = value.strip()
                                        # 避免重复添加相同的城市
                                        if city not in locations.values():
                                            locations[current_key] = city
                                            logger.info(
                                                f"从用户档案key_attributes中找到地点信息: {current_key} = {city}"
                                            )
                                        break  # 只跳出内层循环，继续检查其他键

                            # 如果值是字典，递归处理
                            elif isinstance(value, dict):
                                extract_locations_recursive(value, current_key)

                # 执行递归提取
                extract_locations_recursive(key_attributes)

            except (json.JSONDecodeError, TypeError) as e:
                logger.error(f"解析用户档案key_attributes失败: {e}")
                pass

        # 如果key_attributes中没有，尝试从profile_summary中提取
        if not locations and person_data.get("profile_summary"):
            profile_summary = person_data["profile_summary"]

            # 扩展的城市列表，包括直辖市、省会城市、重要城市
            cities = [
                # 直辖市
                "北京",
                "上海",
                "天津",
                "重庆",
                # 省会城市
                "广州",
                "深圳",
                "杭州",
                "南京",
                "成都",
                "武汉",
                "西安",
                "长沙",
                "郑州",
                "济南",
                "沈阳",
                "大连",
                "哈尔滨",
                "长春",
                "石家庄",
                "太原",
                "呼和浩特",
                "银川",
                "兰州",
                "西宁",
                "乌鲁木齐",
                "拉萨",
                "昆明",
                "贵阳",
                "南宁",
                "海口",
                "福州",
                "厦门",
                "南昌",
                "合肥",
                "苏州",
                "无锡",
                "宁波",
                "温州",
                "青岛",
                "烟台",
                "东莞",
                "佛山",
            ]

            # 使用更智能的匹配策略，查找所有匹配的城市
            for city in cities:
                # 匹配 "在北京工作"、"住在上海"、"北京人" 等模式
                if city in profile_summary and (
                    f"在{city}" in profile_summary
                    or f"住{city}" in profile_summary
                    or f"{city}工作" in profile_summary
                    or f"{city}生活" in profile_summary
                    or f"{city}人" in profile_summary
                ):
                    if city not in locations.values():
                        locations["从简介提取"] = city
                        logger.info(f"从用户档案profile_summary中提取到地点信息: {city}")
                        break  # 从简介中只取第一个匹配的城市

        if not locations:
            logger.warning(f"用户档案中未找到地点信息: {person_data.get('canonical_name', '未知用户')}")
            # 使用默认地点进行测试
            default_city = "北京"  # 可以从配置中获取
            logger.info(f"使用默认地点进行天气查询: {default_city}")
            locations["默认地点"] = default_city

        # 获取当前日期，格式为 YYYY-MM-DD
        current_date = datetime.now().strftime("%Y-%m-%d")

        # 为每个地点获取天气信息
        weather_results = {}
        for location_type, city in locations.items():
            try:
                # 获取地理编码
                adcode = get_adcode(city)
                if adcode is not None:
                    # 使用统一的天气获取接口（返回字典格式）
                    weather_result = get_weather_with_fallback(adcode, city, return_format="dict")

                    if weather_result.get("success"):
                        weather_data = weather_result["data"]

                        # 存储天气数据，稍后统一生成综合对比提醒
                        weather_results[location_type] = {
                            "city": city,
                            "weather_data": weather_data,
                            "source": weather_result.get("source", "unknown"),
                        }
                    else:
                        weather_info = weather_result.get("error", f"无法获取{city}的天气信息")
                        weather_results[location_type] = {"city": city, "weather_info": weather_info}
                else:
                    weather_info = f"无法获取{city}的地理位置信息，请检查地点名称是否正确"
                    weather_results[location_type] = {"city": city, "weather_info": weather_info}
                logger.info(f"获取{location_type}({city})的天气信息成功")
            except Exception as e:
                logger.error(f"获取{location_type}({city})的天气信息失败: {e}")
                weather_results[location_type] = {"city": city, "weather_info": f"获取{city}天气信息失败: {str(e)}"}

        # 获取用户近期事件和提醒（统一获取一次）
        user_events = get_user_recent_events_and_reminders(user_id)

        # 检查是否有足够的有效天气数据
        valid_weather_count = sum(1 for result in weather_results.values() if "weather_data" in result)

        # 为所有有效的天气数据生成个性化提醒
        if valid_weather_count >= 2:
            # 多地点情况：生成综合对比提醒
            try:
                # 生成综合天气对比提醒
                comprehensive_reminder = generate_comprehensive_weather_comparison(
                    weather_results, person_data, user_events
                )

                # 将综合提醒添加到结果中
                for location_type in weather_results:
                    if "weather_data" in weather_results[location_type]:
                        weather_results[location_type]["personalized_reminder"] = comprehensive_reminder

                logger.info("生成综合天气对比提醒成功")

            except Exception as e:
                logger.error(f"生成综合天气对比提醒失败: {e}")
                # 如果综合提醒生成失败，为每个城市生成单独提醒
                for location_type, weather_info in weather_results.items():
                    if "weather_data" in weather_info:
                        try:
                            single_reminder = generate_personalized_weather_reminder(
                                weather_info["weather_data"],
                                person_data,
                                weather_info["city"],
                                location_type,
                                user_events,
                                None,
                            )
                            weather_results[location_type]["personalized_reminder"] = single_reminder
                        except Exception as single_e:
                            logger.error(f"生成{location_type}单独提醒失败: {single_e}")
                            weather_results[location_type][
                                "personalized_reminder"
                            ] = f"{weather_info['city']}天气信息已获取，请查看详细数据。"

        elif valid_weather_count == 1:
            # 单地点情况：生成单独的个性化提醒
            logger.info("只有一个地点的天气数据，生成单独的个性化提醒")
            for location_type, weather_info in weather_results.items():
                if "weather_data" in weather_info:
                    try:
                        single_reminder = generate_personalized_weather_reminder(
                            weather_info["weather_data"],
                            person_data,
                            weather_info["city"],
                            location_type,
                            user_events,
                            None,
                        )
                        weather_results[location_type]["personalized_reminder"] = single_reminder
                        logger.info(f"为单地点{location_type}({weather_info['city']})生成个性化提醒成功")
                    except Exception as single_e:
                        logger.error(f"生成{location_type}单独提醒失败: {single_e}")
                        weather_results[location_type][
                            "personalized_reminder"
                        ] = f"{weather_info['city']}天气信息已获取，请查看详细数据。"

        else:
            # 没有有效天气数据的情况，为所有地点添加默认提醒
            logger.warning("没有有效的天气数据，添加默认提醒")
            for location_type, weather_info in weather_results.items():
                weather_results[location_type][
                    "personalized_reminder"
                ] = f"{weather_info['city']}天气信息获取失败，请稍后重试。"

        return {"result": "success", "locations": list(locations.values()), "weather_data": weather_results}

    except Exception as e:
        logger.error(f"根据用户获取天气信息失败: {str(e)}")
        return {"result": "error", "reason": f"根据用户获取天气信息失败: {str(e)}"}


def get_weather_trend_analysis(adcode: str, destination: str):
    """
    获取天气趋势分析（昨天、今天、明天的对比）
    """
    try:
        from datetime import datetime, timedelta

        import requests
        from configs.config import CURRENT_ENV

        # 获取昨天、今天、明天的日期
        today = datetime.now()
        yesterday = today - timedelta(days=1)
        tomorrow = today + timedelta(days=1)

        date_yesterday = yesterday.strftime("%Y-%m-%d")
        date_today = today.strftime("%Y-%m-%d")
        date_tomorrow = tomorrow.strftime("%Y-%m-%d")

        # 使用dailyWeather API获取3天数据（昨天+今天+明天）
        if CURRENT_ENV == "test":
            url = "http://10.243.161.93:8080/weather/dailyWeather"  # 使用生产环境API
        else:
            url = "http://10.243.161.93:8080/weather/dailyWeather"

        params = {"adcode": adcode, "days": 3}  # days=3返回昨天+今天+明天

        logger.info(f"请求天气趋势分析接口：{url}，参数：{params}")
        response = requests.get(url, params=params, timeout=10)

        if response.status_code != 200:
            logger.error(f"天气趋势API请求失败，状态码: {response.status_code}")
            return {"success": False, "error": f"天气趋势服务不可用，状态码: {response.status_code}"}

        weather_json = response.json()
        logger.info(f"获取天气趋势原始响应：{weather_json}")

        # 检查API响应状态
        if weather_json.get("responseStatus", {}).get("code") != 0:
            error_msg = weather_json.get("responseStatus", {}).get("msg", "未知错误")
            logger.error(f"天气趋势API返回错误: {error_msg}")
            return {"success": False, "error": f"天气趋势获取失败: {error_msg}"}

        # 提取天气数据
        forecast_weather = weather_json.get("forecastWeather", {})
        daily_weather_map = forecast_weather.get("dailyWeatherMap", {})

        # 提取各天的天气数据
        weather_data = {}
        for date_key, date_name in [(date_yesterday, "昨天"), (date_today, "今天"), (date_tomorrow, "明天")]:
            day_data = daily_weather_map.get(date_key, {})
            if day_data:
                # 从API响应结构中正确提取数据
                forecast_condition = day_data.get("forecastCondition", {})
                day_condition = forecast_condition.get("dayCondition", {})
                night_condition = forecast_condition.get("nightCondition", {})

                # 计算平均温度
                temp_high = day_condition.get("temperature", 0)
                temp_low = night_condition.get("temperature", 0)
                temp_avg = (temp_high + temp_low) / 2 if temp_high and temp_low else temp_high or temp_low

                weather_data[date_name] = {
                    "date": date_key,
                    "weather_desc": day_condition.get("iconDesc", "未知"),
                    "temp_high": temp_high,
                    "temp_low": temp_low,
                    "temp_avg": temp_avg,
                    "wind_desc": day_condition.get("windDirectionDesc", "未知"),
                    "wind_level": day_condition.get("windLevel", 0),
                    "raw_data": day_data,
                }

        # 分析温度趋势
        trend_analysis = analyze_weather_trend(weather_data, destination)

        return {"success": True, "weather_data": weather_data, "trend_analysis": trend_analysis}

    except Exception as e:
        logger.error(f"获取天气趋势分析失败: {e}")
        return {"success": False, "error": f"天气趋势分析失败: {str(e)}"}


def analyze_weather_trend(weather_data: dict, destination: str):
    """
    分析天气趋势变化
    """
    try:
        if len(weather_data) < 2:
            return "天气数据不足，无法分析趋势"

        yesterday = weather_data.get("昨天", {})
        today = weather_data.get("今天", {})
        tomorrow = weather_data.get("明天", {})

        trend_parts = []

        # 分析今天相对昨天的变化
        if yesterday and today:
            temp_diff_today = today.get("temp_avg", 0) - yesterday.get("temp_avg", 0)

            if temp_diff_today > 2:
                trend_parts.append(f"🌡️ **今天比昨天热了{temp_diff_today:.1f}°C**")
            elif temp_diff_today < -2:
                trend_parts.append(f"🌡️ **今天比昨天冷了{abs(temp_diff_today):.1f}°C**")
            else:
                trend_parts.append(f"🌡️ **今天温度与昨天基本持平**（相差{abs(temp_diff_today):.1f}°C）")

            # 天气状况对比
            yesterday_weather = yesterday.get("weather_desc", "")
            today_weather = today.get("weather_desc", "")
            if yesterday_weather != today_weather:
                trend_parts.append(f"🌤️ **天气变化**: {yesterday_weather} → {today_weather}")

        # 分析明天的趋势
        if today and tomorrow:
            temp_diff_tomorrow = tomorrow.get("temp_avg", 0) - today.get("temp_avg", 0)

            if temp_diff_tomorrow > 2:
                trend_parts.append(f"📈 **明天会更热**，预计升温{temp_diff_tomorrow:.1f}°C")
            elif temp_diff_tomorrow < -2:
                trend_parts.append(f"📉 **明天会降温**，预计降温{abs(temp_diff_tomorrow):.1f}°C")
            else:
                trend_parts.append(f"➡️ **明天温度稳定**，变化不大（{temp_diff_tomorrow:+.1f}°C）")

            # 明天天气状况
            tomorrow_weather = tomorrow.get("weather_desc", "")
            today_weather = today.get("weather_desc", "")
            if tomorrow_weather != today_weather:
                trend_parts.append(f"🔮 **明天天气**: {today_weather} → {tomorrow_weather}")

        # 生成3天温度范围总结
        if yesterday and today and tomorrow:
            all_temps = [yesterday.get("temp_avg", 0), today.get("temp_avg", 0), tomorrow.get("temp_avg", 0)]
            temp_range = f"{min(all_temps):.0f}°C - {max(all_temps):.0f}°C"
            trend_parts.append(f"📊 **三天温度范围**: {temp_range}")

        return "\n".join(trend_parts) if trend_parts else "暂无明显天气趋势变化"

    except Exception as e:
        logger.error(f"天气趋势分析失败: {e}")
        return f"天气趋势分析失败: {str(e)}"


def get_enhanced_weather_info_by_adcode(adcode: str, time_interval: int, time: str, destination: str):
    """
    获取增强的天气信息，包含更多详细数据用于个性化提醒
    """
    try:
        import json

        import requests
        from configs.config import CURRENT_ENV
        from tools.weather import get_adcode, get_time_interval

        if time_interval == 0:
            # 获取实时天气
            # 注意：测试环境的API不稳定，统一使用生产环境API
            if CURRENT_ENV == "test":
                url = "http://10.243.161.93:8080/weather/realtimeWeather"  # 使用生产环境API
            else:
                url = "http://10.243.161.93:8080/weather/realtimeWeather"

            params = {"adcode": adcode}

            logger.info(f"请求增强天气信息接口：{url}，参数：{params}")
            response = requests.get(url, params=params, timeout=10)

            if response.status_code != 200:
                return {"success": False, "error": f"天气服务不可用，状态码: {response.status_code}"}

            weather_json = response.json()
            logger.info(f"获取增强天气信息原始响应：{weather_json}")

            if weather_json["responseStatus"]["code"] != 0:
                return {"success": False, "error": f"获取天气信息失败: {weather_json['responseStatus']['msg']}"}

            real_time_weather = weather_json.get("realTimeWeather", {})
            condition_item = real_time_weather.get("realTimeCondition", {}).get("conditionItem", {})

            if not condition_item:
                return {"success": False, "error": "没有找到当前时间的天气信息"}

            # 提取详细天气数据
            weather_data = {
                "temperature": condition_item.get("temperature"),  # 气温
                "feels_like": condition_item.get("feelsLike"),  # 体感温度
                "humidity": condition_item.get("humidity"),  # 湿度
                "weather_desc": condition_item.get("iconDesc", ""),  # 天气描述
                "wind_direction": condition_item.get("windDirectionDesc", ""),  # 风向
                "wind_speed": condition_item.get("windSpeed"),  # 风速
                "wind_level": condition_item.get("windLevel"),  # 风级
                "pressure": condition_item.get("pressure"),  # 气压
                "visibility": condition_item.get("visibility"),  # 能见度
                "uv_index": condition_item.get("uvIndex"),  # 紫外线指数
                "air_quality": condition_item.get("aqi"),  # 空气质量指数
                "city": destination,
                "time": time,
            }

            # 生成格式化的天气信息（优先使用体感温度）
            temp_display = weather_data["feels_like"] if weather_data["feels_like"] else weather_data["temperature"]
            temp_type = "体感温度" if weather_data["feels_like"] else "气温"

            formatted_info = f"""当前时间{time}，{destination}的天气为{weather_data['weather_desc']}，
{temp_type}为{temp_display}摄氏度"""

            if (
                weather_data["temperature"]
                and weather_data["feels_like"]
                and weather_data["temperature"] != weather_data["feels_like"]
            ):
                formatted_info += f"（实际气温{weather_data['temperature']}°C）"

            formatted_info += f"""，风向为{weather_data['wind_direction']}，
风速为{weather_data['wind_speed']}米/秒，属于{weather_data['wind_level']}级风"""

            if weather_data["humidity"]:
                formatted_info += f"，湿度{weather_data['humidity']}%"

            return {"success": True, "data": weather_data, "formatted_info": formatted_info}
        else:
            # 对于未来天气，暂时使用原有逻辑
            from tools.weather import get_weather_info_by_adcode

            weather_info = get_weather_info_by_adcode(adcode, time_interval, time, destination)
            return {"success": True, "data": {"basic_forecast": weather_info}, "formatted_info": weather_info}

    except Exception as e:
        logger.error(f"获取增强天气信息失败: {e}")
        return {"success": False, "error": f"获取天气信息失败: {str(e)}"}


def generate_comprehensive_weather_comparison(
    weather_results: dict,
    person_data: dict,
    user_events: str = None,
) -> str:
    """
    生成多城市天气对比的综合个性化提醒
    """
    try:
        import json

        from configs.lion_config import get_value

        # 构建多城市天气对比数据
        weather_comparison = build_weather_comparison_data(weather_results)

        # 获取完整的用户档案信息
        full_user_profile = prepare_full_user_profile(person_data)

        # 从Lion配置中心获取天气提醒的prompt模板（统一使用同一个配置）
        comprehensive_prompt_template = get_value(
            "humanrelation.weather.reminder_prompt",
            """请根据以下天气信息、用户完整档案、近期事件，生成一条个性化的天气提醒建议。

多城市天气对比：
{weather_comparison}

用户完整档案：
{user_profile}

用户近期事件：
{user_events}

请生成温和、贴心的天气提醒，要求：
1. 语言自然友好，像朋友间的关怀
2. 结合用户的具体情况和近期安排
3. 给出实用的生活建议
4. 不超过150字
5. 直接输出提醒内容，不要前缀

回复格式：直接给出提醒内容，不要前缀。""",
        )

        # 构建完整的综合天气提醒prompt
        comprehensive_prompt = comprehensive_prompt_template.format(
            weather_comparison=weather_comparison,
            user_profile=full_user_profile,
            user_events=user_events or "用户暂无近期事件和提醒记录",
        )

        logger.info(f"生成多城市天气对比提醒，用户: {person_data.get('canonical_name', '未知')}")

        # 调用大模型生成综合个性化提醒
        ai_reminder = call_ai_for_weather_reminder(comprehensive_prompt)

        if ai_reminder:
            logger.info(f"为{person_data.get('canonical_name', '用户')}生成综合天气对比提醒成功")
            return ai_reminder
        else:
            # 如果AI调用失败，生成基础提醒
            return generate_basic_multi_city_reminder(weather_results)

    except Exception as e:
        logger.error(f"生成综合天气对比提醒失败: {e}")
        return generate_basic_multi_city_reminder(weather_results)


def build_weather_comparison_data(weather_results: dict) -> str:
    """
    构建多城市天气对比数据
    """
    try:
        comparison_data = []

        for location_type, weather_info in weather_results.items():
            if "weather_data" not in weather_info:
                continue

            city = weather_info["city"]
            weather_data = weather_info["weather_data"]

            # 提取当前天气信息
            current_temp = weather_data.get("temperature", "未知")
            current_feels_like = weather_data.get("feelsLike", current_temp)
            current_weather = weather_data.get("weather", "未知")
            current_humidity = weather_data.get("humidity", "未知")

            # 提取预报数据进行对比分析
            forecast = weather_data.get("forecast", [])
            yesterday_analysis = analyze_yesterday_comparison(forecast, current_temp)
            tomorrow_forecast = analyze_tomorrow_forecast(forecast)

            # 构建该城市的天气变化描述
            city_comparison = f"""{city}（{location_type}）：
- 今天：{current_temp}°C（体感{current_feels_like}°C），{current_weather}，湿度{current_humidity}%
- 昨天对比：{yesterday_analysis}
- 明天预报：{tomorrow_forecast}
- 空气质量：{weather_data.get('air_quality', '未知')}
- 紫外线：{weather_data.get('uv_index', '未知')}"""

            comparison_data.append(city_comparison)

        return "\n\n".join(comparison_data)

    except Exception as e:
        logger.error(f"构建天气对比数据失败: {e}")
        return "天气对比数据构建失败"


def analyze_yesterday_comparison(forecast: list, current_temp: str) -> str:
    """
    分析昨天与今天的温度对比
    """
    try:
        if not forecast or len(forecast) < 2:
            return "无昨日对比数据"

        # 假设forecast[0]是今天，forecast[1]是明天，我们需要推算昨天
        # 这里简化处理，实际应该从历史数据获取
        if isinstance(current_temp, str) and current_temp.isdigit():
            current_temp_int = int(current_temp)
            # 简化的温度变化分析
            if current_temp_int > 30:
                return "比昨天更热"
            elif current_temp_int < 15:
                return "比昨天更冷"
            else:
                return "与昨天相近"

        return "温度变化待分析"

    except Exception as e:
        logger.error(f"分析昨天温度对比失败: {e}")
        return "昨日对比分析失败"


def analyze_tomorrow_forecast(forecast: list) -> str:
    """
    分析明天的天气预报
    """
    try:
        if not forecast or len(forecast) < 2:
            return "无明日预报数据"

        # 获取明天的预报数据
        tomorrow = forecast[1] if len(forecast) > 1 else forecast[0]

        tomorrow_temp_high = tomorrow.get("tempMax", "未知")
        tomorrow_temp_low = tomorrow.get("tempMin", "未知")
        tomorrow_weather = tomorrow.get("textDay", "未知")

        return f"{tomorrow_temp_low}-{tomorrow_temp_high}°C，{tomorrow_weather}"

    except Exception as e:
        logger.error(f"分析明天预报失败: {e}")
        return "明日预报分析失败"


def generate_basic_multi_city_reminder(weather_results: dict) -> str:
    """
    生成基础的多城市天气提醒（AI调用失败时的备选方案）
    """
    try:
        cities_info = []
        for location_type, weather_info in weather_results.items():
            if "weather_data" in weather_info:
                city = weather_info["city"]
                weather_data = weather_info["weather_data"]
                temp = weather_data.get("temperature", "未知")
                weather = weather_data.get("weather", "未知")
                cities_info.append(f"{city}今日{temp}°C，{weather}")

        if cities_info:
            return f"今日天气：{' | '.join(cities_info)}。请根据天气情况合理安排出行，注意保暖或防暑。"
        else:
            return "今日天气信息获取中，请稍后查看。"

    except Exception as e:
        logger.error(f"生成基础多城市提醒失败: {e}")
        return "天气提醒生成失败，请稍后重试。"


def generate_personalized_weather_reminder(
    weather_data: dict,
    person_data: dict,
    city: str,
    location_type: str,
    user_events: str = None,
    trend_data: dict = None,
):
    """
    基于天气数据、用户信息和近期事件生成个性化天气提醒
    """
    try:
        import json

        from configs.lion_config import get_value

        # 获取完整的用户档案信息（不再只提取部分信息）
        full_user_profile = prepare_full_user_profile(person_data)

        # 从Lion配置中心获取天气提醒的prompt模板
        weather_prompt_template = get_value(
            "humanrelation.weather.reminder_prompt",
            """你是一个专业的天气助手，请根据以下详细天气信息为用户生成个性化的天气提醒。

=== 当前天气状况 ===
地点：{city}（{location_type}）
当前天气：{weather_desc}
当前气温：{temperature}°C（体感{feels_like}°C）
湿度：{humidity}%
风力：{wind_direction} {wind_speed}km/h，{wind_level}级风
降水量：{precipitation}
气压：{pressure}
能见度：{visibility}
云量：{cloud}
露点：{dew_point}

=== 今日天气预报 ===
{today_forecast}

=== 明日天气预报 ===
{tomorrow_forecast}

=== 逐小时预报（接下来几小时）===
{hourly_forecast}

=== 天气趋势分析 ===
{weather_trend}

=== 用户档案信息 ===
{user_profile}

=== 用户近期事件和提醒 ===
{user_events}

=== 生成要求 ===
请基于以上信息生成一条个性化天气提醒，必须包含以下要素：

1. **天气状况识别**：
   - 准确描述当前天气（特别注意降水、雷电、大风等恶劣天气）
   - 如果有降水量数据，必须重点提及
   - 如果是雨天/雪天/雷电天气，必须强调并给出相应建议

2. **体感和温度建议**：
   - 基于体感温度给出穿衣建议
   - 考虑湿度对体感的影响
   - 提及温度变化趋势

3. **出行安全提醒**：
   - 根据天气条件给出出行建议
   - 恶劣天气时必须提醒携带雨具、注意安全
   - 考虑能见度、风力对出行的影响

4. **健康关怀**：
   - 基于湿度、气压、空气质量给出健康建议
   - 结合用户年龄、职业特点个性化建议

5. **个性化内容**：
   - 结合用户的具体情况（姓名、职业、家庭等）
   - 考虑用户近期事件安排
   - 语言温暖贴心，像朋友一样关心

**重要提醒**：
- 如果当前有降水（降水量>0mm），必须明确提及是雨天并建议携带雨具
- 如果预报有雷阵雨、暴雨等恶劣天气，必须重点提醒
- 不要说"天气温和"除非确实是晴朗无雨的好天气
- 字数控制在100-150字，语言简洁但信息丰富

直接输出提醒内容，不要任何前缀或标题。""",
        )

        # 构建天气趋势信息
        weather_trend_info = "暂无趋势数据"
        if trend_data and trend_data.get("success"):
            weather_trend_info = trend_data.get("trend_analysis", "暂无趋势分析")

        # 安全获取天气数据字段的辅助函数
        def safe_get_weather_field(field_names, default="未知"):
            """安全获取天气数据字段，支持多个候选字段名"""
            if isinstance(field_names, str):
                field_names = [field_names]

            for field_name in field_names:
                value = weather_data.get(field_name)
                if value and str(value).strip():
                    return str(value).strip()
            return default

        def safe_remove_suffix(value, suffix):
            """安全移除字符串后缀"""
            if value and isinstance(value, str) and value != "未知":
                return value.replace(suffix, "")
            return value

        # 处理预报数据
        def format_forecast_info(forecast_list, days=2):
            """格式化预报信息"""
            if not forecast_list or not isinstance(forecast_list, list):
                return "暂无预报数据"

            forecast_info = []
            for i, day in enumerate(forecast_list[:days]):
                if isinstance(day, dict):
                    date = day.get("date", f"第{i+1}天")
                    temp_max = day.get("tempMax", "")
                    temp_min = day.get("tempMin", "")
                    text_day = day.get("textDay", "")
                    text_night = day.get("textNight", "")
                    uv_index = day.get("uvIndex", "")

                    day_info = f"{date}: {text_day}转{text_night}，{temp_min}-{temp_max}°C"
                    if uv_index:
                        day_info += f"，紫外线指数{uv_index}"
                    forecast_info.append(day_info)

            return "\n".join(forecast_info) if forecast_info else "暂无预报数据"

        def format_hourly_info(hourly_list, hours=6):
            """格式化逐小时预报信息"""
            if not hourly_list or not isinstance(hourly_list, list):
                return "暂无逐小时预报"

            hourly_info = []
            for i, hour in enumerate(hourly_list[:hours]):
                if isinstance(hour, dict):
                    time = hour.get("time", f"{i+1}小时后")
                    temp = hour.get("temperature", "")
                    weather = hour.get("weather", "")
                    pop = hour.get("pop", "")  # 降水概率

                    hour_info = f"{time}: {weather} {temp}°C"
                    if pop:
                        hour_info += f"，降水概率{pop}"
                    hourly_info.append(hour_info)

            return "\n".join(hourly_info) if hourly_info else "暂无逐小时预报"

        # 获取预报数据
        forecast_data = weather_data.get("forecast", [])
        hourly_data = weather_data.get("hourly", [])

        today_forecast = "暂无今日预报"
        tomorrow_forecast = "暂无明日预报"

        if forecast_data and len(forecast_data) >= 1:
            today_forecast = format_forecast_info([forecast_data[0]], 1)
        if forecast_data and len(forecast_data) >= 2:
            tomorrow_forecast = format_forecast_info([forecast_data[1]], 1)

        # 构建完整的天气提醒prompt
        weather_prompt = weather_prompt_template.format(
            city=city,
            location_type=location_type,
            weather_desc=safe_get_weather_field(["weather", "weather_desc"]),
            feels_like=safe_get_weather_field(["feelsLike", "feels_like", "temperature"]),
            temperature=safe_get_weather_field("temperature"),
            humidity=safe_remove_suffix(safe_get_weather_field("humidity"), "%"),
            wind_direction=safe_get_weather_field(["windDirection", "wind_direction"]),
            wind_speed=safe_remove_suffix(safe_get_weather_field(["windSpeed", "wind_speed"]), "km/h"),
            wind_level=safe_remove_suffix(safe_get_weather_field(["windPower", "wind_level"]), "级"),
            precipitation=safe_get_weather_field("precipitation"),
            pressure=safe_get_weather_field("pressure"),
            visibility=safe_get_weather_field("visibility"),
            cloud=safe_get_weather_field("cloud"),
            dew_point=safe_get_weather_field(["dewPoint", "dew_point"]),
            today_forecast=today_forecast,
            tomorrow_forecast=tomorrow_forecast,
            hourly_forecast=format_hourly_info(hourly_data),
            weather_trend=weather_trend_info,
            user_profile=full_user_profile,
            user_events=user_events or "用户暂无近期事件和提醒记录",
        )

        logger.info(f"使用完整用户档案生成个性化天气提醒，用户: {person_data.get('canonical_name', '未知')}")

        # 调用大模型生成个性化提醒
        ai_reminder = call_ai_for_weather_reminder(weather_prompt)

        if ai_reminder:
            logger.info(f"为{person_data.get('canonical_name', '用户')}生成个性化天气提醒成功")
            return ai_reminder
        else:
            # 如果AI调用失败，生成基础提醒
            user_profile = extract_user_profile_for_weather(person_data)  # 备用方案仍使用简化信息
            return generate_basic_weather_reminder(weather_data, user_profile, city)

    except Exception as e:
        logger.error(f"生成个性化天气提醒失败: {e}")
        user_profile = extract_user_profile_for_weather(person_data)
        return generate_basic_weather_reminder(weather_data, user_profile, city)


def get_user_recent_events_and_reminders(user_id: str) -> str:
    """
    获取用户近期事件和提醒，直接返回原始数据给AI分析
    """
    try:
        from datetime import datetime, timedelta

        from configs.lion_config import get_value
        from my_mysql.entity.reminders import query_reminders_by_user

        events_info = []

        # 1. 获取用户提醒事项
        try:
            reminders = query_reminders_by_user(user_id)
            if reminders:
                events_info.append("=== 用户提醒事项 ===")
                for reminder in reminders:
                    try:
                        # 处理不同的数据格式
                        if hasattr(reminder, "_asdict"):
                            reminder_dict = reminder._asdict()
                        elif hasattr(reminder, "__dict__"):
                            reminder_dict = reminder.__dict__
                        elif isinstance(reminder, dict):
                            reminder_dict = reminder
                        else:
                            continue

                        status = reminder_dict.get("status", "")
                        next_trigger_time = reminder_dict.get("next_trigger_time")
                        content = reminder_dict.get("reminder_text_template", "")

                        if next_trigger_time and content:
                            # 格式化时间
                            if isinstance(next_trigger_time, str):
                                time_str = next_trigger_time
                            else:
                                time_str = next_trigger_time.strftime("%Y-%m-%d %H:%M")

                            events_info.append(f"- [{status}] {time_str}: {content}")
                    except Exception as e:
                        logger.error(f"处理提醒记录失败: {e}")
                        continue
        except Exception as e:
            logger.error(f"获取用户提醒失败: {e}")

        # 2. 获取用户近期事件记忆
        try:
            event_index = get_value("humanrelation.event_index_name", "memory_event_store")
            recent_events_result = get_recent_events(event_index, user_id, 10)  # 获取最近10条事件

            if recent_events_result.get("result") == "success" and recent_events_result.get("events"):
                events_info.append("\n=== 用户近期事件 ===")
                for event in recent_events_result["events"]:
                    timestamp = event.get("timestamp", "")
                    description = event.get("description_text", "")
                    location = event.get("location", "")
                    participants = event.get("participants", [])

                    if description:
                        event_text = f"- {timestamp}: {description}"
                        if location:
                            event_text += f" (地点: {location})"
                        if participants:
                            event_text += f" (参与者: {', '.join(participants)})"
                        events_info.append(event_text)
        except Exception as e:
            logger.error(f"获取用户近期事件失败: {e}")

        # 3. 返回原始数据给AI分析
        if events_info:
            result = "\n".join(events_info)
            logger.info(f"获取用户{user_id}近期事件和提醒，共{len(events_info)}条信息")
            return result
        else:
            return "用户暂无近期事件和提醒记录"

    except Exception as e:
        logger.error(f"获取用户近期事件和提醒失败: {e}")
        return "无法获取用户近期事件信息"


def get_user_today_activities(user_id: str) -> dict:
    """
    获取用户今日的提醒事项和活动安排
    """
    try:
        from datetime import datetime, timedelta

        from my_mysql.entity.reminders import query_reminders_by_user

        today = datetime.now().date()
        tomorrow = today + timedelta(days=1)

        # 获取用户所有活跃的提醒
        all_reminders = query_reminders_by_user(user_id)

        today_activities = {"today_reminders": [], "upcoming_reminders": [], "total_count": 0}

        if not all_reminders:
            return today_activities

        for reminder in all_reminders:
            try:
                # 处理不同的数据格式
                if hasattr(reminder, "_asdict"):
                    reminder_dict = reminder._asdict()
                elif hasattr(reminder, "__dict__"):
                    reminder_dict = reminder.__dict__
                elif isinstance(reminder, dict):
                    reminder_dict = reminder
                else:
                    continue

                # 只处理活跃状态的提醒
                if reminder_dict.get("status") != "active":
                    continue

                next_trigger_time = reminder_dict.get("next_trigger_time")
                if not next_trigger_time:
                    continue

                # 确保时间格式正确
                if isinstance(next_trigger_time, str):
                    next_trigger_time = datetime.fromisoformat(next_trigger_time.replace("Z", "+00:00"))
                elif not isinstance(next_trigger_time, datetime):
                    continue

                trigger_date = next_trigger_time.date()

                activity_info = {
                    "time": next_trigger_time.strftime("%H:%M"),
                    "content": reminder_dict.get("reminder_text_template", ""),
                    "person_id": reminder_dict.get("subject_person_id", ""),
                    "full_time": next_trigger_time.strftime("%Y-%m-%d %H:%M"),
                }

                # 今日的提醒
                if trigger_date == today:
                    today_activities["today_reminders"].append(activity_info)
                # 明日的提醒（作为即将到来的活动参考）
                elif trigger_date == tomorrow:
                    today_activities["upcoming_reminders"].append(activity_info)

            except Exception as e:
                logger.error(f"处理提醒记录失败: {e}")
                continue

        # 按时间排序
        today_activities["today_reminders"].sort(key=lambda x: x["time"])
        today_activities["upcoming_reminders"].sort(key=lambda x: x["time"])
        today_activities["total_count"] = len(today_activities["today_reminders"]) + len(
            today_activities["upcoming_reminders"]
        )

        logger.info(
            f"获取用户{user_id}今日活动: 今日{len(today_activities['today_reminders'])}项, 明日{len(today_activities['upcoming_reminders'])}项"
        )
        return today_activities

    except Exception as e:
        logger.error(f"获取用户今日活动失败: {e}")
        return {"today_reminders": [], "upcoming_reminders": [], "total_count": 0}


def prepare_activities_info(today_activities: dict) -> str:
    """
    将今日活动信息格式化为文本，用于AI分析
    """
    if not today_activities or today_activities.get("total_count", 0) == 0:
        return "今日无特殊活动安排"

    activities_text = []

    # 今日提醒事项
    today_reminders = today_activities.get("today_reminders", [])
    if today_reminders:
        activities_text.append("=== 今日提醒事项 ===")
        for reminder in today_reminders:
            time_str = reminder.get("time", "")
            content = reminder.get("content", "")
            if time_str and content:
                activities_text.append(f"- {time_str}: {content}")

    # 明日即将到来的活动（作为参考）
    upcoming_reminders = today_activities.get("upcoming_reminders", [])
    if upcoming_reminders:
        activities_text.append("=== 明日重要活动 ===")
        for reminder in upcoming_reminders[:3]:  # 最多显示3个明日活动
            time_str = reminder.get("time", "")
            content = reminder.get("content", "")
            if time_str and content:
                activities_text.append(f"- {time_str}: {content}")

    if not activities_text:
        return "今日无特殊活动安排"

    return "\n".join(activities_text)


def prepare_full_user_profile(person_data: dict) -> str:
    """
    准备完整的用户档案信息，用于AI分析
    """
    try:
        import json

        profile_parts = []

        # 基本信息
        profile_parts.append(f"姓名：{person_data.get('canonical_name', '未知')}")

        if person_data.get("aliases"):
            profile_parts.append(f"别名：{person_data.get('aliases')}")

        # 关系信息
        relationships = person_data.get("relationships", [])
        if relationships:
            # 处理relationships可能是字符串的情况
            if isinstance(relationships, str):
                try:
                    relationships = json.loads(relationships)
                except (json.JSONDecodeError, TypeError):
                    relationships = []

            if isinstance(relationships, list) and relationships:
                rel_texts = []
                for rel in relationships:
                    if isinstance(rel, dict):
                        rel_texts.append(f"{rel.get('type', '未知')}关系")
                    elif isinstance(rel, str):
                        rel_texts.append(f"{rel}关系")
                if rel_texts:
                    profile_parts.append(f"关系：{', '.join(rel_texts)}")

        # 个人简介
        if person_data.get("profile_summary"):
            profile_parts.append(f"个人简介：{person_data.get('profile_summary')}")

        # 详细属性信息
        key_attributes = person_data.get("key_attributes", {})
        if isinstance(key_attributes, str):
            key_attributes = json.loads(key_attributes)

        if isinstance(key_attributes, dict):
            # 处理标准化结构
            if "基本信息" in key_attributes:
                basic_info = key_attributes["基本信息"]
                profile_parts.append("=== 基本信息 ===")

                for key, value in basic_info.items():
                    if value and key != "联系方式":  # 联系方式单独处理
                        if isinstance(value, dict):
                            # 处理嵌套字典（如职业信息、家庭情况）
                            if value:  # 只有非空字典才显示
                                sub_items = []
                                for sub_key, sub_value in value.items():
                                    if sub_value:
                                        if isinstance(sub_value, list):
                                            if sub_value:  # 非空列表
                                                sub_items.append(f"{sub_key}: {', '.join(map(str, sub_value))}")
                                        else:
                                            sub_items.append(f"{sub_key}: {sub_value}")
                                if sub_items:
                                    profile_parts.append(f"{key}：{'; '.join(sub_items)}")
                        else:
                            profile_parts.append(f"{key}：{value}")

                # 联系方式特殊处理（保护隐私，只显示有无）
                contact_info = basic_info.get("联系方式", {})
                if contact_info:
                    contact_items = []
                    if contact_info.get("电话"):
                        contact_items.append("有电话")
                    if contact_info.get("邮箱"):
                        contact_items.append("有邮箱")
                    if contact_info.get("社交账号", {}).get("微信"):
                        contact_items.append("有微信")
                    if contact_items:
                        profile_parts.append(f"联系方式：{', '.join(contact_items)}")

            # 处理其他顶级属性
            for key, value in key_attributes.items():
                if key != "基本信息" and value and isinstance(value, str):
                    profile_parts.append(f"{key}：{value}")

        # 时间信息
        if person_data.get("created_at"):
            profile_parts.append(f"档案创建时间：{person_data.get('created_at')}")
        if person_data.get("updated_at"):
            profile_parts.append(f"最后更新时间：{person_data.get('updated_at')}")

        return "\n".join(profile_parts)

    except Exception as e:
        logger.error(f"准备完整用户档案失败: {e}")
        # 返回基础信息作为备选
        return f"姓名：{person_data.get('canonical_name', '未知')}\n简介：{person_data.get('profile_summary', '无')}"


def extract_user_profile_for_weather(person_data: dict) -> dict:
    """
    从person_data中提取用于天气提醒的用户信息
    """
    try:
        profile = {
            "name": person_data.get("canonical_name", ""),
            "age": "",
            "gender": "",
            "occupation": "",
            "interests": "",
            "health_notes": "",
        }

        # 从key_attributes中提取信息
        key_attributes = person_data.get("key_attributes", {})
        if isinstance(key_attributes, str):
            import json

            key_attributes = json.loads(key_attributes)

        # 从标准化结构中提取
        if "基本信息" in key_attributes:
            basic_info = key_attributes["基本信息"]
            profile["gender"] = basic_info.get("性别", "")

            # 从生日推算年龄
            birthday = basic_info.get("生日", "")
            if birthday and "年" in birthday:
                try:
                    birth_year = int(birthday.replace("年", ""))
                    from datetime import datetime

                    current_year = datetime.now().year
                    profile["age"] = str(current_year - birth_year)
                except:
                    pass

            # 职业信息
            job_info = basic_info.get("职业信息", {})
            if job_info:
                job_parts = []
                if job_info.get("职位"):
                    job_parts.append(job_info["职位"])
                if job_info.get("公司"):
                    job_parts.append(job_info["公司"])
                if job_info.get("行业"):
                    job_parts.append(job_info["行业"])
                profile["occupation"] = "，".join(job_parts)

        # 从其他字段提取
        profile["interests"] = key_attributes.get("兴趣", "")

        # 从profile_summary中提取补充信息
        profile_summary = person_data.get("profile_summary", "")
        if profile_summary and not profile["occupation"]:
            # 简单的职业关键词匹配
            job_keywords = ["工程师", "经理", "总监", "专家", "顾问", "老师", "医生", "律师"]
            for keyword in job_keywords:
                if keyword in profile_summary:
                    profile["occupation"] = keyword
                    break

        return profile

    except Exception as e:
        logger.error(f"提取用户档案信息失败: {e}")
        return {
            "name": person_data.get("canonical_name", ""),
            "age": "",
            "gender": "",
            "occupation": "",
            "interests": "",
            "health_notes": "",
        }


def call_ai_for_weather_reminder(prompt: str) -> str:
    """
    调用AI生成个性化天气提醒
    """
    try:
        import json

        import requests
        from configs.lion_config import get_value

        # 获取AI API配置，天气功能可以单独配置模型
        api_url = get_value("humanrelation.ai.base_url", "https://aigc.sankuai.com/v1/openai/native/chat/completions")
        api_token = get_value("humanrelation.ai.token", "1930188146638651430")
        # 天气专用模型配置，如果没有配置则使用通用AI模型
        model_name = get_value("humanrelation.weather.ai.model", get_value("humanrelation.ai.model", "gpt-4.1"))

        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_token}"}

        # 天气AI生成参数配置
        max_tokens = int(get_value("humanrelation.weather.ai.max_tokens", "800"))
        temperature = float(get_value("humanrelation.weather.ai.temperature", "0.7"))

        payload = {
            "model": model_name,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": temperature,
        }

        logger.info(f"调用天气专用AI生成提醒，模型: {model_name}, max_tokens: {max_tokens}, temperature: {temperature}")
        # 减少AI调用超时时间以提高响应速度
        response = requests.post(api_url, headers=headers, json=payload, timeout=10)

        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                ai_response = result["choices"][0]["message"]["content"].strip()
                logger.info(f"AI天气提醒生成成功，长度: {len(ai_response)} 字符")
                logger.debug(f"完整AI天气提醒内容: {ai_response}")
                return ai_response
            else:
                logger.error(f"AI响应格式异常: {result}")
                return ""
        else:
            logger.error(f"AI API调用失败，状态码: {response.status_code}, 响应: {response.text}")
            return ""

    except Exception as e:
        logger.error(f"调用AI生成天气提醒失败: {e}")
        return ""


def generate_basic_weather_reminder(weather_data: dict, user_profile: dict, city: str) -> str:
    """
    生成基础的天气提醒（当AI调用失败时使用）
    """
    try:
        reminders = []

        # 体感温度提醒
        feels_like = weather_data.get("feels_like") or weather_data.get("temperature")
        if feels_like:
            temp = (
                float(feels_like)
                if isinstance(feels_like, (int, float, str)) and str(feels_like).replace(".", "").isdigit()
                else None
            )
            if temp is not None:
                if temp <= 0:
                    reminders.append("天气严寒，请注意保暖，建议穿厚外套")
                elif temp <= 10:
                    reminders.append("天气较冷，建议穿外套或毛衣")
                elif temp <= 20:
                    reminders.append("天气凉爽，建议穿长袖衣物")
                elif temp <= 30:
                    reminders.append("天气温和，穿着舒适即可")
                else:
                    reminders.append("天气炎热，建议穿轻薄透气的衣物")

        # 天气状况提醒
        weather_desc = weather_data.get("weather_desc", "")
        if "雨" in weather_desc:
            reminders.append("有降雨，请携带雨具")
        elif "雪" in weather_desc:
            reminders.append("有降雪，出行请注意安全")
        elif "雾" in weather_desc or "霾" in weather_desc:
            reminders.append("能见度较低，出行请注意安全")

        # 风力提醒
        wind_level = weather_data.get("wind_level")
        if wind_level and str(wind_level).isdigit():
            level = int(wind_level)
            if level >= 6:
                reminders.append("风力较大，外出请注意安全")

        # 湿度提醒
        humidity = weather_data.get("humidity")
        if humidity and str(humidity).replace("%", "").isdigit():
            hum = int(str(humidity).replace("%", ""))
            if hum >= 80:
                reminders.append("湿度较高，注意通风")
            elif hum <= 30:
                reminders.append("空气干燥，注意补水")

        # 组合提醒
        if reminders:
            name = user_profile.get("name", "")
            prefix = f"{name}，" if name else ""
            return f"{prefix}{city}今日天气提醒：" + "；".join(reminders) + "。"
        else:
            return f"{city}天气状况正常，请根据实际情况合理安排出行。"

    except Exception as e:
        logger.error(f"生成基础天气提醒失败: {e}")
        return f"{city}天气信息已获取，请注意关注天气变化。"


def get_events_by_person_id(
    index_name: str, user_id: str, person_id: str, page: int = 1, page_size: int = 20
):  # 获取某个人的事件，支持分页
    """获取某个人参与的所有事件，支持分页，默认每页20条"""
    try:
        # 计算起始位置
        from_position = (page - 1) * page_size

        search_body = {
            "from": from_position,
            "size": page_size,
            "sort": [{"timestamp": {"order": "desc"}}],
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"user_id": user_id}},
                        {"term": {"participants": person_id}},
                        # 只搜索事件类型的记忆
                        {
                            "bool": {
                                "should": [
                                    {"term": {"memory_type": "event"}},
                                    {"bool": {"must_not": {"exists": {"field": "memory_type"}}}},
                                ]
                            }
                        },
                    ]
                }
            },
        }

        # 执行搜索并获取总数
        response = client.search(index=index_name, body=search_body, track_total_hits=True)  # 确保获取准确的总数

        events = [hit["_source"] for hit in response["hits"]["hits"]]
        total = response["hits"]["total"]["value"]
        total_pages = (total + page_size - 1) // page_size  # 计算总页数

        return {
            "result": "success",
            "events": events,
            "pagination": {
                "current_page": page,
                "page_size": page_size,
                "total_items": total,
                "total_pages": total_pages,
            },
        }
    except Exception as e:
        logger.error(f"获取人员事件分页失败: {str(e)}")
        return {
            "result": "error",
            "reason": str(e),
            "events": [],
            "pagination": {"current_page": page, "page_size": page_size, "total_items": 0, "total_pages": 0},
        }
