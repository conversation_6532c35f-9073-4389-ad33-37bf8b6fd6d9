# app/service/ESmemory/es_news_query.py

from configs.lion_config import get_value
from service.ESmemory.es_memory_client import client

def search_news(keyword: str, size: int = 5, tags: list = None):
    """
    从ES新闻库中检索相关新闻
    :param keyword: 检索关键词
    :param size: 返回条数
    :param tags: 可选，标签过滤
    :return: 新闻列表
    """
    index_name = get_value("humanrelation.news_index_name", "humanrelation_news")
    query = {
        "bool": {
            "must": [
                {"multi_match": {
                    "query": keyword,
                    "fields": ["title", "content"]
                }}
            ]
        }
    }
    if tags:
        query["bool"]["filter"] = [{"terms": {"tags": tags}}]

    body = {
        "query": query,
        "size": size,
        "sort": [{"created_at": {"order": "desc"}}]
    }
    resp = client.search(index=index_name, body=body)
    hits = resp.get("hits", {}).get("hits", [])
    news_list = []
    for hit in hits:
        source = hit["_source"]
        news_list.append({
            "title": source.get("title"),
            "content": source.get("content"),
            "tags": source.get("tags"),
            "created_at": source.get("created_at"),
            "source_url": source.get("source_url")
        })
    return news_list

# 示例用法
if __name__ == "__main__":
    result = search_news("防骗案例", size=3)
    for news in result:
        print(f"标题: {news['title']}\n时间: {news['created_at']}\n链接: {news['source_url']}\n内容: {news['content'][:100]}...\n")