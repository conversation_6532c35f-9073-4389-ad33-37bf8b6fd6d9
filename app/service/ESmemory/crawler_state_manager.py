#!/usr/bin/env python3
"""
新闻爬虫状态管理工具
用于查看和管理爬虫的批次索引和关键词建议
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
app_dir = os.path.join(project_root, 'app')
if project_root not in sys.path:
    sys.path.append(project_root)
if app_dir not in sys.path:
    sys.path.append(app_dir)

from service.ESmemory.es_memory_client import client


def get_current_batch_index():
    """获取当前批次索引"""
    try:
        response = client.get(index="humanrelation_crawler_state", id="batch_index")
        return response["_source"]["current_batch_index"]
    except:
        return 0


def set_batch_index(index):
    """设置批次索引"""
    try:
        doc = {
            "current_batch_index": index,
            "updated_at": datetime.now().isoformat()
        }
        client.index(
            index="humanrelation_crawler_state",
            id="batch_index",
            body=doc,
            refresh=True
        )
        print(f"✅ 批次索引已设置为: {index}")
        return True
    except Exception as e:
        print(f"❌ 设置批次索引失败: {e}")
        return False


def get_keyword_suggestions(limit=10):
    """获取最近的关键词建议"""
    try:
        query = {
            "query": {"match_all": {}},
            "sort": [{"created_at": {"order": "desc"}}],
            "size": limit
        }
        
        response = client.search(index="humanrelation_crawler_state", body=query)
        suggestions = []
        
        for hit in response["hits"]["hits"]:
            if hit["_id"].startswith("keyword_suggestions_"):
                suggestions.append(hit["_source"])
        
        return suggestions
    except Exception as e:
        print(f"❌ 获取关键词建议失败: {e}")
        return []


def clear_old_suggestions(days=30):
    """清理旧的关键词建议"""
    try:
        from datetime import timedelta
        cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"prefix": {"_id": "keyword_suggestions_"}},
                        {"range": {"created_at": {"lt": cutoff_date}}}
                    ]
                }
            }
        }
        
        response = client.delete_by_query(index="humanrelation_crawler_state", body=query)
        deleted_count = response.get("deleted", 0)
        print(f"✅ 已清理 {deleted_count} 条超过 {days} 天的关键词建议")
        return deleted_count
    except Exception as e:
        print(f"❌ 清理关键词建议失败: {e}")
        return 0


def show_status():
    """显示当前状态"""
    print("📊 新闻爬虫状态信息")
    print("=" * 40)
    
    # 显示批次索引
    batch_index = get_current_batch_index()
    print(f"当前批次索引: {batch_index}")
    
    # 显示最近的关键词建议
    suggestions = get_keyword_suggestions(5)
    if suggestions:
        print(f"\n📝 最近的关键词建议 (共 {len(suggestions)} 条):")
        for i, suggestion in enumerate(suggestions, 1):
            timestamp = suggestion.get("timestamp", "未知时间")
            count = suggestion.get("count", 0)
            print(f"  {i}. {timestamp} - {count} 个关键词")
            
            # 显示前5个关键词
            keywords = suggestion.get("keywords", [])
            for j, keyword in enumerate(keywords[:5], 1):
                print(f"     {j}. {keyword}")
            if len(keywords) > 5:
                print(f"     ... 还有 {len(keywords) - 5} 个")
    else:
        print("\n📝 暂无关键词建议")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="新闻爬虫状态管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 显示状态
    subparsers.add_parser("status", help="显示当前状态")
    
    # 设置批次索引
    set_parser = subparsers.add_parser("set-batch", help="设置批次索引")
    set_parser.add_argument("index", type=int, help="批次索引")
    
    # 重置批次索引
    subparsers.add_parser("reset-batch", help="重置批次索引为0")
    
    # 查看关键词建议
    suggestions_parser = subparsers.add_parser("suggestions", help="查看关键词建议")
    suggestions_parser.add_argument("--limit", type=int, default=10, help="显示数量限制")
    
    # 清理旧建议
    clean_parser = subparsers.add_parser("clean", help="清理旧的关键词建议")
    clean_parser.add_argument("--days", type=int, default=30, help="保留天数")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == "status":
            show_status()
        elif args.command == "set-batch":
            set_batch_index(args.index)
        elif args.command == "reset-batch":
            set_batch_index(0)
        elif args.command == "suggestions":
            suggestions = get_keyword_suggestions(args.limit)
            if suggestions:
                print(f"📝 关键词建议 (最近 {len(suggestions)} 条):")
                for i, suggestion in enumerate(suggestions, 1):
                    timestamp = suggestion.get("timestamp", "未知时间")
                    keywords = suggestion.get("keywords", [])
                    print(f"\n{i}. {timestamp} ({len(keywords)} 个关键词):")
                    for j, keyword in enumerate(keywords, 1):
                        print(f"   {j}. {keyword}")
            else:
                print("📝 暂无关键词建议")
        elif args.command == "clean":
            clear_old_suggestions(args.days)
    except Exception as e:
        print(f"❌ 执行命令失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
