# 新闻抓取系统AI功能配置说明

## 概述

新闻抓取系统现已集成AI智能判断和关键词生成功能，能够自动筛选合适的人际关系相关新闻，并基于新闻内容生成新的搜索关键词。

## Lion配置项

### 配置项名称
`xiaomei.humanrelation.news_search_config`

### 完整配置示例
```json
{
  "taskConfig": {
    "searchResultCount": 5
  },
  "keywordList": [
    "亲子沟通案例",
    "夫妻关系咨询",
    "职场人际关系",
    "PUA案例分析",
    "朋友相处技巧"
  ],
  "aiConfig": {
    "enableAiJudgment": true,
    "enableKeywordGeneration": true
  }
}
```

## 配置参数说明

### taskConfig
- **searchResultCount**: 每个关键词最多抓取的新闻数量（默认5条）

### keywordList
- 搜索关键词数组，用于定向抓取特定领域的人际关系相关新闻

### aiConfig (新增)
- **enableAiJudgment**: 是否启用AI判断新闻合适性（默认true）
- **enableKeywordGeneration**: 是否启用AI生成新关键词（默认true）

## AI功能详解

### 1. AI新闻合适性判断
**功能**: 自动判断抓取的新闻是否适合人际关系管理领域

**判断标准**:
- 是否涉及人际交往、沟通技巧、关系处理
- 是否包含实用的建议或案例
- 内容是否积极正面，有助于改善人际关系
- 是否与家庭、职场、朋友等关系相关

**返回结果**:
```json
{
  "suitable": true/false,
  "reason": "判断理由",
  "confidence": 0.0-1.0
}
```

### 2. AI关键词生成
**功能**: 基于合适的新闻内容生成新的搜索关键词

**生成要求**:
- 关键词应该与新闻主题相关但角度不同
- 关键词应该有助于发现更多有价值的人际关系内容
- 关键词应该具体且可搜索
- 避免过于宽泛的词汇

**返回结果**:
```json
{
  "keywords": ["关键词1", "关键词2", "关键词3"]
}
```

## 数据存储增强

### ES文档结构更新
新闻文档现在包含以下新增字段：

```json
{
  "title": "新闻标题",
  "content": "新闻内容",
  "tags": [],
  "created_at": "2024-01-01 10:00:00",
  "updated_at": "2024-01-01 10:00:00",
  "source_url": "新闻链接",
  "ai_judgment": {
    "suitable": true,
    "reason": "判断理由",
    "confidence": 0.85
  },
  "generated_keywords": ["新关键词1", "新关键词2"]
}
```

## 使用场景

### 使用场景1: 智能内容筛选
- 系统自动抓取大量新闻
- AI判断筛选出真正相关的人际关系内容
- 避免存储无关或低质量的内容

### 使用场景2: 关键词扩展
- 基于高质量新闻自动生成新关键词
- 发现更多相关领域的内容
- 持续优化搜索策略

### 使用场景3: 质量控制
- 通过AI判断确保内容质量
- 记录判断理由和置信度
- 支持后续的质量分析和优化

## 值描述

该配置采用JSON格式，包含三个主要部分：
1. **taskConfig**: 控制抓取任务的基本参数
2. **keywordList**: 定义初始搜索关键词
3. **aiConfig**: 控制AI功能的启用状态

## 关联需求

- 支持动态调整AI判断标准，适应不同时期的内容需求
- 可配置AI功能的启用状态，平衡处理效率与质量
- 与RAG检索系统配合，提供更精准的新闻内容
- 支持关键词的自动更新和优化，提升内容发现能力
- 提供AI判断结果的统计分析，支持系统优化

## 注意事项

1. **性能考虑**: AI判断会增加处理时间，建议根据实际需求调整启用状态
2. **成本控制**: AI调用会产生费用，可通过配置控制使用频率
3. **容错处理**: 系统具备完善的容错机制，AI功能失败时不会影响基本抓取
4. **配置更新**: 新生成的关键词需要手动添加到配置中，系统会提供建议
