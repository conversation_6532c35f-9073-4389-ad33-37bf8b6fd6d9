import sys
import os
from datetime import datetime
from uuid import uuid4
import json
import time
from typing import List, Dict, Optional
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity


# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))  # es_news_crawler.py所在目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))  # 项目根目录
app_dir = os.path.join(project_root, 'app')
if project_root not in sys.path:
    sys.path.append(project_root)
if app_dir not in sys.path:
    sys.path.append(app_dir)

from configs.lion_config import get_value
from service.ESmemory.es_memory_client import client
from tools.webSearch import searchToolDict
from service.ai_client import send_to_ai  # 导入AI服务客户端
from vex.vex_thrift import embedding
from utils.logger import logger

# 移除不再需要的本地嵌入模型相关代码
# _embedding_model = None
# _embedding_model_initialized = False
# def get_embedding_model(): ...

def get_news_config():
    """从Lion获取新闻抓取配置"""
    try:
        # 使用正确的配置项名称
        search_config_str = get_value("xiaomei.humanrelation.news_search_config", "{}")

        # 解析配置
        search_config = json.loads(search_config_str) if search_config_str else {}

        # 提取配置中的关键信息
        task_config = search_config.get("taskConfig", {})
        keyword_list = search_config.get("keywordList", [])
        ai_configs = search_config.get("aiConfigs", {})

        # 构建关键词配置
        keywords_config = {
            "keywords": [
                {"keyword": keyword, "tags": keyword.split(), "enabled": True}
                for keyword in keyword_list
            ]
        }

        # 获取批处理大小
        batch_size = int(get_value("xiaomei.humanrelation.news_batch_size", "15"))

        # 构建爬虫配置
        crawler_config = {
            "news_index_name": get_value("humanrelation.news_index_name", "humanrelation_news_v3"),
            "max_results_per_keyword": task_config.get("searchResultCount", 5),
            "enable_crawling": True,
            "batch_size": batch_size,
            "enable_similarity_check": get_value("humanrelation.enable_similarity_check", "true").lower() == "true",
            "similarity_threshold": float(get_value("humanrelation.similarity_threshold", "0.85"))
        }

        # --- 从 'xiaomei.humanrelation.search_service_config' 动态加载搜索引擎 ---
        search_engines = []
        try:
            service_config_str = get_value("xiaomei.humanrelation.search_service_config", "{}")
            service_config = json.loads(service_config_str) if service_config_str else {}
            engine_status = service_config.get("SearchEngineStatus", {})
            if engine_status:
                search_engines = [engine for engine, enabled in engine_status.items() if enabled]
        except Exception as e:
            logger.error(f"⚠️ 解析 'xiaomei.humanrelation.search_service_config' 失败: {e}，将使用默认搜索引擎")
            search_engines = []

        # 如果从配置中未能获取任何启用的搜索引擎，则使用默认值
        if not search_engines:
            logger.warning("⚠️ 配置中未启用任何搜索引擎或解析失败，使用默认值: ['google_search', 'wenxin_search', 'tencent_search']")
            search_engines = ["google_search", "wenxin_search", "tencent_search"]

        # 构建AI配置
        ai_processing_config = {
            "enableAiJudgment": True,
            "enableKeywordGeneration": True,
            "model": get_value("xiaomei.humanrelation.news_ai_model", "gpt-4o-mini"),
            "search_engines": search_engines
        }

        return crawler_config, keywords_config, ai_processing_config
    except Exception as e:
        logger.error(f"⚠️ 从Lion获取配置失败，使用默认配置: {e}")
        # 使用默认配置
        default_config = {
            "news_index_name": "humanrelation_news_v3",
            "max_results_per_keyword": 5,
            "enable_crawling": True,
            "batch_size": 10,
            "enable_similarity_check": True,
            "similarity_threshold": 0.85
        }
        default_keywords = {
            "keywords": [
                {"keyword": "防骗案例", "tags": ["防骗", "案例"], "enabled": True},
                {"keyword": "PUA案例", "tags": ["PUA", "案例"], "enabled": True},
                {"keyword": "情感诈骗", "tags": ["情感", "诈骗"], "enabled": True},
                {"keyword": "亲子沟通案例", "tags": ["亲子", "沟通"], "enabled": True},
                {"keyword": "夫妻关系咨询", "tags": ["夫妻", "关系"], "enabled": True},
                {"keyword": "朋友相处技巧", "tags": ["朋友", "相处"], "enabled": True},
                {"keyword": "职场人际关系", "tags": ["职场", "人际"], "enabled": True}
            ]
        }
        default_ai_config = {
            "enableAiJudgment": True,
            "enableKeywordGeneration": True,
            "model": "gpt-4o-mini",
            "search_engines": ["bing_search"]
        }
        return default_config, default_keywords, default_ai_config

def embed_documents_batch(texts: List[str], expected_dims: int = 768) -> List[Optional[List[float]]]:
    """
    批量将文档文本转换为向量。
    新增了维度检查，确保返回的向量符合ES mapping要求。
    """
    embeddings = []
    for text in texts:
        try:
            # 假设 embedding 函数处理单个文本
            vec = embedding(text)
            if vec:
                if len(vec) != expected_dims:
                    logger.error(f"❌ 向量维度不匹配！期望维度: {expected_dims}, 实际得到: {len(vec)}. 文本: '{text[:50]}...'")
                    embeddings.append(None)  # 维度不匹配，视为向量化失败
                else:
                    embeddings.append(vec)  # 维度匹配，正常添加
            else:
                embeddings.append(None) # embedding返回None，直接添加
        except Exception as e:
            logger.error(f"文本向量化失败: '{text[:50]}...', 错误: {e}")
            embeddings.append(None)
    return embeddings


def ai_judge_news_suitability(news_content, news_title):
    """
    使用AI判断新闻是否适合人际关系管理领域
    :param news_content: 新闻内容
    :param news_title: 新闻标题
    :return: 判断结果字典
    """
    prompt = get_value("xiaomei.humanrelation.news_judgment_prompt", "")
    if not prompt:
        return {"suitable": True, "reason": "未配置判断提示词，默认适合", "confidence": 1.0}

    model = get_value("xiaomei.humanrelation.news_ai_model", "gpt-4o-mini")

    query_input = {
        "model": model,
        "messages": [
            {"role": "system", "content": prompt},
            {"role": "user", "content": f"标题: {news_title}\n\n内容: {news_content}"}
        ],
        "stream": False,
        "temperature": 0.0,
        "max_tokens": 500
    }

    response = send_to_ai(query_input, need_logger=False)
    if not response:
        return {"suitable": True, "reason": "AI服务调用失败，默认适合", "confidence": 0.5}

    try:
        response_data = json.loads(response.text)
        content = response_data["choices"][0]["message"]["content"].strip()

        # 尝试解析JSON响应
        judgment = json.loads(content)
        return judgment
    except (json.JSONDecodeError, KeyError, IndexError) as e:
        logger.warning(f"解析AI判断结果失败: {e}")
        return {"suitable": True, "reason": f"解析结果失败: {str(e)}", "confidence": 0.5}


def ai_generate_keywords(news_content, news_title, existing_tags):
    """
    使用AI基于新闻内容生成新的搜索关键词
    :param news_content: 新闻内容
    :param news_title: 新闻标题
    :param existing_tags: 已有标签
    :return: 生成的关键词列表
    """
    prompt = get_value("xiaomei.humanrelation.news_keyword_generation_prompt", "")
    if not prompt:
        return []

    model = get_value("xiaomei.humanrelation.news_ai_model", "gpt-4o-mini")

    query_input = {
        "model": model,
        "messages": [
            {"role": "system", "content": prompt},
            {"role": "user", "content": f"标题: {news_title}\n\n内容: {news_content}\n\n已有标签: {', '.join(existing_tags)}"}
        ],
        "stream": False,
        "temperature": 0.7,
        "max_tokens": 500
    }

    response = send_to_ai(query_input, need_logger=False)
    if not response:
        return []

    try:
        response_data = json.loads(response.text)
        content = response_data["choices"][0]["message"]["content"].strip()

        # 尝试解析JSON响应
        keyword_data = json.loads(content)
        return keyword_data.get("keywords", [])
    except (json.JSONDecodeError, KeyError, IndexError) as e:
        logger.warning(f"解析AI生成关键词失败: {e}")
        return []


def fetch_and_store_news(keyword: str, tags: list = None, max_results: int = 5, ai_config: dict = None):
    """
    搜索相关新闻并存入ES数据库，防止重复抓取（使用向量相似度判断重复内容）
    :param keyword: 搜索关键词
    :param tags: 标签列表
    :param max_results: 最多存储多少条新闻
    :param ai_config: AI功能配置
    """
    logger.info(f"🔍 正在抓取关键词: {keyword}")

    # 默认AI配置
    if ai_config is None:
        ai_config = {
            "enableAiJudgment": True,
            "enableKeywordGeneration": True,
            "search_engines": ["bing_search"]
        }

    # 获取配置的搜索引擎列表
    search_engines = ai_config.get("search_engines", ["bing_search"])

    # 执行多搜索引擎搜索
    all_results = []
    search_success_count = 0

    for engine in search_engines:
        if engine in searchToolDict:
            try:
                _, engine_results = searchToolDict[engine]._run(keyword)
                if engine_results:
                    all_results.extend(engine_results)
                    search_success_count += 1
            except Exception as e:
                logger.error(f"❌ {engine} 搜索失败: {e}")

    if not all_results:
        logger.error(f"❌ 所有搜索引擎都未返回结果，关键词: {keyword}")
        return {
            "keyword": keyword,
            "total_found": 0,
            "unique_found": 0,
            "processed": 0,
            "success_count": 0,
            "duplicate_count": 0,
            "unsuitable_count": 0,
            "error_count": 0,
            "generated_keywords": [],
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "search_engines_used": search_engines,
            "search_success_count": search_success_count
        }

    # 去重处理
    unique_urls = set()
    unique_results = []
    for result in all_results:
        url = result.get("link", "")
        if url and url not in unique_urls:
            unique_urls.add(url)
            unique_results.append(result)

    results = unique_results[:max_results]  # 限制结果数量

    # 统计数据
    stats = {
        "keyword": keyword,
        "total_found": len(all_results),
        "unique_found": len(unique_results),
        "processed": min(len(unique_results), max_results),
        "success_count": 0,
        "duplicate_count": 0,
        "unsuitable_count": 0,
        "error_count": 0,
        "generated_keywords": [],  # 新增：收集AI生成的关键词
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "search_engines_used": search_engines,
        "search_success_count": search_success_count
    }

    try:
        # 获取配置
        crawler_config, _, _ = get_news_config()
        enable_similarity_check = crawler_config.get("enable_similarity_check", True)
        similarity_threshold = crawler_config.get("similarity_threshold", 0.85)

        now_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        index_name = get_value("humanrelation.news_index_name", "humanrelation_news_v3")

        # 获取已有新闻的向量表示（仅在启用向量相似度检查时）
        existing_news_vectors = {}
        if enable_similarity_check:
            try:
                # 获取最近100条新闻用于相似度比较
                query = {"match_all": {}}
                es_resp = client.search(index=index_name, body={"query": query, "size": 100, "_source": ["title", "content", "vector"]})

                existing_docs = []
                for hit in es_resp.get("hits", {}).get("hits", []):
                    source = hit["_source"]
                    doc_id = hit["_id"]
                    if "vector" in source and source["vector"]:
                        existing_news_vectors[doc_id] = source["vector"]
                    else:
                        existing_docs.append({
                            "doc_id": doc_id,
                            "text": f"{source.get('title', '')} {source.get('content', '')[:500]}"
                        })

                # 批量为没有向量的文档生成向量
                if existing_docs:
                    texts_to_embed = [doc["text"] for doc in existing_docs]
                    vectors = embed_documents_batch(texts_to_embed)
                    for doc, vector in zip(existing_docs, vectors):
                        if vector:
                            existing_news_vectors[doc["doc_id"]] = vector

            except Exception as e:
                logger.error(f"获取已有新闻向量失败: {e}")
                existing_news_vectors = {}

        success_count = 0
        duplicate_count = 0
        unsuitable_count = 0  # 新增：不适合的新闻计数
        error_count = 0       # 新增：错误计数
        all_generated_keywords = set()  # 新增：用集合收集所有生成的关键词，避免重复

        # 准备批量处理新新闻
        news_to_process = []
        for news in results[:max_results]:
            if not news:
                continue
            source_url = news.get("link", "")
            if not source_url:
                continue

            # 快速URL去重
            query = {"query": {"term": {"source_url.keyword": source_url}}}
            es_resp = client.search(index=index_name, body=query, size=1)
            if es_resp.get("hits", {}).get("total", {}).get("value", 0) > 0:
                duplicate_count += 1
                continue

            news_to_process.append(news)

        # 批量向量化
        if not news_to_process:
            logger.info(f"关键词 '{keyword}' 没有新的新闻需要处理。")
            return stats

        texts_to_embed = [f"{news.get('title', '')} {(news.get('crawled_content') or news.get('snippet') or '')[:500]}" for news in news_to_process]
        news_vectors = embed_documents_batch(texts_to_embed)

        for news, current_vector in zip(news_to_process, news_vectors):
            if not current_vector:
                error_count += 1
                continue

            title = news.get("title", "")
            content = news.get("crawled_content") or news.get("snippet") or ""

            # 向量相似度检查
            if enable_similarity_check and existing_news_vectors:
                try:
                    # 获取当前向量的维度
                    current_dim = len(current_vector)
                    # 仅筛选出维度相同且有效的向量进行比较，防止因数据不一致导致报错
                    valid_existing_vectors = [
                        v for v in existing_news_vectors.values()
                        if isinstance(v, list) and len(v) == current_dim
                    ]

                    # 如果没有可供比较的有效向量，则跳过检查
                    if not valid_existing_vectors:
                        pass
                    else:
                        similarities = cosine_similarity([current_vector], valid_existing_vectors)[0]
                        if np.max(similarities) > similarity_threshold:
                            duplicate_count += 1
                            continue
                except Exception as e:
                    # 增加错误日志，记录下导致问题的向量信息以便排查
                    logger.warning(f"相似度检查失败: {e}. Current vector dim: {len(current_vector)}")

            # AI判断和关键词生成
            ai_judgment = {"suitable": True, "reason": "默认适合", "confidence": 1.0}
            generated_keywords = []

            if ai_config.get("enableAiJudgment", True):
                ai_judgment = ai_judge_news_suitability(content, title)
                if not ai_judgment.get("suitable", True):
                    unsuitable_count += 1
                    continue

            if ai_config.get("enableKeywordGeneration", True):
                generated_keywords = ai_generate_keywords(content, title, tags or [])
                if generated_keywords:
                    all_generated_keywords.update(generated_keywords)

            # 构建文档并写入
            doc = {
                "title": title,
                "content": content,
                "tags": tags or [],
                "created_at": now_str,
                "updated_at": now_str,
                "source_url": news.get("link", ""),
                "source_keyword": keyword,
                "ai_judgment": ai_judgment,
                "generated_keywords": generated_keywords,
                "vector": current_vector
            }

            doc_id = str(uuid4())
            try:
                client.index(index=index_name, id=doc_id, body=doc, refresh=True)
                if enable_similarity_check:
                    existing_news_vectors[doc_id] = current_vector
                success_count += 1
            except Exception as e:
                logger.error(f"❌ 写入ES失败: {e}")
                error_count += 1

        # 更新统计数据
        stats["success_count"] = success_count
        stats["duplicate_count"] = duplicate_count
        stats["unsuitable_count"] = unsuitable_count
        stats["error_count"] = error_count
        stats["generated_keywords"] = list(all_generated_keywords)  # 转换为列表保存

        # 保存统计数据到ES
        try:
            stats_index_name = "humanrelation_news_stats"
            client.index(index=stats_index_name, body=stats, refresh=True)

            # 如果有新生成的关键词，保存到专门的索引中
            if all_generated_keywords:
                keywords_index_name = "humanrelation_generated_keywords"
                keywords_doc = {
                    "source_keyword": keyword,
                    "generated_keywords": list(all_generated_keywords),
                    "timestamp": datetime.now().isoformat(),
                    "used": False  # 标记是否已被使用
                }
                client.index(index=keywords_index_name, body=keywords_doc, refresh=True)
        except Exception as e:
            logger.error(f"保存统计数据失败: {e}")

        # 在处理结束时添加详细统计日志
        logger.info(f"📊 关键词 '{keyword}' 处理完成: 成功 {success_count}/{min(len(results), max_results)} 条")

        # 添加详细的失败统计
        if duplicate_count > 0:
            logger.info(f"   🔄 重复内容跳过: {duplicate_count} 条")
        if unsuitable_count > 0:
            logger.info(f"   ❌ AI判断不适合: {unsuitable_count} 条")
        if error_count > 0:
            logger.info(f"   💥 处理错误: {error_count} 条")

        # 计算总的处理数量
        total_processed = success_count + duplicate_count + unsuitable_count + error_count
        logger.info(f"   📋 总计处理: {total_processed} 条")

        # 返回统计结果
        return stats

    except Exception as e:
        logger.error(f"💥 抓取关键词 '{keyword}' 时发生错误: {e}")
        # 记录错误统计
        stats["error_count"] = 1
        stats["error_message"] = str(e)
        try:
            stats_index_name = "humanrelation_news_stats"
            client.index(index=stats_index_name, body=stats, refresh=True)
        except:
            pass

        # 返回错误统计结果
        return stats

def get_and_process_generated_keywords():
    """
    获取并处理AI生成的关键词，将其添加到搜索队列中
    :return: 新添加的关键词列表
    """
    try:
        # 查询未使用的生成关键词
        keywords_index_name = "humanrelation_generated_keywords"
        query = {
            "bool": {
                "must": [
                    {"term": {"used": False}}
                ]
            }
        }

        # 获取最多100条记录
        resp = client.search(
            index=keywords_index_name,
            body={
                "query": query,
                "size": 100,
                "sort": [{"timestamp": {"order": "desc"}}]
            }
        )

        if not resp.get("hits", {}).get("hits", []):
            return []

        # 从Lion获取当前配置
        search_config_str = get_value("xiaomei.humanrelation.news_search_config", "{}")
        search_config = json.loads(search_config_str) if search_config_str else {}

        # 获取当前关键词列表
        current_keywords = set(search_config.get("keywordList", []))

        # 收集新的关键词
        new_keywords = set()
        processed_ids = []

        for hit in resp["hits"]["hits"]:
            doc = hit["_source"]
            doc_id = hit["_id"]
            generated_keywords = doc.get("generated_keywords", [])

            # 过滤出尚未在配置中的关键词
            filtered_keywords = [k for k in generated_keywords if k not in current_keywords and len(k) >= 4]

            if filtered_keywords:
                new_keywords.update(filtered_keywords)
                processed_ids.append(doc_id)

                # 更新文档状态为已使用
                client.update(
                    index=keywords_index_name,
                    id=doc_id,
                    body={"doc": {"used": True}}
                )

        # 如果有新关键词，更新配置
        if new_keywords:
            # 限制添加的新关键词数量，避免一次性添加太多
            new_keywords_list = list(new_keywords)[:20]  # 最多添加20个新关键词

            # 更新配置
            updated_keywords = list(current_keywords) + new_keywords_list
            search_config["keywordList"] = updated_keywords

            # 保存回Lion配置
            try:
                # 将建议保存到ES存储中，方便管理员查看
                suggestion_data = {
                    "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "count": len(new_keywords_list),
                    "keywords": new_keywords_list
                }
                save_keyword_suggestions_to_storage(suggestion_data)
            except Exception as e:
                pass

            return new_keywords_list
        else:
            return []

    except Exception as e:
        return []

def get_batch_index_from_storage():
    """从持久化存储获取批次索引"""
    try:
        response = client.get(index="humanrelation_crawler_state", id="batch_index")
        return response["_source"]["current_batch_index"]
    except:
        return 0

def save_batch_index_to_storage(batch_index):
    """保存批次索引到持久化存储"""
    try:
        doc = {
            "current_batch_index": batch_index,
            "updated_at": datetime.now().isoformat()
        }
        client.index(
            index="humanrelation_crawler_state",
            id="batch_index",
            body=doc,
            refresh=True
        )
        return True
    except Exception as e:
        return False

def save_keyword_suggestions_to_storage(suggestions):
    """保存关键词建议到持久化存储"""
    try:
        doc = {
            "suggestions": suggestions,
            "created_at": datetime.now().isoformat()
        }
        client.index(
            index="humanrelation_crawler_state",
            id=f"keyword_suggestions_{int(time.time())}",
            body=doc,
            refresh=True
        )
        return True
    except Exception as e:
        return False

def main():
    print("🚀 开始新闻抓取与入库任务...")

    # 从Lion获取配置
    config, keywords_config, ai_config = get_news_config()

    # 检查是否启用抓取
    if not config.get("enable_crawling", True):
        print("⚠️ 新闻抓取功能已禁用，退出任务")
        return

    # 获取配置参数
    max_results = config.get("max_results_per_keyword", 5)
    keywords = keywords_config.get("keywords", [])

    # 获取批处理大小配置
    batch_size = config.get("batch_size", 15)  # 默认每批15个关键词

    # 获取当前批次索引（从ES存储）
    current_batch_index = get_batch_index_from_storage()

    # 计算总批次数
    total_batches = (len(keywords) + batch_size - 1) // batch_size  # 向上取整

    # 计算当前批次的关键词
    start_idx = (current_batch_index % total_batches) * batch_size
    end_idx = min(start_idx + batch_size, len(keywords))
    current_batch = keywords[start_idx:end_idx]

    print(f"📋 处理第 {current_batch_index % total_batches + 1}/{total_batches} 批关键词 ({len(current_batch)}个)")

    # 处理当前批次的关键词
    for i, keyword_item in enumerate(current_batch, 1):
        if keyword_item.get("enabled", True):
            keyword = keyword_item["keyword"]
            tags = keyword_item["tags"]
            print(f"[{i}/{len(current_batch)}] 处理关键词: {keyword}")
            fetch_and_store_news(keyword, tags, max_results, ai_config)

    # 更新批次索引（保存到ES存储）
    next_batch_index = current_batch_index + 1
    save_batch_index_to_storage(next_batch_index)

    # 每处理完5个批次，检查一次生成的关键词
    if current_batch_index % 5 == 0:
        new_keywords = get_and_process_generated_keywords()
        if new_keywords:
            print(f"✅ 发现 {len(new_keywords)} 个新关键词建议")

    print(f"🎉 本批次完成！下次将处理第 {next_batch_index % total_batches + 1}/{total_batches} 批")

def main_loop():
    """
    定时任务主循环，分批处理关键词
    """
    # 从Lion获取爬取间隔配置（单位：分钟）
    try:
        crawl_interval_minutes = int(get_value("xiaomei.humanrelation.news_crawl_interval", "60"))
    except:
        crawl_interval_minutes = 60  # 默认1小时

    crawl_interval_seconds = crawl_interval_minutes * 60

    print(f"🕒 新闻爬取定时任务已启动，间隔: {crawl_interval_minutes} 分钟")

    while True:
        print(f"\n定时任务启动：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        try:
            main()
        except Exception as e:
            print(f"❌ 爬取任务执行失败: {e}")

        print(f"休眠 {crawl_interval_minutes} 分钟...")
        time.sleep(crawl_interval_seconds)

if __name__ == "__main__":
    main_loop()
