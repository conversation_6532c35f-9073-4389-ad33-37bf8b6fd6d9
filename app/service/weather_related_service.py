"""
天气相关信息服务
获取与天气相关的人物档案和事件信息，用于天气播报
"""

import json
import re
from datetime import datetime, timedelta
from typing import Dict, List

from configs.lion_config import get_value
from service.ESmemory.es_event_service import get_recent_events, search_events_by_text
from service.mysql_person_service import get_all_persons_mysql
from utils.logger import logger


def get_weather_related_persons_and_events(user_id: str) -> Dict:
    """
    获取与天气相关的人物档案和事件信息
    
    Args:
        user_id: 用户ID
        
    Returns:
        包含相关人物档案和事件的字典
    """
    try:
        # 获取与天气相关的人物档案
        related_persons = _get_weather_related_persons(user_id)
        
        # 获取与天气相关的事件
        related_events = _get_weather_related_events(user_id)
        
        return {
            "result": "success",
            "user_id": user_id,
            "timestamp": datetime.now().isoformat(),
            "weather_related_persons": related_persons,
            "weather_related_events": related_events,
            "summary": {
                "total_persons": len(related_persons),
                "total_events": len(related_events)
            }
        }
        
    except Exception as e:
        logger.error(f"获取天气相关信息失败: {e}")
        return {
            "result": "error", 
            "reason": f"获取天气相关信息失败: {str(e)}"
        }


def _get_weather_related_persons(user_id: str) -> List[Dict]:
    """
    获取与天气相关的人物档案（主要是亲人，且有居住地信息的）
    
    Args:
        user_id: 用户ID
        
    Returns:
        相关人物档案列表
    """
    related_persons = []
    
    try:
        # 获取所有人物档案
        persons_result = get_all_persons_mysql(user_id)
        
        if persons_result.get("result") != "success":
            logger.warning(f"获取人物档案失败: {persons_result.get('reason', '未知错误')}")
            return related_persons
            
        persons = persons_result.get("persons", [])
        
        # 定义亲人关系类型
        family_relationships = {
            "父亲", "母亲", "爸爸", "妈妈", "父母",
            "配偶", "妻子", "丈夫", "老公", "老婆", "爱人",
            "儿子", "女儿", "孩子", "子女",
            "兄弟", "姐妹", "哥哥", "弟弟", "姐姐", "妹妹",
            "祖父", "祖母", "爷爷", "奶奶", "外公", "外婆",
            "岳父", "岳母", "公公", "婆婆"
        }
        
        for person in persons:
            # 检查是否为亲人关系
            if _is_family_member(person, family_relationships):
                # 检查是否有居住地信息
                location_info = _extract_location_from_person(person)
                if location_info:
                    related_persons.append({
                        "person_id": person.get("person_id"),
                        "canonical_name": person.get("canonical_name"),
                        "relationship": _get_person_relationship(person),
                        "profile_summary": person.get("profile_summary", ""),
                        "location_info": location_info
                    })
                    
        logger.info(f"找到 {len(related_persons)} 个有居住地信息的亲人档案")
        
    except Exception as e:
        logger.error(f"获取天气相关人物档案失败: {e}")
        
    return related_persons


def _get_weather_related_events(user_id: str) -> List[Dict]:
    """
    获取与天气相关的事件（出行计划、户外活动等）
    
    Args:
        user_id: 用户ID
        
    Returns:
        相关事件列表
    """
    related_events = []
    
    try:
        event_index = get_value("humanrelation.event_index_name", "memory_event_store")
        
        # 获取最近的事件
        events_result = get_recent_events(event_index, user_id, size=100)
        
        if events_result.get("result") != "success":
            logger.warning(f"获取事件失败: {events_result.get('reason', '未知错误')}")
            return related_events
            
        events = events_result.get("events", [])
        
        # 过滤出与天气相关的事件
        for event in events:
            if _is_weather_related_event(event):
                related_events.append({
                    "event_id": event.get("event_id"),
                    "description_text": event.get("description_text", ""),
                    "location": event.get("location", ""),
                    "timestamp": event.get("timestamp"),
                    "participants": event.get("participants", [])
                })
                
        logger.info(f"找到 {len(related_events)} 个与天气相关的事件")
        
    except Exception as e:
        logger.error(f"获取天气相关事件失败: {e}")
        
    return related_events


def _is_family_member(person: Dict, family_relationships: set) -> bool:
    """
    判断是否为家庭成员
    
    Args:
        person: 人物档案
        family_relationships: 家庭关系集合
        
    Returns:
        是否为家庭成员
    """
    try:
        # 检查relationships字段
        relationships = person.get("relationships", [])
        if isinstance(relationships, str):
            try:
                relationships = json.loads(relationships)
            except:
                relationships = []
                
        if isinstance(relationships, list):
            for rel in relationships:
                if isinstance(rel, dict):
                    rel_type = rel.get("type", "")
                    if rel_type in family_relationships:
                        return True
                        
        # 检查key_attributes中的关系字段
        key_attributes = person.get("key_attributes", {})
        if isinstance(key_attributes, str):
            try:
                key_attributes = json.loads(key_attributes)
            except:
                key_attributes = {}
                
        if isinstance(key_attributes, dict):
            relation = key_attributes.get("关系", "")
            if relation in family_relationships:
                return True
                
    except Exception as e:
        logger.debug(f"判断家庭成员关系失败: {e}")
        
    return False


def _extract_location_from_person(person: Dict) -> Dict:
    """
    从人物档案中提取居住地信息
    
    Args:
        person: 人物档案
        
    Returns:
        居住地信息字典
    """
    location_info = {}
    
    try:
        key_attributes = person.get("key_attributes", {})
        if isinstance(key_attributes, str):
            try:
                key_attributes = json.loads(key_attributes)
            except:
                key_attributes = {}
                
        if isinstance(key_attributes, dict):
            # 查找地址相关字段
            location_fields = [
                "居住地", "居住地点", "现居地", "所在地", "地点",
                "工作地点", "生活地点", "常住地址", "家庭地址",
                "当前城市", "城市"
            ]
            
            for field in location_fields:
                if field in key_attributes and key_attributes[field]:
                    location_name = str(key_attributes[field]).strip()
                    if location_name:
                        location_info[field] = location_name
                        
            # 检查基本信息中的地址
            basic_info = key_attributes.get("基本信息", {})
            if isinstance(basic_info, dict):
                if "当前城市" in basic_info and basic_info["当前城市"]:
                    location_info["当前城市"] = str(basic_info["当前城市"]).strip()
                if "家乡" in basic_info and basic_info["家乡"]:
                    location_info["家乡"] = str(basic_info["家乡"]).strip()
                    
    except Exception as e:
        logger.debug(f"提取居住地信息失败: {e}")
        
    return location_info


def _get_person_relationship(person: Dict) -> str:
    """
    获取人物关系描述
    
    Args:
        person: 人物档案
        
    Returns:
        关系描述
    """
    try:
        # 优先从relationships字段获取
        relationships = person.get("relationships", [])
        if isinstance(relationships, str):
            try:
                relationships = json.loads(relationships)
            except:
                relationships = []
                
        if isinstance(relationships, list) and relationships:
            for rel in relationships:
                if isinstance(rel, dict) and rel.get("type"):
                    return rel.get("type")
                    
        # 从key_attributes获取
        key_attributes = person.get("key_attributes", {})
        if isinstance(key_attributes, str):
            try:
                key_attributes = json.loads(key_attributes)
            except:
                key_attributes = {}
                
        if isinstance(key_attributes, dict):
            relation = key_attributes.get("关系", "")
            if relation:
                return relation
                
    except Exception as e:
        logger.debug(f"获取人物关系失败: {e}")
        
    return "未知关系"


def _is_weather_related_event(event: Dict) -> bool:
    """
    判断事件是否与天气相关
    
    Args:
        event: 事件数据
        
    Returns:
        是否与天气相关
    """
    try:
        description = event.get("description_text", "").lower()
        location = event.get("location", "").lower()
        
        # 天气相关关键词
        weather_keywords = [
            # 出行相关
            "出行", "旅行", "旅游", "出差", "去", "到", "回",
            "打球", "运动", "跑步", "散步", "爬山", "徒步",
            "看电影", "逛街", "购物", "聚餐", "聚会",
            "户外", "室外", "公园", "海边", "山上",
            # 天气相关
            "天气", "下雨", "晴天", "阴天", "雪", "风",
            "温度", "冷", "热", "暖", "凉",
            # 时间相关（未来计划）
            "明天", "后天", "下周", "下个月", "计划", "准备",
            "要去", "将要", "打算"
        ]
        
        # 检查描述文本
        for keyword in weather_keywords:
            if keyword in description:
                return True
                
        # 检查是否有地点信息（有地点的事件通常与天气相关）
        if location:
            return True
            
        # 检查是否为未来事件（通过时间戳或描述判断）
        if _is_future_event(event):
            return True
            
    except Exception as e:
        logger.debug(f"判断天气相关事件失败: {e}")
        
    return False


def _is_future_event(event: Dict) -> bool:
    """
    判断是否为未来事件
    
    Args:
        event: 事件数据
        
    Returns:
        是否为未来事件
    """
    try:
        description = event.get("description_text", "")
        
        # 检查描述中的时间关键词
        future_keywords = ["明天", "后天", "下周", "下个月", "将要", "计划", "准备"]
        for keyword in future_keywords:
            if keyword in description:
                return True
                
        # 检查日期格式 (YYYY-MM-DD)
        date_pattern = r"(\d{4}-\d{2}-\d{2})"
        matches = re.findall(date_pattern, description)
        
        for match in matches:
            try:
                event_date = datetime.strptime(match, "%Y-%m-%d").date()
                today = datetime.now().date()
                if event_date >= today:
                    return True
            except:
                continue
                
    except Exception as e:
        logger.debug(f"判断未来事件失败: {e}")
        
    return False
