########################################################
# MySQL人员服务 - 替代ES人员服务
########################################################

import json
from uuid import uuid4

from my_mysql.entity.person_table import (
    delete_person,
    get_all_persons,
    get_person_by_id,
    insert_person,
    person_memory,
    search_persons_by_name,
    update_person,
)
from my_mysql.sql_client import CLIENT
from sqlalchemy import select
from utils.logger import logger


def add_person(
    user_id: str,
    canonical_name: str = "",
    aliases=None,  # 可以是字符串或列表
    relationships: list = [],
    profile_summary: str = "",
    key_attributes: dict = {},
    avatar: str = "",
    is_user: bool = False,
    person_id: str = "",
    intimacy_score: int = 0,
):  # 添加人员到MySQL
    if not person_id:
        person_id = str(uuid4())

    # 数据库插入流程日志
    logger.info(f"[数据库插入] add_person接收参数 - 人物: {canonical_name}")
    logger.info(f"[数据库插入] aliases: {aliases} (type: {type(aliases)})")
    logger.info(f"[数据库插入] relationships: {relationships} (type: {type(relationships)})")
    logger.info(f"[数据库插入] key_attributes: {type(key_attributes)}")

    try:
        success = insert_person(
            user_id=user_id,
            person_id=person_id,
            is_user=is_user,
            canonical_name=canonical_name,
            aliases=aliases,
            relationships=relationships,
            profile_summary=profile_summary,
            key_attributes=key_attributes,
            avatar=avatar,
            intimacy_score=intimacy_score,
        )
        if success:
            logger.info(f"MySQL添加人员成功: {person_id} for user: {user_id}")

            # 同步到ES
            person_data = {
                "user_id": user_id,
                "canonical_name": canonical_name,
                "aliases": aliases,
                "relationships": relationships,
                "profile_summary": profile_summary,
                "key_attributes": key_attributes,
                "avatar": avatar,
                "is_user": is_user,
            }
            sync_person_to_es(person_id, person_data)

            return {"result": "success", "person_id": person_id}
        else:
            return {"result": "error", "reason": "插入失败"}
    except Exception as e:
        logger.error(f"MySQL添加人员失败: {str(e)}")
        return {"result": "error", "reason": str(e)}


def get_all_persons_mysql(user_id: str, limit: int = 100, offset: int = 0):  # 获取所有人员列表
    try:
        persons = get_all_persons(user_id=user_id, limit=limit, offset=offset)
        processed_persons = []
        default_attributes = {"关系": "", "年龄": "", "职业": ""}

        for person in persons:
            # 确保 key_attributes 不是 None
            raw_attributes = person.get("key_attributes")
            if raw_attributes:
                # 确保是字典而非JSON字符串
                key_attributes = json.loads(raw_attributes) if isinstance(raw_attributes, str) else raw_attributes
            else:
                key_attributes = {}

            # 补全必需字段
            person["key_attributes"] = {**default_attributes, **key_attributes}

            # 确保 relationships 是列表或null
            raw_relationships = person.get("relationships")
            if raw_relationships:
                person["relationships"] = (
                    json.loads(raw_relationships) if isinstance(raw_relationships, str) else raw_relationships
                )
            else:
                person["relationships"] = None  # 遵从接口定义，返回null

            processed_persons.append(person)

        logger.info(f"MySQL获取人员列表成功，共{len(processed_persons)}人 for user: {user_id}")
        return {"result": "success", "persons": processed_persons}
    except Exception as e:
        logger.error(f"MySQL获取人员列表失败: {str(e)}")
        return {"result": "error", "reason": str(e), "persons": []}


def get_person_by_id_mysql(user_id: str, person_id: str):  # 根据ID获取人员详情
    try:
        person = get_person_by_id(user_id=user_id, person_id=person_id)
        if person:
            logger.info(f"MySQL获取人员详情成功: {person_id} for user: {user_id}")
            return {"result": "success", "person": person}
        else:
            return {"result": "error", "reason": "人员不存在", "person": None}
    except Exception as e:
        logger.error(f"MySQL获取人员详情失败: {str(e)}")
        return {"result": "error", "reason": str(e), "person": None}


def update_person_mysql(user_id: str, person_id: str, **kwargs):  # 更新人员信息
    try:
        success = update_person(user_id=user_id, person_id=person_id, **kwargs)
        if success:
            logger.info(f"MySQL更新人员成功: {person_id} for user: {user_id}")

            # 获取更新后的人员信息并同步到ES
            try:
                person_result = get_person_by_id_mysql(user_id, person_id)
                if person_result.get("result") == "success" and person_result.get("person"):
                    sync_person_to_es(person_id, person_result["person"])
            except Exception as sync_e:
                logger.error(f"更新后同步到ES失败: {sync_e}")

            return {"result": "success"}
        else:
            return {"result": "error", "reason": "更新失败或人员不存在"}
    except Exception as e:
        logger.error(f"MySQL更新人员失败: {str(e)}")
        return {"result": "error", "reason": str(e)}


def search_persons_by_name_mysql(user_id: str, name: str, limit: int = 10):  # 按姓名搜索人员（优化版）
    try:
        from my_mysql import sql_client
        from my_mysql.entity.person_table import person_memory
        from sqlalchemy import or_, select

        # 先尝试精确匹配
        exact_stmt = (
            select(person_memory)
            .where(person_memory.c.user_id == user_id, person_memory.c.canonical_name == name)
            .limit(limit)
        )

        results = sql_client.select_many(exact_stmt)

        # 如果精确匹配无结果，再进行模糊匹配
        if not results and len(name) > 1:
            fuzzy_stmt = (
                select(person_memory)
                .where(
                    person_memory.c.user_id == user_id,
                    or_(person_memory.c.canonical_name.like(f"%{name}%"), person_memory.c.aliases.like(f"%{name}%")),
                )
                .limit(limit)
            )
            results = sql_client.select_many(fuzzy_stmt)

        processed_persons = []
        for row in results:
            person = dict(row._mapping)
            try:
                if person.get("relationships"):
                    person["relationships"] = json.loads(person["relationships"])
                if person.get("key_attributes"):
                    person["key_attributes"] = json.loads(person["key_attributes"])
            except (json.JSONDecodeError, TypeError):
                person["relationships"] = []
                person["key_attributes"] = {}
            processed_persons.append(person)

        # 预览信息（只显示前3个人的姓名）
        preview = [p.get("canonical_name", "未知") for p in processed_persons[:3]]
        if len(processed_persons) > 3:
            preview.append(f"...等{len(processed_persons)}人")

        logger.info(
            f"MySQL搜索人员成功，关键词:{name}，结果数:{len(processed_persons)} for user: {user_id}，预览:{preview}"
        )
        return {"result": "success", "persons": processed_persons}
    except Exception as e:
        logger.error(f"MySQL搜索人员失败: {str(e)}")
        return {"result": "error", "reason": str(e), "persons": []}


def delete_person_mysql(user_id: str, person_id: str):  # 删除人员
    try:
        success = delete_person(user_id=user_id, person_id=person_id)
        if success:
            logger.info(f"MySQL删除人员成功: {person_id} for user: {user_id}")

            # 从ES中删除对应的人物档案
            delete_person_from_es(person_id)

            return {"result": "success"}
        else:
            return {"result": "error", "reason": "删除失败或人员不存在"}
    except Exception as e:
        logger.error(f"MySQL删除人员失败: {str(e)}")
        return {"result": "error", "reason": str(e)}


def search_persons_by_alias(user_id: str, alias: str, limit: int = 10):  # 按别名搜索人员
    try:
        from my_mysql import sql_client
        from my_mysql.entity.person_table import person_memory
        from sqlalchemy import select

        stmt = (
            select(person_memory)
            .where(person_memory.c.user_id == user_id, person_memory.c.aliases.like(f"%{alias}%"))
            .limit(limit)
        )
        results = sql_client.select_many(stmt)
        persons = []
        for row in results:
            person = dict(row._mapping)
            if person["relationships"]:
                person["relationships"] = json.loads(person["relationships"])
            if person["key_attributes"]:
                person["key_attributes"] = json.loads(person["key_attributes"])
            persons.append(person)
        logger.info(f"MySQL按别名搜索人员成功，关键词:{alias}，结果数:{len(persons)} for user: {user_id}")
        return {"result": "success", "persons": persons}
    except Exception as e:
        logger.error(f"MySQL按别名搜索人员失败: {str(e)}")
        return {"result": "error", "reason": str(e), "persons": []}


def search_persons_by_attributes_mysql(user_id: str, search_attributes: list, limit: int = 10):
    """按属性检索人物，支持反向属性查询"""
    try:
        from sqlalchemy import and_, or_, text

        conn = CLIENT.connect()
        try:
            # 构建查询条件
            conditions = [person_memory.c.user_id == user_id]

            for attr in search_attributes:
                if not attr:
                    continue

                # 在profile_summary中搜索
                profile_condition = person_memory.c.profile_summary.like(f"%{attr}%")

                # 在key_attributes JSON字段中搜索
                # 使用JSON_SEARCH函数查找JSON中的值
                json_condition = text(f"JSON_SEARCH(key_attributes, 'one', '%{attr}%') IS NOT NULL")

                # 组合条件
                conditions.append(or_(profile_condition, json_condition))

            # 执行查询
            stmt = select(person_memory).where(and_(*conditions)).limit(limit)
            results = conn.execute(stmt).fetchall()

            # 处理结果
            processed_persons = []
            for result in results:
                person = dict(result._mapping)
                # 处理JSON字段
                if person.get("key_attributes"):
                    try:
                        person["key_attributes"] = json.loads(person["key_attributes"])
                    except (json.JSONDecodeError, TypeError):
                        person["key_attributes"] = {}
                if person.get("relationships"):
                    try:
                        person["relationships"] = json.loads(person["relationships"])
                    except (json.JSONDecodeError, TypeError):
                        person["relationships"] = []
                processed_persons.append(person)

            logger.info(f"MySQL属性检索成功，找到{len(processed_persons)}个匹配的人物")
            return {"result": "success", "persons": processed_persons}

        finally:
            conn.close()
    except Exception as e:
        logger.error(f"MySQL属性检索失败: {str(e)}")
        return {"result": "error", "reason": str(e), "persons": []}


def get_user_person(user_id: str):
    """获取用户本人的档案"""
    try:
        conn = CLIENT.connect()
        try:
            stmt = select(person_memory).where(
                (person_memory.c.user_id == user_id) & (person_memory.c.is_user.is_(True))
            )
            result = conn.execute(stmt).fetchone()
            if result:
                person = dict(result._mapping)
                # 处理JSON字段
                if person.get("relationships"):
                    person["relationships"] = json.loads(person["relationships"])
                if person.get("key_attributes"):
                    person["key_attributes"] = json.loads(person["key_attributes"])
                return person
            return None
        finally:
            conn.close()
    except Exception as e:
        logger.error(f"获取用户本人档案失败: {e}")
        return None


def ensure_user_profile_exists(user_id: str, user_name: str = None):
    """确保用户本人的档案存在，如果不存在则创建"""
    try:
        # 检查是否已存在用户本人档案
        existing_user = get_user_person(user_id)
        if existing_user:
            logger.info(f"用户 {user_id} 的本人档案已存在: {existing_user['person_id']}")
            return {"result": "success", "person_id": existing_user["person_id"], "action": "exists"}

        # 创建用户本人档案
        person_id = f"user_{user_id}_{str(uuid4())[:8]}"  # 特殊的person_id格式
        canonical_name = user_name or f"用户_{user_id}"  # 默认名称

        # 用户本人的特殊属性
        key_attributes = {"身份": "系统用户", "创建方式": "系统自动创建"}

        profile_summary = "这是您的个人档案，系统会在这里记录与您相关的信息。"

        success = insert_person(
            user_id=user_id,
            person_id=person_id,
            is_user=True,  # 关键：标记为用户本人
            canonical_name=canonical_name,
            aliases="我",  # 添加"我"作为别名
            relationships=[],
            profile_summary=profile_summary,
            key_attributes=key_attributes,
            avatar="",
        )

        if success:
            logger.info(f"为用户 {user_id} 创建本人档案成功: {person_id}")
            return {"result": "success", "person_id": person_id, "action": "created"}
        else:
            logger.error(f"为用户 {user_id} 创建本人档案失败")
            return {"result": "error", "reason": "创建档案失败"}

    except Exception as e:
        logger.error(f"确保用户本人档案存在失败: {e}")
        return {"result": "error", "reason": str(e)}


def sync_person_to_es(person_id: str, person_data: dict):
    """将人物档案同步到ES，作为可检索的人物档案记忆"""
    try:
        from datetime import datetime

        from configs.lion_config import get_value
        from service.ESmemory.es_memory_client import client

        # 获取ES索引名称
        index_name = get_value("humanrelation.event_index_name", "memory_event_store")

        # 处理key_attributes，确保ES能正确解析
        key_attributes = person_data.get("key_attributes", {})
        if isinstance(key_attributes, dict):
            # 将字典转换为JSON字符串，以便ES的text字段能正确存储
            key_attributes_str = json.dumps(key_attributes, ensure_ascii=False)
        else:
            key_attributes_str = str(key_attributes)

        # 处理relationships，转换为JSON字符串以便ES存储
        relationships = person_data.get("relationships", [])
        logger.info(f"🔍 原始relationships数据: {relationships} (类型: {type(relationships)})")

        # 统一处理各种可能的relationships格式
        if isinstance(relationships, str):
            logger.info(f"🔍 处理字符串格式的relationships: {relationships}")
            # 如果是字符串，尝试解析为JSON
            try:
                relationships = json.loads(relationships)
                logger.info(f"✅ JSON解析成功: {relationships}")
            except json.JSONDecodeError:
                logger.info(f"❌ JSON解析失败，检查是否为Python对象字符串格式")
                # 检查是否是Python对象的字符串表示（如 {type=同事, target=USER}）
                if relationships.startswith("{") and relationships.endswith("}") and "=" in relationships:
                    logger.info(f"🔧 检测到Python对象字符串格式，尝试解析")
                    try:
                        # 尝试将Python对象字符串转换为标准格式
                        # {type=同事, target=USER} -> {"type": "同事", "target": "USER"}

                        # 移除外层大括号
                        content = relationships.strip("{}")
                        # 分割键值对
                        pairs = content.split(", ")
                        parsed_dict = {}
                        for pair in pairs:
                            if "=" in pair:
                                key, value = pair.split("=", 1)
                                parsed_dict[key.strip()] = value.strip()
                        relationships = [parsed_dict]
                        logger.info(f"✅ Python对象字符串解析成功: {relationships}")
                    except Exception as e:
                        logger.error(f"❌ Python对象字符串解析失败: {e}")
                        # 如果解析失败，将字符串作为单个关系处理
                        relationships = [{"type": "未知关系", "target": relationships}]
                else:
                    # 如果解析失败，将字符串作为单个关系处理
                    relationships = [{"type": "未知关系", "target": relationships}]
        elif isinstance(relationships, dict):
            # 如果是单个字典，转换为列表
            relationships = [relationships]
        elif relationships is None:
            relationships = []
        elif not isinstance(relationships, list):
            # 如果是其他类型，转换为字符串并包装为关系对象
            relationships = [{"type": "未知关系", "target": str(relationships)}]

        # 确保列表中的每个元素都是可序列化的
        processed_relationships = []
        for rel in relationships:
            if isinstance(rel, dict):
                # 确保字典中的值都是可序列化的
                processed_rel = {}
                for k, v in rel.items():
                    try:
                        json.dumps(v, ensure_ascii=False)
                        processed_rel[k] = v
                    except (TypeError, ValueError):
                        processed_rel[k] = str(v)
                processed_relationships.append(processed_rel)
            else:
                # 如果不是字典，转换为标准格式
                processed_relationships.append({"type": "未知关系", "target": str(rel)})

        relationships = processed_relationships

        # 最终调试信息
        relationships_json = json.dumps(relationships, ensure_ascii=False)
        logger.info(f"🎯 最终relationships数据: {relationships_json}")

        # 将relationships转换为JSON字符串，与key_attributes保持一致
        if isinstance(relationships, list):
            relationships_str = json.dumps(relationships, ensure_ascii=False)
        else:
            relationships_str = str(relationships)

        # 构建ES文档
        es_document = {
            "memory_type": "person_profile",  # 新的记忆类型
            "user_id": person_data.get("user_id"),
            "person_id": person_id,
            "memory_content": f"{person_data.get('canonical_name', '')} {person_data.get('profile_summary', '')}",
            "canonical_name": person_data.get("canonical_name", ""),
            "profile_summary": person_data.get("profile_summary", ""),
            "key_attributes": key_attributes_str,  # 存储为JSON字符串
            "aliases": person_data.get("aliases", ""),
            "relationships": relationships_str,  # 存储为JSON字符串
            "time_stamp": datetime.now().strftime("%Y-%m-%d-%H-%M"),
            "timestamp": datetime.now(),
            "is_user": person_data.get("is_user", False),
        }

        # 同步到ES
        doc_id = f"person_profile_{person_id}"
        client.index(index=index_name, id=doc_id, body=es_document)
        logger.info(f"人物档案同步到ES成功: {person_id}")

    except Exception as e:
        logger.error(f"人物档案同步到ES失败: {e}")


def delete_person_from_es(person_id: str):
    """从ES中删除人物档案"""
    try:
        from configs.lion_config import get_value
        from service.ESmemory.es_memory_client import client

        index_name = get_value("humanrelation.event_index_name", "memory_event_store")
        doc_id = f"person_profile_{person_id}"

        client.delete(index=index_name, id=doc_id, ignore=[404])
        logger.info(f"从ES删除人物档案成功: {person_id}")

    except Exception as e:
        logger.error(f"从ES删除人物档案失败: {e}")


def is_current_user(user_id: str, person_id: str) -> bool:
    """判断指定的 person_id 是否为当前用户本人"""
    try:
        person = get_person_by_id_mysql(user_id=user_id, person_id=person_id)
        return person and person.get("is_user", False)
    except Exception as e:
        logger.error(f"判断是否为当前用户失败: {e}")
        return False
