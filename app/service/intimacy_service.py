########################################################
# 亲密度管理服务 - 管理用户与AI助手"老董"的亲密度
########################################################

import json
from datetime import datetime
from typing import Dict, Optional

from my_mysql.sql_client import CLIENT
from sqlalchemy import select, update, text
from utils.logger import logger


class IntimacyService:
    """用户与AI助手亲密度管理服务"""

    def __init__(self):
        self.intimacy_levels = {
            (0, 20): "初识",
            (21, 40): "熟悉", 
            (41, 60): "朋友",
            (61, 80): "好友",
            (81, 100): "知己"
        }

    def update_intimacy(self, user_id: str, interaction_type: str = "chat") -> Dict:
        """
        更新用户亲密度
        
        Args:
            user_id: 用户ID
            interaction_type: 交互类型 (chat, memory_add, deep_chat, personal_share)
            
        Returns:
            Dict: 包含更新结果的字典
        """
        try:
            # 获取当前亲密度分数
            current_score = self.get_intimacy_score(user_id)
            if current_score is None:
                logger.warning(f"用户 {user_id} 档案不存在，无法更新亲密度")
                return {"result": "error", "reason": "用户档案不存在"}

            # 计算增长值
            increase = self._calculate_intimacy_increase(current_score, interaction_type)
            new_score = min(100, current_score + increase)  # 最大值限制为100

            # 更新数据库
            success = self._update_intimacy_in_db(user_id, new_score)
            
            if success:
                logger.info(f"用户 {user_id} 亲密度更新: {current_score} -> {new_score} (+{increase})")
                return {
                    "result": "success",
                    "old_score": current_score,
                    "new_score": new_score,
                    "increase": increase,
                    "level": self.get_intimacy_level(new_score)
                }
            else:
                return {"result": "error", "reason": "数据库更新失败"}
                
        except Exception as e:
            logger.error(f"更新亲密度失败: {str(e)}")
            return {"result": "error", "reason": str(e)}

    def get_intimacy_score(self, user_id: str) -> Optional[int]:
        """获取用户当前亲密度分数"""
        try:
            conn = CLIENT.connect()
            try:
                from my_mysql.entity.person_table import person_memory
                
                stmt = select(person_memory.c.intimacy_score).where(
                    (person_memory.c.user_id == user_id) & 
                    (person_memory.c.is_user.is_(True))
                )
                result = conn.execute(stmt).fetchone()
                return result[0] if result else None
            finally:
                conn.close()
        except Exception as e:
            logger.error(f"获取亲密度分数失败: {str(e)}")
            return None

    def get_intimacy_info(self, user_id: str) -> Dict:
        """获取用户完整的亲密度信息"""
        try:
            score = self.get_intimacy_score(user_id)
            if score is None:
                return {"result": "error", "reason": "用户档案不存在"}

            level = self.get_intimacy_level(score)
            next_level_info = self._get_next_level_info(score)
            
            return {
                "result": "success",
                "user_id": user_id,
                "intimacy_score": score,
                "level": level,
                "next_level": next_level_info["level"],
                "progress_to_next": next_level_info["progress"],
                "points_needed": next_level_info["points_needed"]
            }
        except Exception as e:
            logger.error(f"获取亲密度信息失败: {str(e)}")
            return {"result": "error", "reason": str(e)}

    def get_intimacy_level(self, score: int) -> str:
        """根据分数获取亲密度等级"""
        for (min_score, max_score), level in self.intimacy_levels.items():
            if min_score <= score <= max_score:
                return level
        return "知己"  # 超过100分的情况

    def _calculate_intimacy_increase(self, current_score: int, interaction_type: str) -> int:
        """
        计算亲密度增长值
        前60分增长较快，超过60分后增长越来越慢
        """
        base_increase = {
            'chat': 1,           # 普通聊天
            'memory_add': 2,     # 添加记忆
            'deep_chat': 3,      # 深度对话（多轮）
            'personal_share': 5  # 分享个人信息
        }
        
        increase = base_increase.get(interaction_type, 1)
        
        # 应用衰减系数
        if current_score < 60:
            # 前60分：正常增长
            decay_factor = 1.0
        else:
            # 60分后：对数衰减，分数越高衰减越厉害
            decay_factor = max(0.1, 60 / current_score)
        
        final_increase = max(1, int(increase * decay_factor))
        return final_increase

    def _update_intimacy_in_db(self, user_id: str, new_score: int) -> bool:
        """在数据库中更新亲密度分数"""
        try:
            conn = CLIENT.connect()
            try:
                from my_mysql.entity.person_table import person_memory
                
                stmt = update(person_memory).where(
                    (person_memory.c.user_id == user_id) & 
                    (person_memory.c.is_user.is_(True))
                ).values(
                    intimacy_score=new_score,
                    intimacy_updated_at=text('CURRENT_TIMESTAMP')
                )
                
                result = conn.execute(stmt)
                conn.commit()
                return result.rowcount > 0
            finally:
                conn.close()
        except Exception as e:
            logger.error(f"数据库更新亲密度失败: {str(e)}")
            return False

    def _get_next_level_info(self, current_score: int) -> Dict:
        """获取下一等级的信息"""
        for (min_score, max_score), level in self.intimacy_levels.items():
            if current_score < min_score:
                # 找到下一个等级
                points_needed = min_score - current_score
                progress = 0
                return {
                    "level": level,
                    "points_needed": points_needed,
                    "progress": progress
                }
            elif min_score <= current_score <= max_score:
                # 当前等级，计算到下一等级的进度
                next_levels = [(min_s, max_s, lvl) for (min_s, max_s), lvl in self.intimacy_levels.items() if min_s > max_score]
                if next_levels:
                    next_min, next_max, next_level = next_levels[0]
                    points_needed = next_min - current_score
                    # 修正：计算到下一等级的进度百分比
                    # 进度 = (当前分数 - 当前等级最低分) / (下一等级最低分 - 当前等级最低分) * 100
                    total_points_to_next = next_min - min_score
                    current_progress_points = current_score - min_score
                    progress = (current_progress_points / total_points_to_next) * 100
                    return {
                        "level": next_level,
                        "points_needed": points_needed,
                        "progress": round(progress, 1)
                    }
                else:
                    # 已经是最高等级
                    return {
                        "level": "已达最高等级",
                        "points_needed": 0,
                        "progress": 100.0
                    }
        
        # 超过100分的情况
        return {
            "level": "已达最高等级",
            "points_needed": 0,
            "progress": 100.0
        }

    def analyze_interaction_type(self, user_input: str, ai_response: str) -> str:
        """
        分析交互类型，用于确定亲密度增长值

        Args:
            user_input: 用户输入
            ai_response: AI响应

        Returns:
            str: 交互类型
        """
        # 使用AI进行智能分析
        try:
            ai_result = self._analyze_interaction_with_ai(user_input, ai_response)
            if ai_result:
                return ai_result
        except Exception as e:
            logger.warning(f"AI分析交互类型失败，使用规则判断: {str(e)}")

        # 回退到简单的规则判断
        user_input_lower = user_input.lower()

        # 个人分享类关键词
        personal_keywords = ['我的', '我是', '我喜欢', '我觉得', '我认为', '我想', '我希望']
        if any(keyword in user_input for keyword in personal_keywords):
            return 'personal_share'

        # 深度对话判断（基于响应长度和内容复杂度）
        if len(ai_response) > 200 and ('建议' in ai_response or '分析' in ai_response):
            return 'deep_chat'

        # 记忆相关
        if '记住' in user_input or '记录' in user_input:
            return 'memory_add'

        # 默认为普通聊天
        return 'chat'

    def _analyze_interaction_with_ai(self, user_input: str, ai_response: str) -> str:
        """
        使用AI分析交互类型
        """
        from configs.lion_config import get_value
        from service.ai_client import send_to_ai
        import json

        # 从Lion配置获取提示词模板
        prompt_template = get_value("humanrelation.ai.interaction_analysis_prompt")

        prompt = prompt_template.format(user_input=user_input, ai_response=ai_response)

        query_input = {
            "model": get_value("humanrelation.memory_extraction_model", "gpt-4o-mini"),
            "messages": [{"role": "user", "content": prompt}],
            "stream": False,
            "temperature": 0.3,
            "max_tokens": 50,
        }

        response = send_to_ai(query_input)
        response_data = json.loads(response.text)
        result = response_data["choices"][0]["message"]["content"].strip().lower()

        # 验证返回的类型是否有效
        valid_types = ['chat', 'memory_add', 'deep_chat', 'personal_share']
        if result in valid_types:
            logger.info(f"AI分析交互类型: {user_input[:30]}... -> {result}")
            return result
        else:
            logger.warning(f"AI返回无效交互类型: {result}")
            return None
