"""
用户档案标准化处理模块
确保用户信息严格按照固定JSON结构存储
"""

import json
from typing import Any, Dict

from utils.logger import logger


class UserProfileStandardizer:
    """用户档案标准化处理器 - 强制固定结构版本"""

    # 固定的标准结构 - 这是唯一允许的结构
    STANDARD_STRUCTURE = {
        "基本信息": {
            "性别": "",
            "生日": "",
            "家乡": "",
            "当前城市": "",
            "联系方式": {"电话": "", "邮箱": "", "社交账号": {"微信": ""}},
            "职业信息": {"公司": "", "职位": "", "行业": ""},
            "家庭情况": {"婚育状况": "", "配偶姓名": "", "子女信息": []},
        },
        "过往历史": "",
        "兴趣": "",
        "关心话题": "",
        "旅游历史": "",
        "餐饮偏好": "",
        "期望": "",
    }

    def __init__(self):
        """初始化标准化处理器"""
        pass

    def ensure_standard_structure(self, raw_attributes: Dict[str, Any]) -> Dict[str, Any]:
        """
        确保用户属性按照标准结构存储，同时保留AI解析的有价值数据

        Args:
            raw_attributes: 原始属性字典（可能是大模型输出的任意结构）

        Returns:
            标准结构的属性字典，包含原始数据
        """
        try:
            # 如果输入已经是标准结构，直接验证并返回
            if self._is_standard_structure(raw_attributes):
                return self._fill_missing_fields(raw_attributes)

            # 如果不是标准结构，智能转换而不是丢弃数据
            logger.info(f"检测到非标准结构的用户属性，将智能转换为标准结构: {raw_attributes}")

            # 智能转换：保留有价值的数据，填充标准结构
            return self._convert_to_standard_structure(raw_attributes)

        except Exception as e:
            logger.error(f"确保标准结构失败: {e}")
            # 失败时返回空的标准结构
            return json.loads(json.dumps(self.STANDARD_STRUCTURE))

    def _is_standard_structure(self, attributes: Dict[str, Any]) -> bool:
        """
        检查属性是否符合标准结构
        """
        try:
            if not isinstance(attributes, dict):
                return False

            # 检查顶层字段
            required_top_fields = ["基本信息", "过往历史", "兴趣", "关心话题", "旅游历史", "餐饮偏好", "期望"]
            for field in required_top_fields:
                if field not in attributes:
                    return False

            # 检查基本信息的结构
            basic_info = attributes.get("基本信息", {})
            if not isinstance(basic_info, dict):
                return False

            required_basic_fields = ["性别", "生日", "家乡", "当前城市", "联系方式", "职业信息", "家庭情况"]
            for field in required_basic_fields:
                if field not in basic_info:
                    return False

            return True

        except Exception:
            return False

    def _fill_missing_fields(self, attributes: Dict[str, Any]) -> Dict[str, Any]:
        """
        填充缺失的字段，确保结构完整
        """
        try:
            # 深拷贝标准结构
            complete_structure = json.loads(json.dumps(self.STANDARD_STRUCTURE))

            # 递归合并，保留现有数据，填充缺失字段
            return self._deep_merge_with_defaults(complete_structure, attributes)

        except Exception as e:
            logger.error(f"填充缺失字段失败: {e}")
            return attributes

    def _deep_merge_with_defaults(self, defaults: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并，用数据覆盖默认值
        """
        result = defaults.copy()

        for key, value in data.items():
            if key in result:
                if isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = self._deep_merge_with_defaults(result[key], value)
                else:
                    result[key] = value
            # 忽略不在标准结构中的字段

        return result

    def merge_standard_attributes(self, existing: Dict[str, Any], new: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并两个标准结构的属性，新信息优先但不覆盖有价值的现有信息

        Args:
            existing: 现有的标准结构属性
            new: 新的标准结构属性

        Returns:
            合并后的标准结构属性
        """
        try:
            # 确保两个输入都是标准结构
            std_existing = (
                self.ensure_standard_structure(existing)
                if existing
                else json.loads(json.dumps(self.STANDARD_STRUCTURE))
            )
            std_new = self.ensure_standard_structure(new) if new else json.loads(json.dumps(self.STANDARD_STRUCTURE))

            # 递归合并
            return self._deep_merge_preserve_existing(std_existing, std_new)

        except Exception as e:
            logger.error(f"合并标准属性失败: {e}")
            return new if new else existing

    def _deep_merge_preserve_existing(self, existing: Dict[str, Any], new: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并两个字典，保留现有的有价值信息
        """
        result = existing.copy()

        for key, value in new.items():
            if key in result:
                if isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = self._deep_merge_preserve_existing(result[key], value)
                elif isinstance(result[key], list) and isinstance(value, list):
                    # 合并列表，去重
                    combined = result[key] + value
                    result[key] = list(dict.fromkeys(combined))  # 保持顺序的去重
                elif value and str(value).strip():  # 新值非空时覆盖
                    # 新的非空值直接覆盖现有值（包括非空的现有值）
                    result[key] = value
            else:
                result[key] = value

        return result

    def _convert_to_standard_structure(self, raw_attributes: Dict[str, Any]) -> Dict[str, Any]:
        """
        智能转换非标准结构为标准结构，保留有价值的数据
        """
        try:
            # 从标准结构开始
            result = json.loads(json.dumps(self.STANDARD_STRUCTURE))

            # 如果原始数据为空，直接返回标准结构
            if not raw_attributes:
                return result

            # 智能映射：尝试将原始数据映射到标准结构
            for key, value in raw_attributes.items():
                if key == "基本信息" and isinstance(value, dict):
                    # 直接合并基本信息
                    result["基本信息"] = self._deep_merge_with_defaults(result["基本信息"], value)
                elif key in ["过往历史", "兴趣", "关心话题", "旅游历史", "餐饮偏好", "期望"]:
                    # 直接映射顶层字段
                    result[key] = value
                else:
                    # 尝试智能映射到基本信息
                    self._smart_map_to_basic_info(result["基本信息"], key, value)

            logger.info(f"智能转换完成: {raw_attributes} -> 标准结构")
            return result

        except Exception as e:
            logger.error(f"智能转换失败: {e}")
            return json.loads(json.dumps(self.STANDARD_STRUCTURE))

    def _smart_map_to_basic_info(self, basic_info: Dict[str, Any], key: str, value: Any):
        """
        智能映射字段到基本信息结构
        """
        try:
            # 性别相关
            if key in ["性别", "gender"]:
                basic_info["性别"] = str(value) if value else ""

            # 生日相关
            elif key in ["生日", "年龄", "出生年份", "birthday", "age"]:
                basic_info["生日"] = str(value) if value else ""

            # 地址相关
            elif key in ["家乡", "出生地", "hometown"]:
                basic_info["家乡"] = str(value) if value else ""
            elif key in ["当前城市", "现居地", "城市", "city"]:
                basic_info["当前城市"] = str(value) if value else ""

            # 联系方式相关
            elif key in ["电话", "手机", "phone"]:
                basic_info["联系方式"]["电话"] = str(value) if value else ""
            elif key in ["邮箱", "email"]:
                basic_info["联系方式"]["邮箱"] = str(value) if value else ""
            elif key in ["微信", "wechat"]:
                basic_info["联系方式"]["社交账号"]["微信"] = str(value) if value else ""

            # 职业相关
            elif key in ["公司", "工作单位", "company"]:
                basic_info["职业信息"]["公司"] = str(value) if value else ""
            elif key in ["职位", "工作", "job", "position"]:
                basic_info["职业信息"]["职位"] = str(value) if value else ""
            elif key in ["行业", "industry"]:
                basic_info["职业信息"]["行业"] = str(value) if value else ""

            # 家庭相关
            elif key in ["婚育状况", "婚姻状况", "marital_status"]:
                basic_info["家庭情况"]["婚育状况"] = str(value) if value else ""
            elif key in ["配偶姓名", "配偶", "spouse"]:
                basic_info["家庭情况"]["配偶姓名"] = str(value) if value else ""
            elif key in ["子女信息", "孩子", "children", "孩子年龄"]:
                # 特别处理子女信息
                if isinstance(value, list):
                    basic_info["家庭情况"]["子女信息"] = value
                elif value:
                    # 如果是字符串或数字，转换为列表
                    basic_info["家庭情况"]["子女信息"] = [str(value)]

            # 其他字段暂时忽略，避免污染标准结构

        except Exception as e:
            logger.warning(f"智能映射字段失败 {key}={value}: {e}")


# 全局实例
user_profile_standardizer = UserProfileStandardizer()
