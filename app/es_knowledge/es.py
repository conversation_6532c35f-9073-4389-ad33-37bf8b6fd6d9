from langchain_community.retrievers import ElasticSearchBM25Retriever
from service.ESmemory.es_memory_client import client

index_name = "humanrelation_knowledge"

# 定义索引结构（mapping）
mapping = {
    "mappings": {
        "properties": {
            "title": {"type": "text"},
            "content": {"type": "text"},
            "tags": {"type": "keyword"},
            "created_at": {"type": "date"}
        }
    }
}

# 创建索引
client.indices.create(index=index_name, body=mapping, ignore=400)

# 创建检索器
retriever = ElasticSearchBM25Retriever(
    client=client,
    index_name=index_name,
    content_field="content",
    title_field="title"
)


