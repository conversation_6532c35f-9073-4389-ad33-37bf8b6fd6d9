import json
import time
from datetime import datetime, timedelta
from typing import Dict, List

import requests
from agents.state import State
from configs.config import DEFAULT_MODEL_CONFIG
from configs.lion_config import get_value
from fastapi import HTTPException
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, ToolMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import END, START, StateGraph
from langgraph.prebuilt import ToolNode
from memory.mysql_memory import MySQLCheckpointSaver
from service.enhanced_memory_service import EnhancedMemoryService
from tools.base import add_tools, clear_all_tools, get_all_tools
from tools.db import get_data_from_xhc
from tools.example import calculator_plus
from tools.phonenumber import get_user_phone_tool
from tools.reminder_tool import (
    add_reminder_tool,
    delete_recurring_reminders_tool,
    delete_reminder_tool,
    edit_reminder_tool,
    list_reminders_tool,
)
from tools.weather import get_smart_weather_info
from tools.webSearch import init_search_tools
from utils.log_formatter import format_tool_info
from utils.logger import logger, safe_json_log

# 检查 OpenAI API 密钥
COMPANY_OPENAI_API_BASE = "https://aigc.sankuai.com/v1/openai/native"
COMPANY_OPENAI_API_KEY = str(get_value("xiaomei.humanrelation_openai_apikey", "1930188146638651430"))

# 初始化增强记忆服务
memory_service = EnhancedMemoryService()

# 导入新闻检索功能
from rag_retriever import rag_retriever


def _search_relevant_news(user_input: str, max_results: int = 5) -> dict:
    """检索与用户输入相关的新闻"""
    try:
        logger.info(f"📰 开始新闻检索: '{user_input}'")
        # 调用 RAG 检索器
        news_results = rag_retriever.semantic_search(user_input, top_k=max_results)

        if not news_results:
            logger.info("📰 RAG检索未返回任何新闻")
            return {"result": "no_news", "news": []}

        # 格式化新闻结果，保留更多内容用于上下文增强
        formatted_news = []
        for news in news_results:
            content = news.get("content", "")
            formatted_news.append(
                {
                    "title": news.get("title", ""),
                    "content": content[:300] + "..." if len(content) > 300 else content,
                    "full_content": content,  # 保留完整内容供prompt使用
                    "tags": news.get("tags", []),
                    "source_url": news.get("source_url", ""),
                    "similarity": round(news.get("similarity", 0), 3),
                    "relevance_score": round(news.get("similarity", 0), 3),
                    "ai_judgment": news.get("ai_judgment", {}),
                    "generated_keywords": news.get("generated_keywords", []),
                }
            )

        logger.info(f"✅ 成功检索到{len(formatted_news)}条相关新闻")
        if formatted_news:
            logger.info(
                f"📊 相关度范围: {formatted_news[0]['similarity']:.3f} - {formatted_news[-1]['similarity']:.3f}"
            )
            for i, news in enumerate(formatted_news, 1):
                logger.info(f"  📄 {i}. 《{news['title'][:40]}...》 相关度: {news['similarity']:.3f}")

        return {"result": "success", "news": formatted_news, "total_found": len(news_results), "query": user_input}
    except Exception as e:
        logger.error(f"新闻检索失败: {str(e)}")
        return {"result": "error", "news": [], "error": str(e)}


# --- LangGraph 核心逻辑 ---
# 读取人力资源项目的搜索工具配置
try:
    tools_config_str = str(get_value("xiaomei.humanrelation.search_service_config", "{}"))
    logger.info(f"🔧 从Lion读取的搜索引擎配置: {safe_json_log(tools_config_str)}")
    tools_config = json.loads(tools_config_str)
    # 确保配置包含必要的字段
    if "SearchEngineStatus" not in tools_config:
        logger.warning("搜索工具配置中缺少 SearchEngineStatus，使用默认配置")
        tools_config = {
            "SearchEngineStatus": {
                "bing_search": False,
                "google_search": True,
                "wenxin_search": True,
                "tencent_search": True,
                "unisearch": True,
            }
        }
except Exception as e:
    logger.error(f"获取搜索工具配置失败: {e}，使用默认配置")
    tools_config = {
        "SearchEngineStatus": {
            "bing_search": False,
            "google_search": True,
            "wenxin_search": True,
            "tencent_search": True,
            "unisearch": True,
        }
    }

logger.info(f"🔧 最终使用的搜索工具配置: {safe_json_log(tools_config)}")
searchTool = init_search_tools(tools_config)


clear_all_tools()
core_tools = [
    calculator_plus,
    searchTool,
    get_smart_weather_info,  # 智能天气工具（唯一天气工具）
    add_reminder_tool,
    delete_reminder_tool,
    list_reminders_tool,
    delete_recurring_reminders_tool,
    edit_reminder_tool,
    get_user_phone_tool,
    get_data_from_xhc,
    # get_weather_info,  # 基础天气工具已移除，避免重复调用
]
add_tools(core_tools)
tools_list = get_all_tools()


def custom_tool_node(state: State):
    """自定义工具节点，确保user_id传递到工具上下文，并控制tool_call_id长度"""
    import uuid

    # 设置当前用户ID到上下文中
    user_id = state.get("user_id", "default_user")

    # 设置提醒工具的上下文变量
    from tools.reminder_tool import current_user_id as reminder_current_user_id

    reminder_current_user_id.set(user_id)

    # 设置手机号工具的上下文变量
    from tools.phonenumber import current_user_id as phone_current_user_id

    phone_current_user_id.set(user_id)

    # 设置天气工具的上下文变量
    from tools.weather import current_user_id as weather_current_user_id

    weather_current_user_id.set(user_id)

    # 【改进】增强异常处理，确保所有tool_calls都有对应的ToolMessage
    try:
        # 调用原始工具节点
        tool_node = ToolNode(tools_list)
        result = tool_node.invoke(state)

        # 验证结果完整性
        if "messages" in result and result["messages"]:
            logger.debug(f"工具节点返回 {len(result['messages'])} 条消息")
        else:
            logger.warning("工具节点返回空消息，可能存在执行问题")
            # 如果没有返回消息，为所有tool_calls生成错误响应
            result = _generate_error_tool_messages(state, "工具执行未返回任何响应")

        # 无论如何都要检查是否有工具调用但缺少响应的情况
        _ensure_tool_message_completeness(state, result)

    except Exception as e:
        logger.error(f"工具节点执行异常: {e}")
        # 生成错误响应消息，确保不留下未响应的tool_calls
        result = _generate_error_tool_messages(state, str(e))

    # 检查并修复tool_call_id长度问题
    if "messages" in result:
        for message in result["messages"]:
            if hasattr(message, "tool_calls") and message.tool_calls:
                for tool_call in message.tool_calls:
                    if hasattr(tool_call, "id") and len(tool_call.id) > 40:
                        # 生成短ID（保持唯一性），限制在36字符以内
                        original_id = tool_call.id
                        tool_call.id = str(uuid.uuid4())[:36]
                        logger.info(f"工具调用ID过长已修复: {len(original_id)}字符 -> {len(tool_call.id)}字符")
            # 验证ToolMessage格式
            if isinstance(message, ToolMessage):
                if not hasattr(message, "tool_call_id") or not message.tool_call_id:
                    logger.warning("发现格式异常的ToolMessage，缺少tool_call_id")
                    # 可以考虑修复或跳过这个消息
                else:
                    logger.debug(f"验证ToolMessage格式正常: {message.tool_call_id}")

    return result


def _ensure_tool_message_completeness(state: State, result: dict):
    """确保所有tool_calls都有对应的ToolMessage"""
    try:
        # 从state中获取原始的tool_calls
        messages = state.get("messages", [])
        if not messages:
            return

        last_message = messages[-1]
        if not (hasattr(last_message, "tool_calls") and last_message.tool_calls):
            return

        # 收集所有应该有响应的tool_call_ids
        expected_tool_call_ids = set()
        for tool_call in last_message.tool_calls:
            if hasattr(tool_call, "id"):
                expected_tool_call_ids.add(tool_call.id)

        # 检查result中的ToolMessage是否覆盖了所有tool_calls
        actual_tool_call_ids = set()
        for message in result.get("messages", []):
            if isinstance(message, ToolMessage) and hasattr(message, "tool_call_id"):
                actual_tool_call_ids.add(message.tool_call_id)

        # 找出缺失的tool_call_ids
        missing_tool_call_ids = expected_tool_call_ids - actual_tool_call_ids

        if missing_tool_call_ids:
            logger.warning(f"检测到缺失的tool_call响应: {missing_tool_call_ids}")

            # 为缺失的tool_calls生成错误响应消息
            additional_messages = []
            for missing_id in missing_tool_call_ids:
                error_message = ToolMessage(
                    content="工具执行失败：执行过程中发生异常，请稍后重试。", tool_call_id=missing_id
                )
                additional_messages.append(error_message)
                logger.info(f"为缺失的tool_call_id生成错误响应: {missing_id}")

            # 添加到result中
            if "messages" not in result:
                result["messages"] = []
            result["messages"].extend(additional_messages)

    except Exception as e:
        logger.error(f"检查tool_message完整性时发生错误: {e}")


def _generate_error_tool_messages(state: State, error_msg: str):
    """为所有tool_calls生成错误响应消息"""
    try:
        messages = state.get("messages", [])
        if not messages:
            return {"messages": []}

        last_message = messages[-1]
        if not (hasattr(last_message, "tool_calls") and last_message.tool_calls):
            return {"messages": []}

        error_messages = []
        for tool_call in last_message.tool_calls:
            if hasattr(tool_call, "id"):
                error_message = ToolMessage(content=f"工具执行失败：{error_msg}", tool_call_id=tool_call.id)
                error_messages.append(error_message)
                logger.info(f"为tool_call_id生成错误响应: {tool_call.id}")

        return {"messages": error_messages}

    except Exception as e:
        logger.error(f"生成错误tool_messages时发生异常: {e}")
        return {"messages": []}


logger.info(f"tools: {safe_json_log([format_tool_info(tool) for tool in tools_list])}")


def _format_system_prompt_with_time(system_prompt_template: str) -> str:
    """格式化系统提示词，替换时间变量"""
    try:
        # 获取当前时间信息
        now = datetime.now()
        current_datetime = now.strftime("%Y-%m-%d %H:%M:%S")
        current_date = now.strftime("%Y-%m-%d")
        tomorrow_date = (now + timedelta(days=1)).strftime("%Y-%m-%d")

        # 替换时间变量
        formatted_prompt = system_prompt_template.format(
            current_datetime=current_datetime, current_date=current_date, tomorrow_date=tomorrow_date
        )

        logger.info(
            f"时间变量替换完成: current_datetime={current_datetime}, current_date={current_date}, tomorrow_date={tomorrow_date}"
        )
        return formatted_prompt

    except Exception as e:
        logger.error(f"格式化系统提示词时出错: {str(e)}")
        # 如果格式化失败，返回原始模板
        return system_prompt_template


def _get_contextual_pre_response(user_input: str) -> List[str]:
    """根据用户输入生成合适的预响应消息"""
    import random

    # 分析用户输入类型，选择合适的预响应
    user_input_lower = user_input.lower().strip()

    # 简单查询的预响应
    if any(keyword in user_input_lower for keyword in ["是谁", "叫什么", "谁是"]):
        return ["收到了，让我查找一下相关信息...", "正在检索您的人际关系档案..."]

    # 分析类查询的预响应
    elif any(keyword in user_input_lower for keyword in ["分析", "建议", "怎么办", "如何"]):
        return ["收到了，这是一个很好的问题...", "让我仔细分析一下情况...", "正在整理相关信息和建议..."]

    # 情感类查询的预响应
    elif any(keyword in user_input_lower for keyword in ["压力", "困难", "烦恼", "担心", "焦虑"]):
        return ["我理解您的感受...", "让我帮您梳理一下情况..."]

    # 工作相关查询的预响应
    elif any(keyword in user_input_lower for keyword in ["工作", "同事", "领导", "项目", "职场"]):
        return ["收到了，关于工作方面的问题...", "正在分析您的职场人际关系..."]

    # 家庭相关查询的预响应
    elif any(keyword in user_input_lower for keyword in ["家人", "父母", "孩子", "老婆", "老公", "女儿", "儿子"]):
        return ["收到了，关于家庭的问题...", "让我查看一下相关的家庭信息..."]

    # 默认通用预响应
    else:
        # 基础回复（始终可用）
        generic_responses = [
            ["收到了，我正在思考...", "让我为您查找相关信息..."],
            ["好的，我来帮您分析一下...", "正在处理您的问题..."],
            ["明白了，让我想想...", "正在整理相关资料..."],
        ]

        # 检查是否启用俏皮回复
        enable_playful_responses = get_value("humanrelation.enable_playful_responses", False)

        if enable_playful_responses:
            # 从Lion配置获取俏皮回复内容
            playful_responses_config = get_value("humanrelation.playful_responses", "")

            if playful_responses_config:
                try:
                    import json

                    playful_responses = json.loads(playful_responses_config)
                    generic_responses.extend(playful_responses)
                    logger.info(f"🎭 [俏皮回复] 已启用并加载 {len(playful_responses)} 组俏皮回复")
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"🎭 [俏皮回复] 配置解析失败: {e}，使用默认回复")
            else:
                # 如果启用了但没有配置内容，使用默认的俏皮回复
                default_playful_responses = [
                    ["哎呀，这个问题有点意思呢~", "让我翻翻我的小本本..."],
                    ["嗯嗯，我懂了！", "正在调动我的所有脑细胞..."],
                    ["这个我熟！", "马上给您找到答案~"],
                    ["稍等哦，我在脑海里搜索中...", "让我想想有什么好建议..."],
                    ["收到收到！", "正在努力回忆相关信息..."],
                    ["好嘞，交给我吧！", "我来帮您梳理一下思路..."],
                    ["这个问题问得好！", "让我仔细想想怎么回答..."],
                    ["嘿嘿，我最喜欢这种问题了~", "正在整理我的知识库..."],
                    ["明白明白！", "让我帮您分析分析..."],
                ]
                generic_responses.extend(default_playful_responses)
                logger.info(f"🎭 [俏皮回复] 已启用，使用默认俏皮回复 {len(default_playful_responses)} 组")
        else:
            logger.info("🎭 [俏皮回复] 未启用，使用标准回复")
        return random.choice(generic_responses)


def process_memory_and_context(state: State):  # 记忆处理和上下文增强节点
    """处理用户输入的记忆并为对话添加上下文"""
    messages = state["messages"]
    if not messages:
        return state

    # 【新增】在处理开始前，检查并清理可能的错误状态
    cleaned_messages = _validate_and_fix_message_sequence(messages)
    if len(cleaned_messages) != len(messages):
        logger.warning(f"在记忆处理阶段清理了消息序列: {len(messages)} -> {len(cleaned_messages)}")
        state["messages"] = cleaned_messages
        messages = cleaned_messages

    # 分离当前用户输入和历史消息
    last_message = messages[-1]
    user_input = last_message.content if hasattr(last_message, "content") else str(last_message)

    # 筛选出AIMessage和HumanMessage作为历史，并排除最后的输入
    history_messages = [m for m in messages[:-1] if isinstance(m, (HumanMessage, AIMessage))]

    # --- 新增：为记忆提取构建上下文输入 ---
    # 目的是让记忆提取模块理解用户输入的上下文，例如回答AI提出的问题
    contextual_memory_input_parts = []
    # 如果有历史，则取最后一条AI的消息作为上下文
    if history_messages and isinstance(history_messages[-1], AIMessage):
        contextual_memory_input_parts.append(f"Assistant: {history_messages[-1].content}")

    contextual_memory_input_parts.append(f"User: {user_input}")
    contextual_memory_input = "\n".join(contextual_memory_input_parts)
    # --- 结束 ---

    chat_history_str = "\n".join(
        [f"{'User' if isinstance(m, HumanMessage) else 'Assistant'}: {m.content}" for m in history_messages]
    )

    user_id = state.get("user_id", "default_user")
    conversation_id = state.get("conversation_id")  # 获取conversation_id

    logger.info(f"process_memory_and_context: user_id={user_id}, conversation_id={conversation_id}")
    logger.info(f"state keys: {list(state.keys())}")

    # 档案快照现在在get_agent_response_stream中保存，这里不需要处理
    pass

    try:
        # 1. 提取并自动存储记忆 (使用带上下文的输入)
        logger.info(f"调用extract_and_process_memory的参数: user_id={user_id}, conversation_id={conversation_id}")
        memory_result = memory_service.extract_and_process_memory(contextual_memory_input, user_id, conversation_id)
        logger.info(f"记忆提取结果: {memory_result}")

        # 将memory_result保存到state中，供后续追踪变化使用
        state["memory_result"] = memory_result

        # 【修复】检查是否为查询意图，如果是则直接使用查询结果
        if memory_result.get("action_code") == "QUERY_RESULT":
            logger.info("🔍 识别到查询意图，使用查询结果构建上下文")
            query_payload = memory_result.get("payload", {})
            retrieval_result = query_payload.get(
                "result", {"result": "success", "persons": [], "events": [], "suggestions": ""}
            )

            # 对于查询，不需要新闻检索，直接构建上下文
            full_context_str = _build_full_context(retrieval_result, memory_result, {"result": "success", "news": []})
        else:
            # 2. 检索相关记忆为对话提供上下文
            retrieval_result = memory_service.retrieve_memory_for_conversation(user_input, user_id, 3)

            # 3. [新增] 检索相关新闻
            logger.info("📰 [增强流程] 开始检索相关新闻...")
            news_result = _search_relevant_news(user_input)

            # 4. [修改] 构建完整的上下文 (记忆 + 新闻)
            full_context_str = _build_full_context(retrieval_result, memory_result, news_result)

        # 5. 获取系统角色设定并格式化时间变量
        system_prompt_template = str(
            get_value(
                "humanrelation.default_system", "你是一个乐于助人的人力资源助理。请总是使用中文进行友好和专业的回答。"
            )
        )
        system_prompt = _format_system_prompt_with_time(system_prompt_template)

        # 6. [修改] 构建结构化的最终提示
        final_prompt = f"""system_prompt: {system_prompt}

chathistory:
{chat_history_str}

context:
{full_context_str}

ask: {user_input}"""

        # 7. 将最终提示暂存，而不是修改message历史
        return {"memory_context": final_prompt.strip()}

    except Exception as e:
        logger.error(f"记忆与新闻处理失败: {str(e)}")
        # 即使失败，也要确保返回一个标准的SystemMessage，避免后续节点出错
        system_prompt_template = str(
            get_value(
                "humanrelation.default_system",
                "你是一个乐于助人的人力资源助理。请总是使用中文进行友好和专业的回答。",
            )
        )
        system_prompt = _format_system_prompt_with_time(system_prompt_template)

        return {"messages": [SystemMessage(content=system_prompt)] + messages}


def call_model(state: State):  # 调用模型节点
    """调用 OpenAI 模型节点"""
    # 【修复】声明使用全局导入的AIMessage，避免UnboundLocalError
    global AIMessage, HumanMessage, SystemMessage, ToolMessage

    messages = state["messages"]

    # 【修复断流问题】智能判断使用哪种消息格式
    # 如果最后一条消息是 ToolMessage，这意味着我们正处于工具调用循环中
    if messages and isinstance(messages[-1], ToolMessage):
        logger.info("🔄 检测到工具调用后的第二次模型调用，使用完整消息历史")
        messages_for_model = messages

        # 【优化】检查消息历史的总长度，如果过长则进行智能截断
        total_length = sum(len(str(msg.content)) for msg in messages if hasattr(msg, "content") and msg.content)
        if total_length > 8000:  # 如果总长度超过8000字符
            logger.warning(f"⚠️ 消息历史过长 ({total_length} 字符)，进行智能截断")
            # 保留最近的几条关键消息
            key_messages = []
            # 保留最后的工具调用和响应
            for msg in reversed(messages):
                key_messages.insert(0, msg)
                if isinstance(msg, AIMessage) and hasattr(msg, "tool_calls") and msg.tool_calls:
                    break  # 找到工具调用的起点就停止
            messages_for_model = key_messages[-10:]  # 最多保留10条消息
            logger.info(f"📝 截断后消息数量: {len(messages_for_model)}")
    else:
        # 第一次调用：优先使用增强的 memory_context
        final_prompt = state.get("memory_context")
        if isinstance(final_prompt, str):
            logger.info("🎯 使用增强的memory_context进行第一次模型调用")

            # 【优化】检查prompt长度，如果过长则截断
            if len(final_prompt) > 12000:
                logger.warning(f"⚠️ final_prompt过长 ({len(final_prompt)} 字符)，进行截断")
                # 保留system_prompt和ask部分，截断context部分
                lines = final_prompt.split("\n")
                system_lines = [line for line in lines if line.startswith("system_prompt:")]
                ask_lines = [line for line in lines if line.startswith("ask:")]
                context_lines = [line for line in lines if "context:" in line or line.startswith("  ")]

                # 重新组装，限制context长度
                truncated_context = "\n".join(context_lines[:50])  # 最多50行context
                final_prompt = "\n".join(system_lines + ["", truncated_context, ""] + ask_lines)
                logger.info(f"📝 截断后prompt长度: {len(final_prompt)}")

            messages_for_model = [HumanMessage(content=final_prompt)]
        else:
            # 如果 memory_context 缺失，回退到原始消息
            logger.warning("⚠️ memory_context缺失，回退到原始消息")
            messages_for_model = messages

    # 【新增】验证和修复消息序列格式
    logger.info(f"📊 修复前消息数量: {len(messages_for_model)}")

    # 【重要修复】工具调用后的第二次调用不要过度修复消息序列
    is_tool_followup = messages and isinstance(messages[-1], ToolMessage)
    if is_tool_followup:
        logger.info("🔧 工具调用后续处理，跳过激进的消息序列修复")
        # 只做基本验证，不做修复，避免破坏工具调用上下文
        messages_for_model = messages_for_model
    else:
        # 第一次调用时才进行完整的消息序列修复
        messages_for_model = _validate_and_fix_message_sequence(messages_for_model)

    logger.info(f"📊 修复后消息数量: {len(messages_for_model)}")

    # 打印最终发送给回答模型的内容
    logger.info("--- 向最终回答模型发送的内容 ---")
    for i, msg in enumerate(messages_for_model):
        logger.info(f"[{i}] 角色: {msg.type}, 内容: {msg.content[:100]}...")
        if hasattr(msg, "tool_calls") and msg.tool_calls:
            logger.info(f"    工具调用: {[tc.id for tc in msg.tool_calls if hasattr(tc, 'id')]}")
        if hasattr(msg, "tool_call_id"):
            logger.info(f"    工具响应ID: {msg.tool_call_id}")
    logger.info("-----------------------------")

    model_config = json.loads(str(get_value("xiaomei.humanrelation.model_config", DEFAULT_MODEL_CONFIG)))

    logger.info(f"model_config: {safe_json_log(model_config)}, COMPANY_OPENAI_API_KEY: {COMPANY_OPENAI_API_KEY}")

    # 根据模型类型初始化不同的模型类
    model_name = model_config["model_name"]

    if "anthropic" in model_name.lower() or "claude" in model_name.lower():
        # 使用Anthropic模型
        try:
            from langchain_anthropic import ChatAnthropic

            model = ChatAnthropic(
                model=model_name,
                temperature=model_config["temperature"],
                streaming=True,
                anthropic_api_key=COMPANY_OPENAI_API_KEY,  # 如果使用统一的API密钥
                # 可能需要调整API地址
            )
            logger.info(f"✅ 使用Anthropic模型: {model_name}")
        except ImportError:
            logger.warning("❌ langchain_anthropic未安装，回退到ChatOpenAI")
            model = ChatOpenAI(
                model_name=model_name,
                temperature=model_config["temperature"],
                streaming=True,
                openai_api_base=COMPANY_OPENAI_API_BASE,
                openai_api_key=COMPANY_OPENAI_API_KEY,
            )
    else:
        # 使用OpenAI兼容的模型（包括OpenAI、国产模型等）
        model_kwargs = {
            "model_name": model_name,
            "temperature": model_config.get("temperature", 0.3),
            "streaming": True,
            "openai_api_base": COMPANY_OPENAI_API_BASE,
            "openai_api_key": COMPANY_OPENAI_API_KEY,
        }

        # 【优化】添加更多模型参数，提高响应稳定性
        # 注意：由于OpenAI API参数冲突问题，暂时不设置token限制
        # 让API使用默认的token限制，避免max_tokens和max_completion_tokens冲突
        logger.info(f"🔧 跳过max_tokens设置，避免API参数冲突。原配置值: {model_config.get('max_tokens', 'N/A')}")

        if "top_p" in model_config:
            model_kwargs["top_p"] = model_config["top_p"]
        if "frequency_penalty" in model_config:
            model_kwargs["frequency_penalty"] = model_config["frequency_penalty"]
        if "presence_penalty" in model_config:
            model_kwargs["presence_penalty"] = model_config["presence_penalty"]

        # 【重要】添加请求超时设置
        model_kwargs["request_timeout"] = 30  # 30秒超时
        model_kwargs["max_retries"] = 2  # 最多重试2次

        logger.info(f"🔧 模型参数配置: {model_kwargs}")

        model = ChatOpenAI(**model_kwargs)
        logger.info(f"✅ 使用OpenAI兼容模型: {model_name}")

    # 【智能工具绑定】根据调用场景决定是否绑定工具
    try:
        # 如果是工具调用后的第二次调用，通常不需要再次调用工具
        # 但为了保持一致性，仍然绑定工具（模型会自动判断是否需要调用）
        if is_tool_followup:
            logger.info("🔧 工具调用后续处理，使用工具绑定模型确保上下文完整")

        model_with_tools = model.bind_tools(tools_list)
        logger.info(f"✅ 模型 {model_name} 支持工具调用，已绑定 {len(tools_list)} 个工具")
    except Exception as e:
        logger.warning(f"⚠️ 模型 {model_name} 可能不支持工具调用: {e}")
        # 对于不支持工具调用的模型，仍然返回原始模型
        model_with_tools = model

    # 【增强调用日志】
    logger.info(f"🚀 开始调用模型，消息类型: {'工具后续' if is_tool_followup else '首次调用'}")

    # 【诊断日志】记录关键调用信息
    total_msg_length = sum(
        len(str(msg.content)) for msg in messages_for_model if hasattr(msg, "content") and msg.content
    )
    logger.info(f"📊 调用统计: 消息数={len(messages_for_model)}, 总字符数={total_msg_length}")

    # 记录消息类型分布
    msg_types = {}
    for msg in messages_for_model:
        msg_type = type(msg).__name__
        msg_types[msg_type] = msg_types.get(msg_type, 0) + 1
    logger.info(f"📋 消息类型分布: {safe_json_log(msg_types)}")

    try:
        # 调用模型
        response = model_with_tools.invoke(messages_for_model)
    except Exception as e:
        logger.error(f"❌ 模型调用异常: {e}")

        # 检查是否是工具调用格式错误
        error_str = str(e)
        if "tool_calls" in error_str and "tool_call_id" in error_str:
            logger.warning("🔧 检测到工具调用格式错误，尝试自动修复...")

            # 尝试修复消息序列
            fixed_messages = _validate_and_fix_message_sequence(messages_for_model)

            if len(fixed_messages) != len(messages_for_model):
                logger.info(f"✅ 消息序列已修复: {len(messages_for_model)} -> {len(fixed_messages)}")
                try:
                    # 使用修复后的消息重新调用模型
                    response = model_with_tools.invoke(fixed_messages)
                    logger.info("✅ 使用修复后的消息成功调用模型")
                except Exception as retry_e:
                    logger.error(f"❌ 修复后仍然失败: {retry_e}")
                    # 生成错误回答
                    fallback_response = AIMessage(
                        content="抱歉，我在处理您的请求时遇到了技术问题。请稍后再试，或者换个方式提问。"
                    )
                    return {"messages": [fallback_response]}
            else:
                logger.warning("❌ 消息序列无法修复")
                # 生成错误回答
                fallback_response = AIMessage(
                    content="抱歉，我在处理您的请求时遇到了技术问题。请稍后再试，或者换个方式提问。"
                )
                return {"messages": [fallback_response]}
        else:
            # 其他类型的错误，直接生成错误回答
            fallback_response = AIMessage(
                content="抱歉，我在处理您的请求时遇到了技术问题。请稍后再试，或者换个方式提问。"
            )
            return {"messages": [fallback_response]}

    # 【调用结果验证和修复】
    if hasattr(response, "content"):
        content_length = len(response.content) if response.content else 0
        has_tool_calls = hasattr(response, "tool_calls") and response.tool_calls

        logger.info(f"📤 模型调用完成，响应内容长度: {content_length}")

        if has_tool_calls:
            logger.info(f"🔧 模型响应包含 {len(response.tool_calls)} 个工具调用")
        else:
            logger.info("💬 模型响应为纯文本内容")

        # 【断流修复】如果是工具调用后的空响应，生成默认回答
        if is_tool_followup and content_length == 0 and not has_tool_calls:
            logger.error("❌ 检测到工具调用后的空响应！生成默认回答防止断流")

            # 尝试从工具结果中提取信息生成回答
            tool_results = [msg for msg in messages if isinstance(msg, ToolMessage)]
            if tool_results:
                last_tool_result = tool_results[-1]
                tool_content = last_tool_result.content[:200] if last_tool_result.content else "工具执行完成"

                fallback_content = f"根据搜索结果，我为您找到了相关信息：\n\n{tool_content}..."
                logger.info(f"🔧 生成备用回答，长度: {len(fallback_content)}")

                # 创建新的AI消息替代空响应
                response = AIMessage(content=fallback_content)
            else:
                # 完全无法获取工具信息时的最后兜底
                response = AIMessage(
                    content="抱歉，我在处理您的请求时遇到了一些技术问题，但我已经尽力为您查找了相关信息。请您稍后再试，或者换个方式提问。"
                )
                logger.warning("⚠️ 使用最终兜底回答")

    else:
        logger.warning(f"⚠️ 模型响应格式异常: {type(response)}")

    # 返回AI响应，它将被追加到纯净的聊天历史中
    return {"messages": [response]}


def _validate_and_fix_message_sequence(messages):
    """验证并修复消息序列，确保tool_calls和tool messages正确配对"""
    try:
        if not messages:
            return messages

        # 收集所有的tool_call_ids和对应的tool_response_ids
        pending_tool_calls = {}  # tool_call_id -> message_index
        tool_responses = set()  # tool_call_ids that have responses

        # 第一遍：识别所有tool_calls和tool_responses
        for i, message in enumerate(messages):
            if hasattr(message, "tool_calls") and message.tool_calls:
                for tool_call in message.tool_calls:
                    if hasattr(tool_call, "id"):
                        pending_tool_calls[tool_call.id] = i
                        logger.debug(f"Found tool_call: {tool_call.id} at message {i}")

            if isinstance(message, ToolMessage):
                if hasattr(message, "tool_call_id") and message.tool_call_id:
                    tool_responses.add(message.tool_call_id)
                    logger.debug(f"Found tool_response: {message.tool_call_id}")

        # 检查是否有未响应的tool_calls
        unresponsive_calls = set(pending_tool_calls.keys()) - tool_responses

        if unresponsive_calls:
            logger.warning(f"发现未响应的tool_calls: {unresponsive_calls}")

            # 改进的修复策略：
            # 1. 如果AIMessage包含混合的tool_calls（有些有响应，有些没有），只移除未响应的
            # 2. 如果AIMessage所有tool_calls都没有响应，保留消息但移除所有tool_calls
            # 3. 这样可以保持对话的连续性
            filtered_messages = []
            for i, message in enumerate(messages):
                if hasattr(message, "tool_calls") and message.tool_calls:
                    # 检查这个消息的tool_calls响应情况
                    has_responsive_calls = any(
                        hasattr(tool_call, "id") and tool_call.id not in unresponsive_calls
                        for tool_call in message.tool_calls
                    )
                    has_unresponsive_calls = any(
                        hasattr(tool_call, "id") and tool_call.id in unresponsive_calls
                        for tool_call in message.tool_calls
                    )

                    if has_unresponsive_calls:
                        logger.warning(f"消息 {i} 包含未响应的tool_calls，正在修复...")

                        if has_responsive_calls:
                            # 混合情况：只保留有响应的tool_calls
                            remaining_tool_calls = [
                                tool_call
                                for tool_call in message.tool_calls
                                if hasattr(tool_call, "id") and tool_call.id not in unresponsive_calls
                            ]
                            logger.info(
                                f"保留 {len(remaining_tool_calls)}/{len(message.tool_calls)} 个有响应的tool_calls"
                            )

                            # 创建新的AIMessage，只包含有响应的tool_calls
                            new_message = AIMessage(content=message.content, tool_calls=remaining_tool_calls)
                            filtered_messages.append(new_message)
                        else:
                            # 所有tool_calls都没有响应：移除tool_calls但保留消息内容
                            logger.warning(f"消息 {i} 的所有tool_calls都没有响应，移除tool_calls但保留消息内容")
                            new_message = AIMessage(content=message.content or "")
                            filtered_messages.append(new_message)
                    else:
                        # 所有tool_calls都有响应，保持原样
                        filtered_messages.append(message)
                else:
                    # 不是tool_calls消息，直接保留
                    filtered_messages.append(message)

            logger.info(f"消息序列修复完成: {len(messages)} -> {len(filtered_messages)} 条消息")

            # 最终验证：确保修复后的序列是有效的
            if _final_validation(filtered_messages):
                return filtered_messages
            else:
                logger.error("修复后的消息序列仍然无效，返回原始消息")
                return messages

        logger.debug("消息序列验证通过，无需修复")
        return messages

    except Exception as e:
        logger.error(f"消息序列验证失败: {e}")
        # 发生错误时返回原始消息
        return messages


def _final_validation(messages):
    """最终验证修复后的消息序列是否有效"""
    try:
        tool_calls_needing_response = set()

        for message in messages:
            if hasattr(message, "tool_calls") and message.tool_calls:
                for tool_call in message.tool_calls:
                    if hasattr(tool_call, "id"):
                        tool_calls_needing_response.add(tool_call.id)

            if isinstance(message, ToolMessage):
                if hasattr(message, "tool_call_id") and message.tool_call_id:
                    tool_calls_needing_response.discard(message.tool_call_id)

        if tool_calls_needing_response:
            logger.error(f"最终验证失败：仍有未响应的tool_calls: {tool_calls_needing_response}")
            return False

        logger.debug("最终验证通过")
        return True

    except Exception as e:
        logger.error(f"最终验证出错: {e}")
        return False


def _build_full_context(retrieval_result: dict, memory_result: dict, news_result: dict) -> str:
    """基于检索到的记忆和新闻构建完整的上下文"""
    context_parts = []
    logger.info("--- [增强内容分析] ---")

    # Part 1: Memory Context
    memory_context_parts = []
    try:
        base_prompt = str(
            get_value(
                "humanrelation.default_system", "你是一个乐于助人的人力资源助理。请总是使用中文进行友好和专业的回答。"
            )
        )

        # 添加提醒功能说明
        reminder_instruction = str(get_value("humanrelation.reminder_instruction", ""))

        if retrieval_result.get("result") != "success":
            logger.info(f"  - 记忆检索未成功或无结果，跳过记忆上下文构建。检索结果: {retrieval_result}")
        else:
            persons = retrieval_result.get("persons", [])
            events = retrieval_result.get("events", [])
            suggestions = retrieval_result.get("suggestions", "")
            logger.info(f"  - 记忆检索成功，找到 {len(persons)} 个人物，{len(events)} 个事件")

            # 添加人物信息（长期记忆）
            if persons:
                person_texts = []

                # 优化：确保用户本人档案优先显示
                user_person = None
                other_persons = []

                for person in persons:
                    if person.get("is_user", False):
                        user_person = person
                    else:
                        other_persons.append(person)

                # 构建要显示的人物列表：用户本人 + 最多2个其他相关人物
                persons_to_show = []
                if user_person:
                    persons_to_show.append(user_person)
                persons_to_show.extend(other_persons[:2])  # 最多2个其他人物

                # 如果没有用户本人档案，则显示前3个相关人物
                if not user_person:
                    persons_to_show = persons[:3]

                for person in persons_to_show:
                    person_text = f"• {person.get('canonical_name', '')}: {person.get('profile_summary', '')}"
                    key_attrs = person.get("key_attributes", {})

                    # 处理key_attributes可能是字符串的情况
                    if isinstance(key_attrs, str):
                        try:
                            import json

                            key_attrs = json.loads(key_attrs)
                        except (json.JSONDecodeError, TypeError):
                            logger.warning(f"无法解析key_attributes: {key_attrs}")
                            key_attrs = {}

                    if key_attrs and isinstance(key_attrs, dict):
                        attrs_text = ", ".join(
                            [
                                f"{k}: {v}"
                                for k, v in key_attrs.items()
                                if not k.endswith("_更新时间") and not k.startswith("verification")
                            ]
                        )
                        if attrs_text:
                            person_text += f"\n  关键信息: {attrs_text}"
                    person_texts.append(person_text)

                if person_texts:
                    memory_context_parts.append("【相关人物档案】\n" + "\n".join(person_texts))

            # 添加相关事件（短期记忆）
            if events:
                event_texts = []
                for event in events[:3]:  # 最多显示3个相关事件
                    event_text = f"• {event.get('description_text', '')}"
                    if event.get("location"):
                        event_text += f"\n  地点: {event.get('location')}"
                    if event.get("sentiment"):
                        event_text += f"\n  情感: {event.get('sentiment')}"
                    event_texts.append(event_text)
                if event_texts:
                    memory_context_parts.append("【相关事件记忆】\n" + "\n".join(event_texts))

            # 添加对话建议
            if suggestions:
                memory_context_parts.append(f"【对话建议】\n{suggestions}")

            # 添加验证信息
            if memory_result.get("action_code") == "PROCESS_COMPLETE":
                persons_results = memory_result.get("payload", {}).get("persons", [])
                verification_messages = []
                for person_res in persons_results:
                    verification_msg = person_res.get("verification_message", "")
                    if verification_msg:
                        verification_messages.append(verification_msg)
                if verification_messages:
                    memory_context_parts.append("【需要确认】\n" + "\n".join(verification_messages))

        if memory_context_parts:
            context_parts.append("\n\n".join(memory_context_parts))
            logger.info(f"  ✅ 已添加 [记忆上下文]，共 {len(memory_context_parts)} 个部分")
        else:
            logger.info("  ⚠️ 记忆上下文为空，未添加到context_parts")
    except Exception as e:
        logger.error(f"构建记忆上下文失败: {str(e)}")

    # Part 2: News Context
    try:
        if news_result and news_result.get("result") == "success":
            news_list = news_result.get("news", [])
            if news_list:
                news_context_items = ["【相关最新资讯】"]
                suitable_news_count = 0

                for news in news_list:
                    # 检查AI判断结果，只展示适合的新闻
                    ai_judgment = news.get("ai_judgment", {})
                    is_suitable = ai_judgment.get("suitable", True)

                    if is_suitable:
                        title = news.get("title", "")
                        content = news.get("full_content", news.get("content", ""))
                        similarity = news.get("similarity", 0)

                        # 基本新闻信息
                        news_text = f"• 《{title}》(相关度: {similarity:.2f}): {content[:150]}..."

                        # 添加AI生成的关键词（如果有）
                        generated_keywords = news.get("generated_keywords", [])
                        if generated_keywords:
                            keywords_str = ", ".join(generated_keywords[:5])  # 最多显示5个关键词
                            news_text += f"\n  相关话题: {keywords_str}"

                        news_context_items.append(news_text)
                        suitable_news_count += 1
                    else:
                        logger.debug(f"跳过不适合的新闻: {news.get('title', '')[:50]}, 适合性: {is_suitable}")

                if suitable_news_count > 0:  # 确保有适合的新闻
                    context_parts.append("\n".join(news_context_items))
                    logger.info(f"  ✅ 已添加 [最新资讯]，共 {suitable_news_count} 条")
                else:
                    logger.info("  ⚠️ 没有找到适合的新闻内容")
    except Exception as e:
        logger.error(f"构建新闻上下文失败: {str(e)}")

    logger.info("--------------------")

    if not context_parts:
        return "没有检索到相关上下文信息。"
    return "\n\n---\n\n".join(context_parts)


# 1. 构建图
workflow = StateGraph(State)

# 2. 添加节点
workflow.add_node("memory_processor", process_memory_and_context)
workflow.add_node("agent", call_model)

workflow.add_node("tools", custom_tool_node)


# 【新增】自定义工具条件函数，避免断流问题
def custom_tools_condition(state: State):
    """
    自定义工具条件判断，确保对话不会意外断流
    """
    try:
        messages = state.get("messages", [])
        if not messages:
            logger.warning("🚨 custom_tools_condition: 消息列表为空，跳转到END")
            return END

        last_message = messages[-1]

        # 检查最后一条消息是否是AI消息
        if isinstance(last_message, AIMessage):
            # 检查是否有工具调用
            if hasattr(last_message, "tool_calls") and last_message.tool_calls:
                logger.info(f"🔧 检测到 {len(last_message.tool_calls)} 个工具调用，跳转到tools节点")
                return "tools"

            # 检查是否有内容
            if last_message.content and last_message.content.strip():
                logger.info(f"💬 AI响应包含内容（长度: {len(last_message.content)}），正常结束")
                return END

            # 【关键修复】如果既没有工具调用也没有内容，这是异常情况！
            logger.warning("🚨 AI响应既没有内容也没有工具调用，这可能导致断流！")

            # 检查是否是第一次调用后的空响应（通常表示应该有工具调用但没有）
            # 如果前面有HumanMessage但没有对应的ToolMessage，说明可能是第一次调用
            has_tool_messages = any(isinstance(msg, ToolMessage) for msg in messages)
            if not has_tool_messages:
                logger.error("❌ 第一次AI调用返回空内容且无工具调用，强制结束避免无限循环")
                return END

            # 如果已经有工具消息，说明是第二次调用返回空，这是真正的断流问题
            logger.error("❌ 工具调用后的AI响应为空，这是断流的根本原因！强制结束")
            return END

        elif isinstance(last_message, ToolMessage):
            # 注意：ToolMessage后会通过固定边自动回到agent，这里不应该出现
            logger.warning("⚠️ custom_tools_condition中检测到ToolMessage，这不应该发生（应该通过固定边处理）")
            return END

        else:
            logger.info(f"📝 最后一条消息类型: {type(last_message).__name__}，正常结束")
            return END

    except Exception as e:
        logger.error(f"❌ custom_tools_condition 执行异常: {e}")
        return END


# 3. 定义图的流程
workflow.set_entry_point("memory_processor")
workflow.add_edge("memory_processor", "agent")
workflow.add_conditional_edges("agent", custom_tools_condition, {"tools": "tools", END: END})
workflow.add_edge("tools", "agent")  # 工具执行后必须回到agent进行第二次模型调用

# 4. 设置记忆
memory = MySQLCheckpointSaver()

# 5. 编译图
graph = workflow.compile(checkpointer=memory)

# --- 流式响应生成器 ---


async def get_agent_response_stream(user_input: str, conversation_id: str, user_id: str):  # 流式响应生成器
    """异步生成器函数，供 FastAPI 调用"""

    # 🎯 预响应机制：立即发送确认信息
    import asyncio
    import json

    try:
        # 根据用户输入选择合适的预响应
        pre_responses = _get_contextual_pre_response(user_input)

        for i, pre_msg in enumerate(pre_responses):
            pre_response_data = json.dumps(
                {"type": "pre_response", "content": pre_msg, "stage": f"pre_{i + 1}"}, ensure_ascii=False
            )
            yield f"data: {pre_response_data}\n\n"

            # 短暂延迟，模拟思考过程
            if i < len(pre_responses) - 1:
                await asyncio.sleep(0.3)

        logger.info(f"🎯 [预响应] 已发送 {len(pre_responses)} 条预响应消息")

    except Exception as e:
        logger.error(f"🎯 [预响应] 预响应机制出错: {e}")
        # 发送一个简单的确认消息作为后备
        fallback_data = json.dumps(
            {"type": "pre_response", "content": "收到了，我正在处理...", "stage": "fallback"}, ensure_ascii=False
        )
        yield f"data: {fallback_data}\n\n"


    # 在对话开始时尝试保存档案快照（如果失败，会在checkpoint保存时补充）
    try:
        from utils.profile_diff import get_user_profile_snapshot
        start_profile = get_user_profile_snapshot(user_id)
        logger.info(f"获取到用户档案快照: user_id={user_id}, 快照大小={len(str(start_profile))}")

        if graph.checkpointer and hasattr(graph.checkpointer, 'save_conversation_snapshot'):
            logger.info(f"开始调用save_conversation_snapshot: user_id={user_id}, conversation_id={conversation_id}")
            graph.checkpointer.save_conversation_snapshot(user_id, conversation_id, start_profile)
            logger.info(f"已在对话开始时保存档案快照: user_id={user_id}, conversation_id={conversation_id}")
        else:
            logger.warning(f"checkpointer不可用，无法保存档案快照: checkpointer={graph.checkpointer}, has_method={hasattr(graph.checkpointer, 'save_conversation_snapshot') if graph.checkpointer else False}")

    except Exception as e:
        logger.error(f"保存对话开始时的档案快照失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")

    # 定义 LangGraph 的标准输入格式，直接将user_id和conversation_id注入state
    inputs = {"messages": [HumanMessage(content=user_input)], "user_id": user_id, "conversation_id": conversation_id}

    # 定义配置，将thread_id和user_id都包含在内
    config = {"configurable": {"thread_id": conversation_id, "user_id": user_id}}

    # 累积收到的内容，实现"叠加"输出
    accumulated = ""
    model_call_count = 0  # 【新增】跟踪模型调用次数

    # 【强化检查点清理】检查并清理可能的错误状态
    try:
        if graph.checkpointer:
            logger.info(f"🔍 检查检查点状态，thread_id: {config['configurable']['thread_id']}")
            # 获取当前检查点状态
            current_checkpoint = graph.checkpointer.get_tuple(config)
            logger.info(f"🔍 检查点获取结果: {current_checkpoint is not None}")
            if current_checkpoint and current_checkpoint.checkpoint:
                logger.info("🔍 发现检查点数据，开始验证...")
                # 检查是否包含错误状态
                checkpoint_data = current_checkpoint.checkpoint
                if isinstance(checkpoint_data, dict):
                    messages = checkpoint_data.get("channel_values", {}).get("messages", [])

                    # 【新增】检查是否包含未响应的tool_calls
                    has_invalid_tool_calls = False
                    logger.info(f"🔍 检查点包含 {len(messages)} 条消息")
                    if messages:
                        # 使用我们的验证函数检查
                        try:
                            validation_result = _final_validation(messages)
                            logger.info(f"🔍 检查点验证结果: {validation_result}")
                            if not validation_result:
                                has_invalid_tool_calls = True
                                logger.warning("⚠️ 检测到检查点包含未响应的tool_calls")
                        except Exception as e:
                            logger.warning(f"❌ 检查点验证出错: {e}")
                            has_invalid_tool_calls = True

                        # 【新增】额外检查：查找包含tool_calls但缺少响应的情况
                        for i, msg in enumerate(messages):
                            if hasattr(msg, "tool_calls") and msg.tool_calls:
                                for tool_call in msg.tool_calls:
                                    if hasattr(tool_call, "id"):
                                        tool_call_id = tool_call.id
                                        logger.debug(f"🔍 检查 tool_call: {tool_call_id}")

                                        # 【特殊处理】直接检查已知的问题tool_call_id
                                        known_problematic_ids = [
                                            "call_bnwxxHU5HKq3Xqp32gTrhdYx",
                                            "call_nsSdIgvJdnn8yysf6XJFZKCr",
                                            "call_M94H8jvFkb7ad86VpLS44ZND",
                                        ]
                                        if tool_call_id in known_problematic_ids:
                                            logger.warning(f"🚨 发现已知问题tool_call_id: {tool_call_id}")
                                            has_invalid_tool_calls = True
                                            break

                                        # 检查是否有对应的ToolMessage
                                        has_response = any(
                                            isinstance(m, ToolMessage)
                                            and hasattr(m, "tool_call_id")
                                            and m.tool_call_id == tool_call_id
                                            for m in messages[i + 1 :]
                                        )
                                        if not has_response:
                                            logger.warning(f"🚨 检测到未响应的tool_call: {tool_call_id}")
                                            has_invalid_tool_calls = True
                                            break

                    # 【强制清理】如果是已知问题的thread_id，强制清理
                    problematic_thread_ids = ["14", "19", "25"]
                    if conversation_id in problematic_thread_ids:
                        logger.warning(f"🚨 检测到问题thread_id='{conversation_id}'，强制重置")
                        has_invalid_tool_calls = True

                    if has_invalid_tool_calls:
                        logger.warning("⚠️ 检测到无效的检查点状态，强制重置对话")
                        # 重置为新的对话状态，不使用历史检查点
                        config = {
                            "configurable": {
                                "thread_id": f"{conversation_id}_clean_{int(time.time())}",
                                "user_id": user_id,
                            }
                        }
                        logger.info(f"✅ 已重置为新的thread_id: {config['configurable']['thread_id']}")
                    else:
                        logger.info("✅ 检查点状态正常，继续使用现有thread_id")
    except Exception as e:
        logger.warning(f"检查点状态验证失败，继续执行: {e}")

    try:
        # 使用 astream_events V2 接口
        logger.info("🚀 开始astream_events处理...")
        event_count = 0

        async for event in graph.astream_events(inputs, config=config, version="v2"):
            event_count += 1
            kind = event["event"]

            # 添加调试信息（仅显示前5个事件和重要事件）
            if event_count <= 5 or kind in ["on_chat_model_start", "on_chat_model_end"]:
                logger.info(f"📝 事件 #{event_count}: {kind}, 名称: {event.get('name', 'N/A')}")

            # logger.info(f"current event: {event}")  ##慎用，一大堆文字，会打印很多
            if kind == "on_chat_model_stream":
                try:
                    content = event["data"]["chunk"].content
                    # logger.debug(f"🔄 收到流式内容片段: '{content}' (长度: {len(content) if content else 0})")
                    if content:
                        accumulated += content
                        data = json.dumps({"content": accumulated, "type": "chat"}, ensure_ascii=False)
                        yield f"data: {data}\n\n"
                except (KeyError, AttributeError) as e:
                    logger.warning(f"处理聊天模型流事件时出错: {e}")
                    logger.warning(f"事件数据结构: {event.get('data', {})}")

            elif kind == "on_chat_model_start":
                model_call_count += 1  # 【新增】增加调用计数
                logger.info(f"🤖 AI模型开始调用 #{model_call_count}: {event.get('name', 'unknown')}")
                # 【新增】记录调用次数，帮助排查断流问题
                try:
                    run_id = event.get("run_id", "unknown")
                    logger.info(f"🔍 模型调用详情 - run_id: {run_id[:8]}...")
                except Exception as e:
                    logger.debug(f"获取run_id失败: {e}")

                # 【重要】如果是第二次调用，特别标注
                if model_call_count == 2:
                    logger.info("🔥 这是工具调用后的第二次模型调用！应该生成最终回答")

            elif kind == "on_chat_model_end":
                logger.info(f"✅ AI模型调用 #{model_call_count} 结束: {event.get('name', 'unknown')}")
                # 检查是否有输出
                try:
                    output = event.get("data", {}).get("output", {})
                    run_id = event.get("run_id", "unknown")
                    logger.info(f"🔍 模型调用结束 - run_id: {run_id[:8]}...")

                    if hasattr(output, "content"):
                        content_length = len(output.content) if output.content else 0
                        logger.info(f"📤 AI模型 #{model_call_count} 最终输出长度: {content_length}")
                        if content_length == 0:
                            if model_call_count == 1:
                                logger.info("🔧 第一次调用输出为空，可能包含工具调用（正常）")
                            else:
                                logger.warning("⚠️ 第二次调用输出为空！这可能导致断流")
                            # 检查是否有工具调用
                            if hasattr(output, "tool_calls") and output.tool_calls:
                                logger.info(f"🔧 检测到 {len(output.tool_calls)} 个工具调用")
                            else:
                                if model_call_count > 1:
                                    logger.error("❌ 第二次调用既没有输出内容也没有工具调用！这是断流的根本原因")
                        else:
                            logger.info(f"💬 模型 #{model_call_count} 成功输出 {content_length} 字符的内容")
                    else:
                        logger.warning(f"⚠️ AI模型输出格式异常: {type(output)}")
                except Exception as e:
                    logger.warning(f"⚠️ 检查AI模型输出时出错: {e}")

            elif kind == "on_tool_start":
                try:
                    tool_name = event["name"]
                    tool_input = event["data"]["input"]
                    result = json.dumps(
                        {"tool_name": tool_name, "tool_input": tool_input, "status": "start"}, ensure_ascii=False
                    )
                    yield f"data: {result}\n\n"
                except (KeyError, AttributeError) as e:
                    logger.warning(f"处理工具开始事件时出错: {e}")

            elif kind == "on_tool_end":
                tool_name = event["name"]
                # 安全地获取工具输出，处理不同的输出格式
                try:
                    output_data = event["data"]["output"]
                    if hasattr(output_data, "content"):
                        tool_output = output_data.content
                    else:
                        tool_output = str(output_data)
                except (KeyError, AttributeError) as e:
                    logger.warning(f"获取工具输出时出错: {e}, event: {event}")
                    tool_output = "工具执行完成"

                result = json.dumps(
                    {"tool_name": tool_name, "tool_output": tool_output, "status": "end"}, ensure_ascii=False
                )
                yield f"data: {result}\n\n"

            # 添加新闻检索结果的日志
            elif kind == "on_chain_start" and event.get("name") == "memory_processor":
                # 记录新闻检索开始
                logger.info("🔍 开始进行新闻检索和记忆处理...")

            elif kind == "on_chain_end" and event.get("name") == "memory_processor":
                # 记录新闻检索完成
                logger.info("✅ 新闻检索和记忆处理完成")

            # 【新增】监控条件判断事件
            elif kind == "on_conditional_edge_start":
                edge_name = event.get("name", "unknown")
                logger.info(f"🔀 条件判断开始: {edge_name}")

            elif kind == "on_conditional_edge_end":
                edge_name = event.get("name", "unknown")
                next_node = event.get("data", {}).get("output", "unknown")
                logger.info(f"🔀 条件判断结束: {edge_name} -> {next_node}")

                # 特别关注custom_tools_condition的判断结果
                if "tools_condition" in edge_name.lower() or next_node in ["tools", "agent", "END"]:
                    logger.info(f"🎯 关键流程判断: 下一步 -> {next_node}")
                    if next_node == "END":
                        logger.warning("⚠️ 流程即将结束，检查是否为正常结束")
                    elif next_node == "agent" and model_call_count >= 1:
                        logger.info("🔥 准备进行第二次模型调用生成最终回答")

        logger.info(f"📊 总共处理了 {event_count} 个事件")
        if not accumulated:
            logger.error("❌ 没有收到任何AI流式内容！")
        else:
            logger.info(f"✅ 累积收到AI内容长度: {len(accumulated)}")

    except Exception as e:
        logger.error(f"流式响应处理过程中发生错误: {str(e)}")
        import traceback

        logger.error(f"详细错误信息: {traceback.format_exc()}")

        # 发送错误信息给客户端
        error_data = json.dumps(
            {"type": "error", "content": "抱歉，处理您的请求时遇到了问题，请稍后重试。", "error": str(e)},
            ensure_ascii=False,
        )
        yield f"data: {error_data}\n\n"
        return

    # 流式响应结束后，生成推荐问题
    try:
        from configs.lion_config import get_value
        from service.recommendation_service import RecommendationService

        # 检查是否启用推荐问题功能
        enable_recommendations_config = get_value("humanrelation.enable_recommendations", "true")
        enable_recommendations = str(enable_recommendations_config).lower() == "true"

        if enable_recommendations and accumulated:  # 确保有AI回答内容
            logger.info("流式响应结束，开始生成推荐问题...")
            recommendation_service = RecommendationService()

            recommended_questions = recommendation_service.generate_recommended_questions(
                user_input=user_input,
                ai_response=accumulated,
                user_id=user_id,
                conversation_context="",
                max_questions=3,
            )

            if recommended_questions:
                # 发送推荐问题
                recommendations_data = json.dumps(
                    {"type": "recommendations", "questions": recommended_questions}, ensure_ascii=False
                )
                yield f"data: {recommendations_data}\n\n"
                logger.info(f"流式接口成功生成 {len(recommended_questions)} 个推荐问题")
            else:
                logger.info("流式接口未生成推荐问题")
        else:
            if not enable_recommendations:
                logger.info("推荐问题功能已禁用")
            else:
                logger.info("没有AI回答内容，跳过推荐问题生成")

    except Exception as e:
        logger.error(f"流式接口生成推荐问题失败: {str(e)}")
        import traceback

        logger.error(f"详细错误信息: {traceback.format_exc()}")
        # 推荐问题生成失败不影响主要功能

    # 更新用户亲密度
    try:
        if accumulated:  # 确保有AI回答内容
            from service.intimacy_service import IntimacyService

            intimacy_service = IntimacyService()
            interaction_type = intimacy_service.analyze_interaction_type(user_input, accumulated)

            update_result = intimacy_service.update_intimacy(user_id, interaction_type)
            if update_result.get("result") == "success":
                logger.info(
                    f"亲密度更新成功: {update_result.get('old_score')} -> {update_result.get('new_score')} (+{update_result.get('increase')})"
                )

                # 追踪对话变化并保存
                try:
                    logger.info(f"开始追踪对话变化: user_id={user_id}, conversation_id={conversation_id}")

                    from utils.profile_diff import track_profile_changes
                    from service.ESmemory.es_event_service import get_events_by_conversation

                    # 获取对话开始时的档案快照（从graph的state中获取）
                    config = {"configurable": {"thread_id": conversation_id, "user_id": user_id}}
                    logger.info(f"获取state配置: {config}")

                    # 从数据库获取对话开始时的档案快照
                    start_profile = {}
                    if graph.checkpointer:
                        start_profile = graph.checkpointer.get_conversation_snapshot(user_id, conversation_id)
                        logger.info(f"从数据库获取的档案快照: {bool(start_profile)}")

                    # 如果获取失败，使用空快照（这样至少不会出错）
                    if not start_profile:
                        logger.warning("未找到对话开始时的档案快照，使用空快照")
                        start_profile = {}

                    # 追踪档案变化
                    logger.info(f"开始追踪档案变化: user_id={user_id}")
                    profile_changes = track_profile_changes(user_id, start_profile)
                    logger.info(f"档案变化结果: {profile_changes}")

                    # 获取本次对话中新增的事件ID（最近20秒内的事件）
                    event_ids = []
                    try:
                        from datetime import datetime, timedelta

                        # 获取当前时间前15秒的时间戳作为过滤条件
                        current_time = datetime.now()
                        time_threshold = current_time - timedelta(seconds=15)

                        event_index = get_value("humanrelation.event_index_name", "memory_event_store")
                        all_events = get_events_by_conversation(event_index, user_id, conversation_id)

                        # 过滤出最近15秒内创建的事件
                        recent_events = []
                        for event in all_events:
                            event_time_str = event.get("timestamp")
                            if event_time_str:
                                try:
                                    # 解析时间戳
                                    if isinstance(event_time_str, str):
                                        event_time = datetime.fromisoformat(event_time_str.replace('Z', '+00:00'))
                                    else:
                                        event_time = event_time_str

                                    # 转换为本地时间进行比较
                                    if event_time.tzinfo:
                                        event_time = event_time.replace(tzinfo=None)

                                    if event_time >= time_threshold:
                                        recent_events.append(event)
                                except Exception as parse_e:
                                    logger.warning(f"解析事件时间失败: {parse_e}")

                        event_ids = [event.get("event_id") for event in recent_events if event.get("event_id")]
                        logger.info(f"最近15秒内新增事件数量: {len(event_ids)}")

                    except Exception as e:
                        logger.error(f"获取最近事件失败: {str(e)}")
                        event_ids = []

                    # 事件详情将在API接口中根据event_ids获取
                    logger.info(f"本次对话新增事件数量: {len(event_ids)}")

                    # 保存对话变化到数据库
                    if graph.checkpointer:
                        logger.info("开始保存对话变化到数据库")
                        graph.checkpointer.save_conversation_changes(
                            user_id=user_id,
                            thread_id=conversation_id,
                            profile_updates=profile_changes.get("profile_updates", {}),
                            intimacy_change=update_result.get('increase', 0),
                            events_added=event_ids
                        )
                        logger.info(f"对话变化追踪完成: 档案更新={len(profile_changes.get('profile_updates', {}))}, 新增事件={len(event_ids)}, 亲密度+{update_result.get('increase', 0)}")
                    else:
                        logger.warning("graph.checkpointer 不存在，无法保存对话变化")

                except Exception as track_e:
                    logger.error(f"追踪对话变化失败: {str(track_e)}")
                    import traceback
                    logger.error(f"详细错误信息: {traceback.format_exc()}")

            else:
                logger.warning(f"亲密度更新失败: {update_result.get('reason')}")
    except Exception as e:
        logger.error(f"更新亲密度失败: {str(e)}")
        # 亲密度更新失败不影响主要功能
