# Python缓存
__pycache__/
*.py[cod]
*.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 测试和覆盖率
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日志
*.log

# 数据库
*.db
*.sqlite3

# 备份文件
*.backup
*_backup_*.json
migration_log_*.json

# 大数据文件
data/
*.csv
*.json.gz

# Git
.git/
.gitignore

# 文档
*.md
docs/

# 测试文件
test_*.py
*_test.py
tests/

# 临时文件
*.tmp
*.temp
.DS_Store
