# 天气AI模型配置说明

## 概述

天气功能现在支持单独配置专用的AI模型，用于生成个性化天气提醒。如果不配置，将使用系统默认的AI模型。

## 配置项说明

### 1. 天气专用模型配置

| 配置键 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `humanrelation.weather.ai.model` | 天气专用AI模型名称 | 使用通用AI模型 | `gpt-4o-mini` |
| `humanrelation.weather.ai.max_tokens` | 生成文本的最大token数 | `800` | `1000` |
| `humanrelation.weather.ai.temperature` | 生成文本的创造性程度 | `0.7` | `0.8` |

### 2. 通用AI配置（天气功能会继承）

| 配置键 | 说明 | 默认值 |
|--------|------|--------|
| `humanrelation.ai.base_url` | AI API基础URL | `https://aigc.sankuai.com/v1/openai/native/chat/completions` |
| `humanrelation.ai.token` | AI API访问令牌 | `1930188146638651430` |
| `humanrelation.ai.model` | 通用AI模型名称 | `anthropic.claude-sonnet-4` |

## 配置优先级

1. **天气专用配置优先**：如果配置了 `humanrelation.weather.ai.model`，则使用该模型
2. **通用配置兜底**：如果没有配置天气专用模型，则使用 `humanrelation.ai.model`
3. **硬编码默认值**：如果都没有配置，则使用 `gpt-4.1`

## 配置示例

### 场景1：使用不同模型生成天气提醒

```
# 通用AI使用Claude
humanrelation.ai.model = anthropic.claude-sonnet-4

# 天气功能使用GPT模型（更适合生成生活化的提醒）
humanrelation.weather.ai.model = gpt-4o-mini
humanrelation.weather.ai.temperature = 0.8
humanrelation.weather.ai.max_tokens = 1000
```

### 场景2：只使用通用配置

```
# 所有功能都使用同一个模型
humanrelation.ai.model = gpt-4o
```

### 场景3：针对不同场景优化

```
# 通用AI使用高性能模型
humanrelation.ai.model = anthropic.claude-sonnet-4

# 天气提醒使用成本更低的模型
humanrelation.weather.ai.model = gpt-4o-mini
humanrelation.weather.ai.temperature = 0.9  # 更有创造性的提醒
humanrelation.weather.ai.max_tokens = 600   # 适中的长度
```

## 模型选择建议

### 推荐模型对比

| 模型 | 优势 | 适用场景 | 成本 |
|------|------|----------|------|
| `anthropic.claude-sonnet-4` | 逻辑性强，准确度高 | 需要精确分析的场景 | 高 |
| `gpt-4o` | 平衡性好，通用性强 | 大多数场景 | 中高 |
| `gpt-4o-mini` | 成本低，速度快 | 简单的文本生成 | 低 |

### 天气提醒场景建议

- **个性化程度要求高**：使用 `gpt-4o` + `temperature: 0.8`
- **成本敏感**：使用 `gpt-4o-mini` + `temperature: 0.7`
- **准确性要求高**：使用 `anthropic.claude-sonnet-4` + `temperature: 0.6`

## 配置生效

配置修改后，重启应用即可生效。可以通过日志查看实际使用的模型：

```
调用天气专用AI生成提醒，模型: gpt-4o-mini, max_tokens: 800, temperature: 0.7
```

## 测试验证

配置完成后，可以通过以下方式测试：

1. **API调用测试**：
   ```bash
   curl -X POST "http://localhost:8000/humanrelation/weather/person" \
     -H "Content-Type: application/json" \
     -d '{"user_id": "test_user", "person_id": "test_person"}'
   ```

2. **日志验证**：查看应用日志中的模型使用信息

3. **效果对比**：对比不同模型生成的天气提醒质量

## 注意事项

1. **模型兼容性**：确保配置的模型名称在您的AI服务中可用
2. **token限制**：不同模型的token限制不同，注意调整 `max_tokens`
3. **成本控制**：高性能模型成本较高，根据实际需求选择
4. **响应时间**：不同模型的响应时间可能不同
5. **内容质量**：建议测试不同模型的生成效果，选择最适合的

## 故障排查

如果天气提醒生成失败，检查以下项目：

1. **模型名称**：确认配置的模型名称正确
2. **API配额**：确认AI服务有足够的调用配额
3. **网络连接**：确认能正常访问AI服务
4. **日志信息**：查看详细的错误日志

## 更新历史

- **v1.0**：支持天气专用模型配置
- **v1.1**：添加max_tokens和temperature配置
- **v1.2**：优化配置优先级和兜底机制
