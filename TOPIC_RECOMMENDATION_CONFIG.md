# 话题推荐功能 Lion 配置项

## 概述
话题推荐功能需要在Lion配置中心添加以下配置项，用于控制话题推荐的生成策略和行为。

## 必需配置项

### 1. 话题推荐AI提示词
**配置键**: `humanrelation.topic_recommendation_prompt`
**说明**: 用于生成话题推荐的AI提示词
**默认值**: 见服务代码中的默认提示词

**当前默认提示词内容**:
```
你是一个智能生活助手，为用户推荐有趣的聊天话题。根据用户档案和近期活动，生成简洁、个性化的话题建议。

话题要求：
1. 简洁明了，一句话表达完整
2. 贴近生活，容易引起共鸣
3. 有时效性，符合当前季节/时间
4. 涵盖娱乐、美食、旅行、工作、生活等多个方面
5. 适合年轻人的兴趣和表达习惯
6. 避免过于深奥或私人的话题
7. 生活化、个性化，有趣味性

话题分类优先级：
- food（美食）：餐厅、菜品、饮品等 - 优先级4-5
- hobby（兴趣）：运动、阅读、学习等 - 优先级4-5
- life（生活）：日常、健康、兴趣等 - 优先级4-5
- entertainment（娱乐）：电影、音乐、综艺等 - 优先级3-4
- travel（旅行）：景点、出行、度假等 - 优先级3-4
- work（工作）：职场、项目、技能等 - 优先级3

请返回JSON数组格式，每个话题包含：
- topic: 话题内容（简洁有趣的一句话）
- category: 分类标签
- description: 简短说明
- priority: 优先级（1-5）

示例：
[
  {
    "topic": "你平时都喜欢吃什么？",
    "category": "food",
    "description": "美食分享话题",
    "priority": 4
  },
  {
    "topic": "最近有在学什么新技能吗？",
    "category": "hobby",
    "description": "兴趣学习话题",
    "priority": 4
  },
  {
    "topic": "最近有什么有趣的事情吗？",
    "category": "life",
    "description": "生活分享话题",
    "priority": 4
  }
]

用户信息：
- user_profile: 用户档案
- recent_events: 近期事件
- reminders: 提醒事项
- current_time: 当前时间
```

### 2. 话题推荐使用的AI模型
**配置键**: `humanrelation.memory_extraction_model`
**默认值**: `gpt-4o-mini`
**说明**: 话题推荐功能复用记忆提取的AI模型配置

## 可选配置项

### 3. 事件索引名称
**配置键**: `humanrelation.event_index_name`
**默认值**: `memory_event_store`
**说明**: 用于获取用户事件信息的ElasticSearch索引名称

## API 接口说明

### 话题推荐接口
**路径**: `POST /humanrelation/recommend_topics`

**请求参数**:
```json
{
  "user_id": "请求用户ID（必需，用于权限验证）",
  "person_id": "目标人员ID（必需，为谁生成话题推荐）",
  "context": "额外上下文信息（可选，默认空字符串）",
  "max_topics": 5,
  "fast_mode": false
}
```

**参数说明**:
- `user_id`: 必填，请求用户ID字符串，用于权限验证
- `person_id`: 必填，目标人员ID字符串，为谁生成话题推荐
- `context`: 可选，额外的上下文信息，如"准备和朋友聚餐"
- `max_topics`: 可选，最大推荐话题数量，默认5个，限制1-5个
- `fast_mode`: 可选，快速模式开关，默认false
  - `true`: 跳过AI调用，直接返回预设话题（响应极快，< 0.5秒）
  - `false`: 使用AI生成个性化话题（响应较慢，1-3秒）

**响应格式**:
```json
{
  "result": "success",
  "user_id": "test_user",
  "person_id": "person_123",
  "recommended_topics": [
    {
      "topic": "你平时都喜欢吃什么？",
      "category": "food",
      "description": "美食话题，人人都有话说",
      "priority": 4
    }
  ],
  "context_summary": {
    "cached": false,
    "fast_mode": false,
    "user_profile_available": true,
    "events_count": 5,
    "has_reminders": true
  },
  "generated_at": "2024-01-01 12:00:00"
}
```

**context_summary 字段说明**:
- `cached`: 是否使用了缓存结果
- `fast_mode`: 是否使用了快速模式
- `user_profile_available`: 用户档案是否可用
- `events_count`: 获取到的事件数量
- `has_reminders`: 是否有提醒事项

## 使用场景

### 1. 基础话题推荐
```bash
curl -X POST /humanrelation/recommend_topics \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user123"}'
```

### 2. 限制话题数量
```bash
curl -X POST /humanrelation/recommend_topics \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user123", "max_topics": 3}'
```

### 3. 带上下文的话题推荐
```bash
curl -X POST /humanrelation/recommend_topics \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "context": "准备参加公司聚会",
    "max_topics": 3
  }'
```

### 4. 快速模式（极速响应）
```bash
curl -X POST /humanrelation/recommend_topics \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "fast_mode": true,
    "max_topics": 3
  }'
```

## 功能特性

1. **智能个性化**: 基于用户档案和近期事件生成个性化话题
2. **生活化有趣**: 生成贴近生活、有趣味性的话题
3. **优先级排序**: AI会为话题分配优先级，重点推荐美食、兴趣、生活话题（4-5分）
4. **分类标签**: 每个话题都有分类标签，便于理解和筛选
5. **上下文感知**: 考虑时间、季节和用户背景等上下文因素
6. **备用机制**: 当AI生成失败时，提供备用话题保证功能可用
7. **🆕 缓存机制**: 相同请求5分钟内使用缓存，提升响应速度
8. **🆕 快速模式**: 支持极速响应，跳过AI调用直接返回预设话题
9. **🆕 并行查询**: 用户档案和事件信息并行获取，减少响应时间
10. **🆕 错误恢复**: 完善的错误处理和降级机制

## 性能特性

| 模式 | 响应时间 | 个性化程度 | 适用场景 |
|------|----------|------------|----------|
| 快速模式 | < 0.5秒 | 低 | 极速响应需求 |
| 缓存命中 | < 0.5秒 | 高 | 重复请求 |
| AI生成 | 1-3秒 | 高 | 个性化推荐 |

## 注意事项

1. 话题生成需要消耗AI模型token，建议合理设置`max_topics`参数
2. 功能依赖用户档案和事件数据，数据越完整推荐越准确
3. 建议在配置中心设置合适的AI提示词以符合业务需求
4. 接口支持并发调用，但需要注意AI服务的QPS限制
5. 🆕 缓存有效期为5分钟，频繁相同请求会自动使用缓存
6. 🆕 快速模式适合对响应时间要求极高的场景
7. 🆕 系统具备完善的降级机制，AI服务异常时自动返回备用话题

## 故障排查

### 接口响应慢？
1. 尝试使用快速模式：`"fast_mode": true`
2. 检查AI服务状态和网络连接
3. 查看日志中的"AI请求完成，耗时"信息

### AI生成失败？
1. 检查Lion配置 `humanrelation.memory_extraction_model`
2. 确认AI服务token有效性
3. 查看是否自动降级到备用话题

### 话题质量不佳？
1. 优化Lion配置 `humanrelation.topic_recommendation_prompt`
2. 确保用户档案数据完整
3. 提供更具体的context参数
