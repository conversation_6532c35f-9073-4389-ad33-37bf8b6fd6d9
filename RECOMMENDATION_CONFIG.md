# 推荐问题功能 Lion 配置项

## 概述
推荐问题功能需要在Lion配置中心添加以下配置项，用于控制推荐问题的生成策略和行为。

## 必需配置项

### 1. 推荐问题功能开关
**配置键**: `humanrelation.enable_recommendations`
**默认值**: `true`
**说明**: 控制是否启用推荐问题功能
**可选值**: `true` / `false`

### 2. 推荐问题生成提示词
**配置键**: `humanrelation.recommendation_prompt`
**默认值**: 见下方默认提示词
**说明**: 用于生成推荐问题的AI提示词

**默认提示词内容**:
```
你是一个智能的问题推荐助手。基于用户的问题、AI的回答以及相关的人物和事件记忆，生成3个相关且有价值的后续问题。

要求：
1. 推荐的问题应该与当前对话主题相关
2. 问题应该能够深入探讨或扩展当前话题
3. 利用提供的人物档案和事件记忆信息，生成个性化的问题
4. 问题应该自然、友好，符合中文表达习惯
5. 避免重复用户已经问过的问题

请以JSON数组格式返回推荐问题，例如：
["问题1？", "问题2？", "问题3？"]

输入数据包含：
- user_question: 用户的原始问题
- ai_answer: AI的回答
- conversation_context: 对话上下文
- related_persons: 相关人物信息
- related_events: 相关事件信息
- current_time: 当前时间
```

### 3. 推荐问题最大数量
**配置键**: `humanrelation.max_recommended_questions`
**默认值**: `3`
**说明**: 每次生成的推荐问题最大数量
**可选值**: 1-5 的整数

### 4. 推荐问题相似度阈值
**配置键**: `humanrelation.question_similarity_threshold`
**默认值**: `0.7`
**说明**: 过滤与原问题过于相似的推荐问题的阈值
**可选值**: 0.0-1.0 的浮点数

## 可选配置项

### 5. 推荐问题生成模型
**配置键**: `humanrelation.recommendation_model`
**默认值**: 使用 gpt-4.1 的值
**说明**: 用于生成推荐问题的AI模型，如果不配置则使用记忆提取模型

### 6. 推荐问题温度参数
**配置键**: `humanrelation.recommendation_temperature`
**默认值**: `0.7`
**说明**: 控制推荐问题生成的创造性，值越高越有创意
**可选值**: 0.0-1.0 的浮点数

### 7. 推荐问题最大Token数
**配置键**: `humanrelation.recommendation_max_tokens`
**默认值**: `800`
**说明**: 推荐问题生成时的最大Token数限制

## 配置示例

在Lion配置中心添加以下配置：

```json
{
  "humanrelation.enable_recommendations": "true",
  "humanrelation.max_recommended_questions": "3",
  "humanrelation.question_similarity_threshold": "0.7",
  "humanrelation.recommendation_temperature": "0.7",
  "humanrelation.recommendation_max_tokens": "800",
  "humanrelation.recommendation_prompt": "你是一个智能的问题推荐助手..."
}
```

## 功能说明

### 推荐问题生成流程
1. 用户提问并获得AI回答
2. 系统检索相关的人物档案和事件记忆
3. 构建包含对话上下文和记忆信息的推荐生成请求
4. 调用AI模型生成推荐问题
5. 过滤相似度过高的问题
6. 返回最终的推荐问题列表

### 推荐问题类型
- **深入探讨型**: 针对当前话题的进一步询问
- **关联扩展型**: 基于人物关系和事件记忆的相关问题
- **实用建议型**: 提供具体行动建议的问题
- **情感关怀型**: 关注人际关系和情感状态的问题

### 个性化特性
- 基于用户的人物档案生成个性化问题
- 利用历史事件记忆提供相关建议
- 考虑对话上下文和当前时间
- 避免重复和无关问题

## 注意事项

1. **性能考虑**: 推荐问题生成会增加响应时间，建议在生产环境中监控性能影响
2. **错误处理**: 推荐问题生成失败不会影响主要的聊天功能
3. **配置更新**: 配置项更新后会立即生效，无需重启服务
4. **兜底机制**: 当AI生成失败时，系统会提供基于关键词的兜底推荐问题

## 测试建议

1. 测试不同类型的用户问题（工作、生活、人际关系等）
2. 验证推荐问题的相关性和实用性
3. 检查推荐问题的去重和过滤效果
4. 测试配置项的动态更新功能
