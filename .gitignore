# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Flask specific
instance/
.webassets-cache

# PyCharm
.idea/

# VS Code
.vscode/

# Environments
.env
.env.local
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# Visual Studio
.vs/

# PyBuilder
target/

# logs
applogs/
data/applogs/
logs/
.logs/
*.log

# Local development
local_settings.py
*.sqlite3
*.db

# Coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Local development configuration
app/configs/local_config.py

# Local development files
file_upload_parallel.py
file_upload.py
test.py
example.md
kv_file.txt
test.txt
test2.txt
output-test.txt
output_test.txt
output.txt
output-test2.txt
output-test3.txt
output-test4.txt
output-test5.txt
output-test6.txt
output-test7.txt
output-test8.txt
output-test9.txt
output-test10.txt
insight_script.py
merchcants_data.json
test_img.py
img_format_test.py
error_log.txt
output_sql.txt
waiting_file.txt
test/
vex_data/
upload_log.txt
test_upload_log.txt
test_vex_data/
analysis_upload_log.txt
analysis_upload_log_test.txt
upload_log_test.txt
test_ana/
knowledge_v3/
test*

# Personal development environment scripts
setup-cursor-config.sh
python-dev-config/

# Project specific
humanrelation/

# News crawler state files (local state files, not the manager script)
app/service/ESmemory/current_batch_index.txt
app/service/ESmemory/keyword_suggestions.txt
# Note: crawler_state_manager.py should be included in the repo

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/
test/
data/

# Data migration and backup files
migration_log_*.json
person_memory_backup_*.json
*_migration.py
*_clear_*.py
debug_*.py
check_*.py
restore_*.py
verify_*.py
selective_*.py
admin_*.py
data_migration_strategy.md

# Temporary development files
完整版*.md
新的*.md

# Temporary scripts and reports
*_fix_report_*.json
relationship_conflict_fix_report_*.json
validate_*.py
app/simple_test_*.py
app/scripts/fix_*.py

# Additional temporary files
test*.py
test*.txt
debug_*.py
check_*.py
restore_*.py
verify_*.py
selective_*.py
admin_*.py
production_data_migration.py
*_migration.py
*_clear_*.py

# Temporary directories and files
~/
~
