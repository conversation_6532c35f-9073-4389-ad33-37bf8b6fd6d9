# 天气提醒功能增强说明

## 功能概述

基于用户需求，我们对天气查询功能进行了重大升级，新增了**个性化天气提醒**功能，能够结合用户信息和体感温度，利用大模型生成针对性的天气建议。

## 🆕 新增功能

### 1. 体感温度优先显示

- **优先使用体感温度**：天气信息优先显示体感温度，提供更准确的温度感受
- **温度对比显示**：当体感温度与实际气温不同时，同时显示两个数值
- **智能温度提醒**：基于体感温度生成更贴近实际感受的穿衣建议

### 2. 增强的天气数据获取

新增 `get_enhanced_weather_info_by_adcode()` 函数，获取更丰富的天气信息：

- 体感温度 (feelsLike)
- 湿度 (humidity)
- 气压 (pressure)
- 能见度 (visibility)
- 紫外线指数 (uvIndex)
- 空气质量指数 (aqi)

### 3. 个性化天气提醒

新增 `generate_personalized_weather_reminder()` 函数，基于以下信息生成个性化建议：

#### 用户档案信息

- 姓名、年龄、性别
- 职业信息（公司、职位、行业）
- 兴趣爱好
- 健康状况

#### 天气数据

- 体感温度和实际气温
- 天气状况（晴、雨、雪等）
- 湿度、风力、紫外线
- 空气质量

#### 今日活动安排 ⭐ 新增

- 今日提醒事项（时间、内容）
- 明日重要活动（作为参考）
- 活动相关人员信息

#### AI 生成的建议类型

1. **穿衣建议**：基于体感温度的精准穿衣指导
2. **出行建议**：考虑天气和风力的出行提醒
3. **健康提醒**：基于湿度、空气质量、紫外线的健康建议
4. **活动建议**：结合今日具体活动安排和天气条件 ⭐ 新增
5. **个性化建议**：结合用户职业、年龄、兴趣的专属建议

## 🔧 技术实现

### 核心函数

#### 1. `get_enhanced_weather_info_by_adcode()`

```python
def get_enhanced_weather_info_by_adcode(adcode: str, time_interval: int, time: str, destination: str):
    """获取增强的天气信息，包含更多详细数据用于个性化提醒"""
```

#### 2. `generate_personalized_weather_reminder()`

```python
def generate_personalized_weather_reminder(weather_data: dict, person_data: dict, city: str, location_type: str):
    """基于天气数据和用户信息生成个性化天气提醒"""
```

#### 3. `prepare_full_user_profile()` ⭐ 新增

```python
def prepare_full_user_profile(person_data: dict) -> str:
    """准备完整的用户档案信息，用于AI分析"""
```

#### 4. `get_user_today_activities()` ⭐ 新增

```python
def get_user_today_activities(user_id: str) -> dict:
    """获取用户今日的提醒事项和活动安排"""
```

#### 5. `prepare_activities_info()` ⭐ 新增

```python
def prepare_activities_info(today_activities: dict) -> str:
    """将今日活动信息格式化为文本，用于AI分析"""
```

#### 4. `extract_user_profile_for_weather()`

```python
def extract_user_profile_for_weather(person_data: dict) -> dict:
    """从person_data中提取用于天气提醒的用户信息（备用方案）"""
```

#### 5. `call_ai_for_weather_reminder()`

```python
def call_ai_for_weather_reminder(prompt: str) -> str:
    """调用AI生成个性化天气提醒"""
```

#### 6. `generate_basic_weather_reminder()`

```python
def generate_basic_weather_reminder(weather_data: dict, user_profile: dict, city: str) -> str:
    """生成基础的天气提醒（当AI调用失败时使用）"""
```

### AI 配置

使用 Lion 配置中心管理 AI API 配置：

- `humanrelation.ai.base_url`: AI API 地址
- `humanrelation.ai.token`: API 认证令牌
- `humanrelation.ai.model`: 使用的 AI 模型
- `humanrelation.weather.reminder_prompt`: 天气提醒的 prompt 模板 ⭐ 新增

### 容错机制

- **AI 调用失败**：自动回退到基础天气提醒
- **数据缺失**：智能处理缺失的天气或用户数据
- **异常处理**：完善的异常捕获和日志记录

## 📊 API 响应格式更新

### 新的响应结构

```json
{
  "result": "success",
  "locations": ["上海"],
  "weather_data": {
    "当前城市": {
      "city": "上海",
      "weather_info": "当前时间2025-07-18，上海的天气为多云，体感温度为12摄氏度（实际气温15°C），风向为北风，风速为3米/秒，属于3级风，湿度65%",
      "personalized_reminder": "张三，今日上海多云，体感12°C建议穿薄外套或卫衣。3级微风适宜出行，但空气质量一般(85)，建议减少户外跑步或选择室内运动。紫外线中等，外出可适当防护。湿度较高注意保持室内通风。作为程序员久坐一族，可利用温和天气适当散步活动筋骨。"
    }
  }
}
```

## 🧪 测试验证

已通过完整测试验证以下功能：

### 1. 用户档案信息提取

- ✅ 从标准化结构中提取基本信息
- ✅ 年龄计算（从生日推算）
- ✅ 职业信息组合
- ✅ 兴趣爱好提取

### 2. 天气数据处理

- ✅ 体感温度优先显示
- ✅ 温度差异对比显示
- ✅ 多维度天气信息提取

### 3. 个性化提醒生成

- ✅ AI 成功调用并生成个性化建议
- ✅ 基础提醒作为备选方案
- ✅ 不同天气条件下的适应性

### 4. 实际效果示例

#### 修正前（简化信息）

**用户信息**：张三，35 岁，男，软件工程师，爱好编程、跑步、阅读（122 字符）
**AI 提醒**：

> "张三，今日上海多云，体感 12°C 建议穿薄外套或卫衣。3 级微风适宜出行，但空气质量一般(85)，建议减少户外跑步或选择室内运动。紫外线中等，外出可适当防护。湿度较高注意保持室内通风。作为程序员久坐一族，可利用温和天气适当散步活动筋骨。"

#### 修正后（完整档案）

**用户信息**：张三（三三），37 岁，男，高级软件工程师，已婚，5 岁女儿，爱好编程、跑步、阅读，喜欢川菜，去过日本韩国泰国等（350 字符）
**AI 提醒**：

> "三三，上海今天多云，体感 12°C 有点凉，建议穿薄外套或卫衣。空气质量一般(85)，如果要晨跑建议选择公园等相对清洁的区域，或改为室内运动。湿度 65%偏高，记得多喝水。紫外线指数 5 属中等，户外时间长的话做好防晒。3 级微风适合带女儿到户外活动，周末可以考虑去公园走走。"

#### 个性化提升对比

- ✅ **使用别名**："三三"而非正式姓名
- ✅ **家庭考虑**："带女儿到户外活动"
- ✅ **运动细化**：具体的跑步地点建议
- ✅ **信息丰富度**：187%提升（122→350 字符）

## 🎯 功能亮点

1. **体感温度优先**：更贴近用户实际感受
2. **个性化程度高**：结合职业、年龄、兴趣等多维度信息
3. **建议全面**：涵盖穿衣、出行、健康、运动等方面
4. **智能回退**：AI 失败时自动使用基础提醒
5. **数据兼容**：完美支持新旧数据格式

## 📈 使用场景

- **日常出行**：获取个性化的穿衣和出行建议
- **运动规划**：基于空气质量和天气的运动建议
- **健康关怀**：针对不同年龄和职业的健康提醒
- **工作安排**：考虑天气因素的工作日程建议

## 🆕 最新功能：活动感知天气提醒

### 功能说明

在原有个性化天气提醒基础上，新增了**今日活动感知**功能，AI 能够结合用户的具体活动安排，提供更精准的天气建议。

### 实际效果示例

#### 场景：雨天 + 多项活动安排

**用户**：张三（三三），软件工程师，已婚，5 岁女儿
**天气**：上海小雨，体感 20°C，湿度 70%，空气质量 95
**今日活动**：

- 09:00 与张三开会讨论项目进展
- 14:30 带女儿去公园玩
- 18:00 和朋友聚餐
- 明日 10:00 参加技术分享会

**AI 生成的活动感知提醒**：

> "三三，今天上海小雨体感 20°C，建议穿长袖衬衫加薄外套。
>
> **出行提醒**：记得带伞，风力较小不用担心。下午带女儿去公园时，选择有遮雨亭的区域或室内游乐场更合适。
>
> **健康建议**：湿度 70%较高，空气质量 95 偏差，不适合户外跑步，可考虑室内运动。紫外线较弱无需防晒。
>
> **活动建议**：
>
> - 上午开会建议备件干爽衣物
> - 下午亲子时间可准备小雨具，考虑商场儿童区"

### 智能匹配亮点

1. **时间精准**：AI 知道上午 9:00 有开会，下午 14:30 要带女儿去公园
2. **活动适应**：雨天 + 公园活动 → 建议室内游乐场或有遮雨亭的区域
3. **场景结合**：开会 + 潮湿天气 → 建议备干爽衣物
4. **关怀细致**：考虑到亲子活动的特殊需求（小雨具、商场儿童区）

### 技术优势

- **无缝集成**：与现有提醒系统完美结合
- **智能过滤**：只处理当日和次日的活跃提醒
- **隐私保护**：活动信息仅用于天气建议生成
- **容错设计**：无活动时自动回退到标准个性化提醒

这次升级大大提升了天气查询功能的实用性和个性化程度，为用户提供了更贴心、更智能的天气服务体验。
