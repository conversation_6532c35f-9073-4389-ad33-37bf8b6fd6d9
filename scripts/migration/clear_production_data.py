#!/usr/bin/env python3
"""
线上数据清空脚本
警告：此脚本会清空所有线上数据，请谨慎使用！
"""

import sys
import os
sys.path.append('app')

from my_mysql.sql_client import CLIENT
from service.ESmemory.es_memory_client import client as es_client
from configs.lion_config import get_value
from sqlalchemy import text
import json
from datetime import datetime

def backup_mysql_data():
    """备份MySQL数据到JSON文件"""
    print("🔄 开始备份MySQL数据...")
    
    backup_data = {
        "backup_time": datetime.now().isoformat(),
        "tables": {}
    }
    
    conn = CLIENT.connect()
    
    try:
        # 备份人员表
        persons = conn.execute(text("SELECT * FROM person_memory")).fetchall()
        backup_data["tables"]["person_memory"] = [dict(row._mapping) for row in persons]
        print(f"✅ 备份人员表: {len(persons)} 条记录")
        
        # 备份聊天记录表
        try:
            chats = conn.execute(text("SELECT * FROM chat_history")).fetchall()
            backup_data["tables"]["chat_history"] = [dict(row._mapping) for row in chats]
            print(f"✅ 备份聊天记录表: {len(chats)} 条记录")
        except Exception as e:
            print(f"⚠️ 聊天记录表备份失败: {e}")
            backup_data["tables"]["chat_history"] = []
        
        # 备份提醒表
        try:
            reminders = conn.execute(text("SELECT * FROM reminders")).fetchall()
            backup_data["tables"]["reminders"] = [dict(row._mapping) for row in reminders]
            print(f"✅ 备份提醒表: {len(reminders)} 条记录")
        except Exception as e:
            print(f"⚠️ 提醒表备份失败: {e}")
            backup_data["tables"]["reminders"] = []
            
    finally:
        conn.close()
    
    # 保存备份文件
    backup_filename = f"production_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(backup_filename, 'w', encoding='utf-8') as f:
        json.dump(backup_data, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"✅ MySQL数据备份完成: {backup_filename}")
    return backup_filename

def clear_mysql_data():
    """清空MySQL数据"""
    print("🔄 开始清空MySQL数据...")
    
    conn = CLIENT.connect()
    
    try:
        # 清空人员表
        result = conn.execute(text("DELETE FROM person_memory"))
        print(f"✅ 清空人员表: 删除 {result.rowcount} 条记录")
        
        # 清空聊天记录表
        try:
            result = conn.execute(text("DELETE FROM chat_history"))
            print(f"✅ 清空聊天记录表: 删除 {result.rowcount} 条记录")
        except Exception as e:
            print(f"⚠️ 清空聊天记录表失败: {e}")
        
        # 清空提醒表
        try:
            result = conn.execute(text("DELETE FROM reminders"))
            print(f"✅ 清空提醒表: 删除 {result.rowcount} 条记录")
        except Exception as e:
            print(f"⚠️ 清空提醒表失败: {e}")
        
        conn.commit()
        
    finally:
        conn.close()
    
    print("✅ MySQL数据清空完成")

def clear_elasticsearch_data():
    """清空ElasticSearch数据"""
    print("🔄 开始清空ElasticSearch数据...")
    
    try:
        # 获取事件索引名称
        event_index = get_value("humanrelation.event_index_name", "humanrelation_events")
        
        # 清空事件索引
        try:
            es_client.delete_by_query(
                index=event_index,
                body={"query": {"match_all": {}}}
            )
            print(f"✅ 清空事件索引: {event_index}")
        except Exception as e:
            print(f"⚠️ 清空事件索引失败: {e}")
        
        # 清空新闻索引（如果需要）
        news_index = get_value("humanrelation.news_index_name", "humanrelation_news")
        try:
            es_client.delete_by_query(
                index=news_index,
                body={"query": {"match_all": {}}}
            )
            print(f"✅ 清空新闻索引: {news_index}")
        except Exception as e:
            print(f"⚠️ 清空新闻索引失败: {e}")
            
    except Exception as e:
        print(f"⚠️ ElasticSearch清空失败: {e}")
    
    print("✅ ElasticSearch数据清空完成")

def main():
    print("🚨 警告：此脚本将清空所有线上数据！")
    print("📊 数据包括：")
    print("   - MySQL: 人员档案、聊天记录、提醒")
    print("   - ElasticSearch: 事件记忆、新闻数据")
    print()
    
    # 确认操作
    confirm = input("❓ 确认要继续吗？请输入 'YES' 确认: ")
    if confirm != "YES":
        print("❌ 操作已取消")
        return
    
    try:
        # 1. 备份数据
        backup_file = backup_mysql_data()
        
        # 2. 清空MySQL数据
        clear_mysql_data()
        
        # 3. 清空ElasticSearch数据
        clear_elasticsearch_data()
        
        print()
        print("🎉 数据清空完成！")
        print(f"📁 备份文件: {backup_file}")
        print("💡 现在可以部署新版本代码了")
        
    except Exception as e:
        print(f"❌ 清空过程中出现错误: {e}")
        print("🔧 请检查数据库连接和权限")

if __name__ == "__main__":
    main()
