#!/usr/bin/env python3
"""
用户数据迁移脚本
将旧格式的key_attributes数据迁移到标准化结构
"""

import os
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

import json
import re
from datetime import datetime
from typing import Any, Dict, List, Optional

from my_mysql.entity.person_table import person_memory
from my_mysql.sql_client import select_many, select_one, update
from service.user_profile_standardizer import user_profile_standardizer
from sqlalchemy import and_, select
from utils.logger import logger


class DataMigrator:
    """数据迁移器"""

    def __init__(self, dry_run=True):
        """
        初始化迁移器

        Args:
            dry_run: 是否为试运行模式（不实际修改数据）
        """
        self.dry_run = dry_run
        self.migration_log = []
        self.current_year = datetime.now().year

        # 字段映射规则
        self.field_mapping = {
            # 基本信息映射
            "性别": "基本信息.性别",
            "gender": "基本信息.性别",
            "年龄": "基本信息.生日",  # 需要转换
            "age": "基本信息.生日",  # 需要转换
            "生日": "基本信息.生日",
            "地址": "基本信息.当前城市",
            "居住地": "基本信息.当前城市",
            "电话": "基本信息.联系方式.电话",
            "手机号": "基本信息.联系方式.电话",
            "电话号": "基本信息.联系方式.电话",
            "电话号码": "基本信息.联系方式.电话",
            "phone": "基本信息.联系方式.电话",
            # 职业信息映射
            "职业": "基本信息.职业信息.职位",
            # 兴趣偏好映射
            "爱好": "兴趣",
            "兴趣爱好": "兴趣",
            "兴趣": "兴趣",
            "hobbies": "兴趣",
            "饮食偏好": "餐饮偏好",
            # 其他信息映射
            "愿望": "期望",
            # 描述性信息映射到过往历史
            "身份": "过往历史",
            "关系": "过往历史",
            "活动": "过往历史",
            "创建方式": "过往历史",
        }

    def log_action(self, action: str, person_id: str, details: str = ""):
        """记录迁移日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "person_id": person_id,
            "details": details,
            "dry_run": self.dry_run,
        }
        self.migration_log.append(log_entry)
        logger.info(f"[迁移日志] {action}: {person_id} - {details}")

    def convert_age_to_birth_year(self, age_str: str) -> str:
        """将年龄转换为出生年份"""
        try:
            age = int(re.findall(r"\d+", str(age_str))[0])
            birth_year = self.current_year - age
            return f"{birth_year}年"
        except:
            return str(age_str)  # 转换失败时返回原值

    def map_old_to_standard(self, old_attributes: Dict[str, Any]) -> Dict[str, Any]:
        """将旧格式属性映射到标准结构"""
        # 创建空的标准结构 - 使用深拷贝避免引用问题
        import copy

        standard_attrs = copy.deepcopy(user_profile_standardizer.STANDARD_STRUCTURE)

        # 收集描述性信息
        history_parts = []

        for old_field, value in old_attributes.items():
            if not value or str(value).strip() == "":
                continue

            # 跳过无意义的字段
            if old_field in ["1", "2"] or str(value) in ["1", ""]:
                continue

            # 查找映射规则
            if old_field in self.field_mapping:
                target_path = self.field_mapping[old_field]

                if target_path == "基本信息.生日" and old_field in ["年龄", "age"]:
                    # 年龄转换为出生年份
                    value = self.convert_age_to_birth_year(value)

                # 设置值到标准结构
                self._set_nested_value(standard_attrs, target_path, value)

                # 如果是描述性信息，也添加到历史中
                if target_path == "过往历史":
                    history_parts.append(f"{old_field}: {value}")
            else:
                # 未映射的字段添加到过往历史
                history_parts.append(f"{old_field}: {value}")

        # 合并过往历史
        if history_parts:
            existing_history = standard_attrs.get("过往历史", "")
            new_history = "; ".join(history_parts)
            if existing_history:
                standard_attrs["过往历史"] = f"{existing_history}; {new_history}"
            else:
                standard_attrs["过往历史"] = new_history

        return standard_attrs

    def _set_nested_value(self, data: Dict[str, Any], path: str, value: Any):
        """设置嵌套字典中的值"""
        try:
            if path == "基本信息.性别":
                data["基本信息"]["性别"] = str(value)
            elif path == "基本信息.生日":
                data["基本信息"]["生日"] = str(value)
            elif path == "基本信息.当前城市":
                data["基本信息"]["当前城市"] = str(value)
            elif path == "基本信息.联系方式.电话":
                data["基本信息"]["联系方式"]["电话"] = str(value)
            elif path == "基本信息.职业信息.职位":
                data["基本信息"]["职业信息"]["职位"] = str(value)
            elif path == "兴趣":
                data["兴趣"] = str(value)
            elif path == "餐饮偏好":
                data["餐饮偏好"] = str(value)
            elif path == "期望":
                data["期望"] = str(value)
            elif path == "过往历史":
                # 过往历史在上层处理
                pass
        except Exception as e:
            logger.warning(f"设置嵌套值失败: {path} = {value}, 错误: {e}")

    def should_migrate_record(self, person_data: Dict[str, Any]) -> bool:
        """判断是否应该迁移该记录"""
        try:
            key_attributes_raw = person_data["key_attributes"]

            # 处理双重JSON编码的情况
            if isinstance(key_attributes_raw, str):
                try:
                    # 尝试解析JSON字符串
                    key_attributes = json.loads(key_attributes_raw)

                    # 如果解析后还是字符串，再次解析
                    if isinstance(key_attributes, str):
                        key_attributes = json.loads(key_attributes)
                except:
                    return False
            else:
                key_attributes = key_attributes_raw

            # 如果已经是标准结构，跳过
            if "基本信息" in key_attributes:
                return False

            # 如果是系统自动创建的用户，可以选择跳过（让系统重新创建）
            if key_attributes.get("创建方式") == "系统自动创建":
                # 可以根据需要决定是否迁移系统用户
                return True  # 这里选择迁移，也可以改为False跳过

            return True

        except:
            return False

    def migrate_single_record(self, person_data: Dict[str, Any]) -> bool:
        """迁移单条记录"""
        person_id = person_data["person_id"]
        canonical_name = person_data["canonical_name"]

        try:
            # 解析旧属性，处理双重JSON编码
            key_attributes_raw = person_data["key_attributes"]
            if isinstance(key_attributes_raw, str):
                try:
                    old_attributes = json.loads(key_attributes_raw)
                    # 如果解析后还是字符串，再次解析
                    if isinstance(old_attributes, str):
                        old_attributes = json.loads(old_attributes)
                except:
                    old_attributes = {}
            else:
                old_attributes = key_attributes_raw

            self.log_action("开始迁移", person_id, f"用户: {canonical_name}")

            # 转换为标准结构
            standard_attributes = self.map_old_to_standard(old_attributes)

            # 直接使用转换后的标准结构（已经是正确格式）
            validated_attributes = standard_attributes

            if not self.dry_run:
                # 更新数据库
                stmt = (
                    person_memory.update()
                    .where(person_memory.c.person_id == person_id)
                    .values(key_attributes=json.dumps(validated_attributes, ensure_ascii=False))
                )

                result = update(stmt)
                if result.rowcount > 0:
                    self.log_action("迁移成功", person_id, f"已更新标准结构")
                    return True
                else:
                    self.log_action("迁移失败", person_id, f"数据库更新失败")
                    return False
            else:
                self.log_action("试运行", person_id, f"将转换为标准结构（未实际更新）")
                return True

        except Exception as e:
            self.log_action("迁移异常", person_id, f"错误: {e}")
            return False

    def get_migration_candidates(self, limit: int = None) -> List[Dict[str, Any]]:
        """获取需要迁移的记录"""
        stmt = (
            select(
                person_memory.c.person_id,
                person_memory.c.canonical_name,
                person_memory.c.key_attributes,
                person_memory.c.user_id,
            )
            .where(person_memory.c.key_attributes.isnot(None))
            .where(person_memory.c.key_attributes != "")
            .where(person_memory.c.key_attributes != "{}")
            .order_by(person_memory.c.updated_at.desc())
        )

        if limit:
            stmt = stmt.limit(limit)

        results = select_many(stmt)

        # 过滤需要迁移的记录
        candidates = []
        for row in results:
            person_data = dict(row._mapping)
            if self.should_migrate_record(person_data):
                candidates.append(person_data)

        return candidates

    def run_migration(self, batch_size: int = 20, max_records: int = None):
        """运行迁移"""
        print(f"=== 开始数据迁移 ===")
        print(f"模式: {'试运行' if self.dry_run else '实际迁移'}")
        print(f"批次大小: {batch_size}")

        # 获取迁移候选
        candidates = self.get_migration_candidates(max_records)
        print(f"找到 {len(candidates)} 条需要迁移的记录")

        if len(candidates) == 0:
            print("没有需要迁移的数据")
            return

        # 分批处理
        success_count = 0
        failed_count = 0

        for i in range(0, len(candidates), batch_size):
            batch = candidates[i : i + batch_size]
            print(f"\n处理批次 {i//batch_size + 1}: 记录 {i+1}-{min(i+batch_size, len(candidates))}")

            batch_success = 0
            for person_data in batch:
                if self.migrate_single_record(person_data):
                    success_count += 1
                    batch_success += 1
                else:
                    failed_count += 1

            print(f"批次结果: 成功 {batch_success}/{len(batch)}")

            # 批次间暂停（可选）
            if not self.dry_run and i + batch_size < len(candidates):
                try:
                    input("按回车键继续下一批次...")
                except EOFError:
                    # 非交互模式下自动继续
                    print("非交互模式，自动继续下一批次...")

        print(f"\n=== 迁移完成 ===")
        print(f"总计: {len(candidates)} 条记录")
        print(f"成功: {success_count} 条")
        print(f"失败: {failed_count} 条")
        print(f"成功率: {(success_count/len(candidates)*100):.1f}%")

        # 保存迁移日志
        log_filename = f"migration_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(log_filename, "w", encoding="utf-8") as f:
            json.dump(self.migration_log, f, ensure_ascii=False, indent=2)
        print(f"迁移日志已保存到: {log_filename}")


def create_backup():
    """创建数据备份"""
    print("=== 创建数据备份 ===")

    try:
        # 查询所有需要备份的数据
        stmt = (
            select(person_memory)
            .where(person_memory.c.key_attributes.isnot(None))
            .where(person_memory.c.key_attributes != "")
            .where(person_memory.c.key_attributes != "{}")
        )

        results = select_many(stmt)

        # 创建备份文件
        backup_filename = f"person_memory_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        backup_data = []

        for row in results:
            person_data = dict(row._mapping)
            # 转换datetime对象为字符串
            for key, value in person_data.items():
                if isinstance(value, datetime):
                    person_data[key] = value.isoformat()
            backup_data.append(person_data)

        with open(backup_filename, "w", encoding="utf-8") as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)

        print(f"备份完成: {backup_filename}")
        print(f"备份记录数: {len(backup_data)}")
        return backup_filename

    except Exception as e:
        print(f"备份失败: {e}")
        return None


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="用户数据迁移脚本")
    parser.add_argument("--dry-run", action="store_true", default=True, help="试运行模式（默认）")
    parser.add_argument("--execute", action="store_true", help="实际执行迁移（危险操作）")
    parser.add_argument("--batch-size", type=int, default=20, help="批次大小（默认20）")
    parser.add_argument("--max-records", type=int, help="最大迁移记录数（用于测试）")
    parser.add_argument("--backup", action="store_true", help="仅创建备份，不执行迁移")

    args = parser.parse_args()

    # 如果只是备份
    if args.backup:
        create_backup()
        return

    # 确定运行模式
    dry_run = not args.execute

    if not dry_run:
        print("⚠️  警告：您即将执行实际的数据迁移操作！")
        print("这将修改生产数据，请确保已经做好备份！")

        # 询问是否需要先备份
        backup_confirm = input("是否需要先创建备份？(y/N): ")
        if backup_confirm.lower() in ["y", "yes"]:
            backup_file = create_backup()
            if not backup_file:
                print("备份失败，迁移终止")
                return

        confirm = input("请输入 'YES' 确认继续迁移: ")
        if confirm != "YES":
            print("操作已取消")
            return

    # 创建迁移器并运行
    migrator = DataMigrator(dry_run=dry_run)
    migrator.run_migration(batch_size=args.batch_size, max_records=args.max_records)


if __name__ == "__main__":
    main()
